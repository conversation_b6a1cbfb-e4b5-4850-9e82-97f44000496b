version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chainops-postgres-staging
    environment:
      POSTGRES_DB: chainops_staging
      POSTGRES_USER: chainops
      POSTGRES_PASSWORD: ${DB_PASSWORD:-chainops_staging_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - chainops-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chainops -d chainops_staging"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: chainops-redis-staging
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-chainops_redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - chainops-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # ChainOps Application
  chainops:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: chainops-app-staging
    environment:
      # Server Configuration
      CHAINOPS_SERVER_HOST: 0.0.0.0
      CHAINOPS_SERVER_PORT: 8080
      
      # Database Configuration
      CHAINOPS_DATABASE_HOST: postgres
      CHAINOPS_DATABASE_PORT: 5432
      CHAINOPS_DATABASE_USER: chainops
      CHAINOPS_DATABASE_PASSWORD: ${DB_PASSWORD:-chainops_staging_password}
      CHAINOPS_DATABASE_NAME: chainops_staging
      CHAINOPS_DATABASE_SSL_MODE: disable
      
      # Redis Configuration
      CHAINOPS_REDIS_HOST: redis
      CHAINOPS_REDIS_PORT: 6379
      CHAINOPS_REDIS_PASSWORD: ${REDIS_PASSWORD:-chainops_redis_password}
      
      # JWT Configuration
      CHAINOPS_JWT_SECRET: ${JWT_SECRET:-staging-jwt-secret-change-in-production}
      CHAINOPS_JWT_EXPIRATION: 24h
      CHAINOPS_JWT_REFRESH_EXPIRATION: 168h
      
      # Logging Configuration
      CHAINOPS_LOG_LEVEL: info
      CHAINOPS_LOG_FORMAT: json
      CHAINOPS_LOG_OUTPUT: stdout
      
      # Pipeline Configuration
      CHAINOPS_PIPELINE_MAX_CONCURRENT: 10
      CHAINOPS_PIPELINE_DEFAULT_TIMEOUT: 1h
      CHAINOPS_PIPELINE_WORKSPACE_DIR: /app/data/workspaces
      CHAINOPS_PIPELINE_ARTIFACTS_DIR: /app/data/artifacts
      CHAINOPS_PIPELINE_LOGS_DIR: /app/data/logs
      
      # Storage Configuration
      CHAINOPS_STORAGE_TYPE: local
      CHAINOPS_STORAGE_LOCAL_BASE_PATH: /app/data/storage
      
      # Feature Flags
      CHAINOPS_FEATURE_BLUE_GREEN: true
      CHAINOPS_FEATURE_CANARY: true
      CHAINOPS_FEATURE_SECURITY_SCANNING: true
      CHAINOPS_FEATURE_VISUAL_EDITOR: true
      
      # Monitoring
      CHAINOPS_MONITORING_ENABLED: true
      CHAINOPS_PROMETHEUS_ENABLED: true
    volumes:
      - chainops_data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock  # For Docker-based runners
    ports:
      - "8080:8080"
    networks:
      - chainops-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: chainops-prometheus-staging
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - chainops-network
    restart: unless-stopped

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: chainops-grafana-staging
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    networks:
      - chainops-network
    depends_on:
      - prometheus
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: chainops-nginx-staging
    volumes:
      - ./config/nginx/staging.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - chainops-network
    depends_on:
      - chainops
      - grafana
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  chainops_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  chainops-network:
    driver: bridge
