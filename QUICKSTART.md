# ChainOps/PipelinerX Quick Start Guide

Welcome to ChainOps/PipelinerX - the next-generation CI/CD platform! This guide will help you get up and running quickly.

## 🚀 Quick Start

### Prerequisites

- Go 1.21+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+
- Git

### 1. Test the Implementation

```bash
# Run comprehensive tests
./scripts/test-implementation.sh all

# Or run specific test suites
./scripts/test-implementation.sh compile
./scripts/test-implementation.sh unit
./scripts/test-implementation.sh api
```

### 2. Deploy to Staging

```bash
# Deploy staging environment
./scripts/deploy-staging.sh deploy

# Check deployment status
./scripts/deploy-staging.sh status

# View logs
./scripts/deploy-staging.sh logs
```

### 3. Access the Platform

Once deployed, access these endpoints:

- **ChainOps API**: http://localhost:8080
- **Grafana Dashboard**: http://localhost:3000 (admin/admin)
- **Prometheus Metrics**: http://localhost:9090
- **Health Check**: http://localhost:8080/health

## 🧪 Testing Guide

### Run All Tests

```bash
# Complete test suite
./scripts/run-tests.sh all

# Individual test types
./scripts/run-tests.sh unit
./scripts/run-tests.sh integration
./scripts/run-tests.sh api
./scripts/run-tests.sh performance
```

### Test Coverage

```bash
# Generate coverage report
./scripts/run-tests.sh coverage

# View HTML coverage report
open coverage.html
```

## 🐳 Docker Deployment

### Local Development

```bash
# Build and run locally
docker build -t chainops:latest .
docker run -p 8080:8080 chainops:latest
```

### Staging Environment

```bash
# Deploy full staging stack
docker-compose -f docker-compose.staging.yml up -d

# Scale the application
docker-compose -f docker-compose.staging.yml up -d --scale chainops=3

# View logs
docker-compose -f docker-compose.staging.yml logs -f chainops
```

## 📋 API Usage Examples

### Create a Pipeline

```bash
curl -X POST http://localhost:8080/api/v1/pipelines \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "My First Pipeline",
    "repository": "https://github.com/user/repo",
    "yaml_content": "stages:\n  - name: build\n    jobs:\n      - name: compile\n        image: golang:1.21\n        script:\n          - go build ./..."
  }'
```

### Execute a Pipeline

```bash
curl -X POST http://localhost:8080/api/v1/pipelines/{pipeline-id}/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "trigger_type": "manual",
    "variables": {
      "ENVIRONMENT": "staging"
    }
  }'
```

### Create a Template

```bash
curl -X POST http://localhost:8080/api/v1/templates \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Node.js Template",
    "description": "Standard Node.js CI/CD pipeline",
    "category": "web",
    "content": "stages:\n  - name: build\n    jobs:\n      - name: install\n        image: node:{{.node_version}}\n        script:\n          - npm install",
    "variables": [
      {
        "name": "node_version",
        "description": "Node.js version",
        "default": "18",
        "required": true
      }
    ]
  }'
```

## 🔧 Configuration

### Environment Variables

```bash
# Database
export CHAINOPS_DATABASE_HOST=localhost
export CHAINOPS_DATABASE_PORT=5432
export CHAINOPS_DATABASE_USER=chainops
export CHAINOPS_DATABASE_PASSWORD=your_password
export CHAINOPS_DATABASE_NAME=chainops

# JWT
export CHAINOPS_JWT_SECRET=your_jwt_secret

# Redis
export CHAINOPS_REDIS_HOST=localhost
export CHAINOPS_REDIS_PORT=6379

# Features
export CHAINOPS_FEATURE_BLUE_GREEN=true
export CHAINOPS_FEATURE_SECURITY_SCANNING=true
```

### Configuration Files

- `config/test.yaml` - Test environment
- `config/staging.yaml` - Staging environment
- `config/production.yaml` - Production environment (create as needed)

## 🚀 Advanced Features

### Blue/Green Deployments

```yaml
# .chainops.yml
stages:
  - name: deploy
    jobs:
      - name: blue-green-deploy
        type: blue_green_deployment
        config:
          application_name: "my-app"
          strategy: "automatic"
          health_check_url: "/health"
          rollback_on_failure: true
```

### Security Scanning

```yaml
# .chainops.yml
stages:
  - name: security
    jobs:
      - name: container-scan
        type: security_scan
        config:
          scan_type: "container"
          target: "my-app:latest"
          fail_on_severity: "high"
      - name: dependency-scan
        type: security_scan
        config:
          scan_type: "dependency"
          target: "."
```

### Real-time Log Streaming

```javascript
// Connect to log stream via WebSocket
const ws = new WebSocket('ws://localhost:8080/api/v1/logs/stream');

ws.onmessage = function(event) {
  const logEntry = JSON.parse(event.data);
  console.log(`[${logEntry.timestamp}] ${logEntry.level}: ${logEntry.message}`);
};

// Filter logs
ws.send(JSON.stringify({
  filter: {
    levels: ["info", "error"],
    execution_id: "your-execution-id"
  }
}));
```

## 📊 Monitoring

### Prometheus Metrics

Access metrics at: http://localhost:9090

Key metrics:
- `chainops_pipeline_executions_total`
- `chainops_pipeline_duration_seconds`
- `chainops_api_requests_total`
- `chainops_active_connections`

### Grafana Dashboards

Access dashboards at: http://localhost:3000

Default credentials: admin/admin

Pre-configured dashboards:
- Pipeline Performance
- System Health
- API Metrics
- Security Scan Results

## 🛠️ Development

### Local Development Setup

```bash
# Install dependencies
go mod download

# Setup test database
./scripts/setup-test-db.sh

# Run migrations
go run cmd/migrate/main.go up

# Start development server
go run cmd/server/main.go --config=config/test.yaml
```

### Running Tests During Development

```bash
# Watch mode for tests
go test -v ./... -count=1

# Run specific test
go test -v ./internal/core/pipeline -run TestEngine_ExecutePipeline

# Benchmark tests
go test -bench=. ./...
```

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database status
   ./scripts/setup-test-db.sh
   
   # Verify connection
   psql -h localhost -U chainops_test -d chainops_test
   ```

2. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :8080
   
   # Kill process
   kill -9 <PID>
   ```

3. **Docker Build Failed**
   ```bash
   # Clean Docker cache
   docker system prune -a
   
   # Rebuild without cache
   docker build --no-cache -t chainops:latest .
   ```

### Logs and Debugging

```bash
# View application logs
./scripts/deploy-staging.sh logs

# Debug mode
export CHAINOPS_LOG_LEVEL=debug
go run cmd/server/main.go

# Database logs
docker-compose -f docker-compose.staging.yml logs postgres
```

## 📚 Next Steps

1. **Explore the API**: Check out the full API documentation
2. **Create Your First Pipeline**: Follow the pipeline creation guide
3. **Set Up Monitoring**: Configure alerts and dashboards
4. **Deploy to Production**: Use the production deployment guide
5. **Contribute**: Check out the contributing guidelines

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/chainops/chainops/issues)
- **Discussions**: [GitHub Discussions](https://github.com/chainops/chainops/discussions)

---

**Happy CI/CD with ChainOps/PipelinerX! 🚀**
