name: "Simple Local Pipeline"
description: "A simple pipeline for local testing without <PERSON><PERSON>"
timeout: "10m"

variables:
  APP_ENV: "development"
  BUILD_TARGET: "dist"

triggers:
  - on: ["push"]
    branches: ["main"]
  - on: ["manual"]

stages:
  - name: "Setup"
    steps:
      - name: "Check Environment"
        run: |
          echo "=== Environment Check ==="
          echo "Current directory: $(pwd)"
          echo "User: $(whoami)"
          echo "Date: $(date)"
          echo "Environment: ${APP_ENV}"

      - name: "Create Build Directory"
        run: |
          echo "Creating build directory..."
          mkdir -p ${BUILD_TARGET}
          echo "Build directory created: ${BUILD_TARGET}"

  - name: "Build"
    steps:
      - name: "Simulate Install Dependencies"
        run: |
          echo "=== Installing Dependencies ==="
          echo "Simulating npm install..."
          sleep 2
          echo "Dependencies installed successfully"

      - name: "Simulate Lint"
        run: |
          echo "=== Running Linter ==="
          echo "Checking code style..."
          sleep 1
          echo "✓ No linting errors found"

      - name: "Simulate Build"
        run: |
          echo "=== Building Application ==="
          echo "Compiling source code..."
          sleep 3
          echo "Creating build artifacts..."
          echo "Build completed successfully" > ${BUILD_TARGET}/build.log
          echo "✓ Build completed successfully"

  - name: "Test"
    steps:
      - name: "Unit Tests"
        run: |
          echo "=== Running Unit Tests ==="
          echo "Running test suite..."
          sleep 2
          echo "✓ All 25 tests passed"

      - name: "Integration Tests"
        run: |
          echo "=== Running Integration Tests ==="
          echo "Testing API endpoints..."
          sleep 2
          echo "✓ All integration tests passed"

  - name: "Package"
    steps:
      - name: "Create Artifacts"
        run: |
          echo "=== Creating Deployment Artifacts ==="
          echo "Packaging application..."
          tar -czf app-${BUILD_TARGET}.tar.gz ${BUILD_TARGET}/
          echo "✓ Artifacts created: app-${BUILD_TARGET}.tar.gz"

      - name: "Verify Artifacts"
        run: |
          echo "=== Verifying Artifacts ==="
          if [ -f "app-${BUILD_TARGET}.tar.gz" ]; then
            echo "✓ Artifact verification successful"
            ls -lh app-${BUILD_TARGET}.tar.gz
          else
            echo "✗ Artifact verification failed"
            exit 1
          fi

  - name: "Deploy"
    steps:
      - name: "Deploy to Local"
        run: |
          echo "=== Deploying to Local Environment ==="
          echo "Extracting artifacts..."
          tar -xzf app-${BUILD_TARGET}.tar.gz
          echo "✓ Deployment completed successfully"
          echo "Application available at: http://localhost:3000"
        environment:
          DEPLOY_ENV: "local"
          DEPLOY_URL: "http://localhost:3000"
