# ChainOps Local Testing Example

This example demonstrates how to test ChainOps pipelines locally before pushing them to your repository.

## 🚀 Quick Start

### 1. Install ChainOps CLI

```bash
# Download and install the ChainOps CLI
curl -sSL https://get.chainops.dev | bash

# Or build from source
go build -o chainops-local cmd/local/main.go
```

### 2. Initialize Local Configuration

```bash
# Create a default configuration file
chainops-local init

# This creates .chainops-local.yml with default settings
```

### 3. Test Your Pipeline

```bash
# Run the pipeline locally
chainops-local run

# Run with verbose output
chainops-local run --verbose

# Dry run to see what would be executed
chainops-local run --dry-run

# Run with custom configuration
chainops-local run --config .chainops-local.yml --pipeline .chainops.yml
```

## 📋 Available Commands

### `chainops-local run`
Execute the pipeline locally with full Docker support.

**Options:**
- `--config, -c`: Local runner configuration file (default: `.chainops-local.yml`)
- `--pipeline, -p`: Pipeline configuration file (default: `.chainops.yml`)
- `--workdir, -w`: Working directory (default: current directory)
- `--verbose, -v`: Enable verbose logging
- `--dry-run, -d`: Show what would be executed without running
- `--output, -o`: Output format (text, json, yaml)
- `--watch`: Watch for file changes and re-run

### `chainops-local validate`
Validate your pipeline configuration without running it.

```bash
chainops-local validate
chainops-local validate --pipeline custom-pipeline.yml
```

### `chainops-local list`
List all stages and steps in your pipeline.

```bash
chainops-local list
```

### `chainops-local clean`
Clean up local workspace and cache directories.

```bash
chainops-local clean
```

## 🐳 Docker Support

ChainOps Local supports running steps in Docker containers, just like in production:

```yaml
stages:
  - name: "Build"
    steps:
      - name: "Build with Node.js"
        uses: "node:18-alpine"
        run: |
          npm install
          npm run build
```

### Custom Images

You can use any Docker image:

```yaml
steps:
  - name: "Python Tests"
    uses: "python:3.9"
    run: |
      pip install -r requirements.txt
      pytest

  - name: "Go Build"
    uses: "golang:1.19"
    run: |
      go mod download
      go build ./...
```

## 🔧 Configuration

### Local Runner Configuration (`.chainops-local.yml`)

```yaml
# Workspace settings
workspace_dir: "./.chainops/workspace"
cache_dir: "./.chainops/cache"
default_image: "ubuntu:latest"

# Environment variables
environment:
  CI: "true"
  NODE_ENV: "development"

# Volume mounts
volumes:
  - source: "${PWD}"
    target: "/workspace"
    type: "bind"

# Feature flags
enable_docker: true
enable_secrets: false
timeout_duration: "30m"
```

### Pipeline Configuration (`.chainops.yml`)

```yaml
name: "My Pipeline"
description: "A sample CI/CD pipeline"

variables:
  NODE_VERSION: "18"
  BUILD_ENV: "production"

stages:
  - name: "Build"
    steps:
      - name: "Install Dependencies"
        run: npm ci
      
      - name: "Build Application"
        run: npm run build
        environment:
          NODE_ENV: "${BUILD_ENV}"
```

## 🎯 Best Practices

### 1. Use Environment Variables

```yaml
variables:
  NODE_VERSION: "18"
  DATABASE_URL: "postgres://localhost:5432/test"

steps:
  - name: "Setup Node"
    uses: "node:${NODE_VERSION}"
    run: node --version
```

### 2. Cache Dependencies

```yaml
# In .chainops-local.yml
cache:
  enabled: true
  paths:
    - "node_modules"
    - ".npm"
    - "vendor"
```

### 3. Mock External Services

```yaml
# In .chainops-local.yml
mock_services:
  - name: "slack"
    type: "webhook"
    url: "http://localhost:8080/mock/slack"
```

### 4. Skip Deployment Steps Locally

```yaml
# In .chainops-local.yml
local_settings:
  skip_steps:
    - "Deploy to Production"
    - "Send Notifications"
```

## 🔍 Debugging

### Verbose Output

```bash
chainops-local run --verbose
```

### Debug Mode

```yaml
# In .chainops-local.yml
dev_helpers:
  debug_mode: true
  show_timing: true
```

### View Logs

```bash
# JSON output for programmatic processing
chainops-local run --output json

# YAML output
chainops-local run --output yaml
```

## 📊 Examples

### Frontend Application

```yaml
name: "Frontend Build"
stages:
  - name: "Build"
    steps:
      - name: "Install Dependencies"
        uses: "node:18"
        run: npm ci
      
      - name: "Lint"
        run: npm run lint
      
      - name: "Test"
        run: npm run test
      
      - name: "Build"
        run: npm run build
```

### Backend API

```yaml
name: "Backend API"
stages:
  - name: "Test"
    steps:
      - name: "Unit Tests"
        uses: "golang:1.19"
        run: go test ./...
      
      - name: "Integration Tests"
        run: |
          docker-compose up -d postgres
          go test -tags=integration ./...
          docker-compose down
```

### Multi-Language Project

```yaml
name: "Full Stack Application"
stages:
  - name: "Frontend"
    steps:
      - name: "Build Frontend"
        uses: "node:18"
        run: |
          cd frontend
          npm ci
          npm run build
  
  - name: "Backend"
    steps:
      - name: "Build Backend"
        uses: "golang:1.19"
        run: |
          cd backend
          go build ./...
```

## 🚨 Troubleshooting

### Docker Issues

```bash
# Check Docker is running
docker info

# Pull required images manually
docker pull node:18
docker pull golang:1.19
```

### Permission Issues

```bash
# Fix workspace permissions
sudo chown -R $USER:$USER .chainops/
```

### Network Issues

```bash
# Use host network mode
# In .chainops-local.yml
network_mode: "host"
```

## 🔗 Integration with IDEs

### VS Code

Create `.vscode/tasks.json`:

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "ChainOps: Run Pipeline",
      "type": "shell",
      "command": "chainops-local",
      "args": ["run", "--verbose"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    }
  ]
}
```

### JetBrains IDEs

Create a run configuration:
- Program: `chainops-local`
- Arguments: `run --verbose`
- Working directory: `$ProjectFileDir$`

## 📚 Additional Resources

- [ChainOps Documentation](https://docs.chainops.dev)
- [Pipeline Configuration Reference](https://docs.chainops.dev/pipeline-config)
- [Local Testing Guide](https://docs.chainops.dev/local-testing)
- [Docker Integration](https://docs.chainops.dev/docker)

## 🤝 Contributing

Found an issue or want to contribute? Check out our [Contributing Guide](../../CONTRIBUTING.md).
