# ChainOps Local Runner Configuration
# This file configures how pipelines are executed locally

# Workspace configuration
workspace_dir: "./.chainops/workspace"
cache_dir: "./.chainops/cache"

# Default container image for steps that don't specify one
default_image: "node:18-alpine"

# Global environment variables
environment:
  CI: "true"
  CHAINOPS_LOCAL: "true"
  NODE_ENV: "development"
  NPM_CONFIG_CACHE: "/workspace/.npm"
  
# Volume mounts for local development
volumes:
  - source: "${PWD}"
    target: "/workspace"
    type: "bind"
  - source: "${HOME}/.npm"
    target: "/root/.npm"
    type: "bind"
  - source: "/var/run/docker.sock"
    target: "/var/run/docker.sock"
    type: "bind"

# Network configuration
network_mode: "bridge"

# Feature flags
enable_docker: true
enable_secrets: false

# Timeout for the entire pipeline
timeout_duration: "30m"

# Local testing specific settings
local_settings:
  # Skip certain steps in local mode
  skip_steps:
    - "Deploy to Production"
    - "Send Notifications"
  
  # Override certain environment variables for local testing
  environment_overrides:
    DEPLOY_ENV: "local"
    SKIP_DEPLOYMENT: "true"
    
  # Mock external services
  mock_services:
    - name: "slack"
      type: "webhook"
      url: "http://localhost:8080/mock/slack"
    - name: "email"
      type: "smtp"
      host: "localhost"
      port: 1025

# Resource limits for containers
resource_limits:
  memory: "1g"
  cpu: "1.0"
  
# Caching configuration
cache:
  enabled: true
  paths:
    - "node_modules"
    - ".npm"
    - "dist"
    - "coverage"
  
# Artifact storage
artifacts:
  enabled: true
  storage_path: "./.chainops/artifacts"
  retention_days: 7

# Logging configuration
logging:
  level: "info"
  format: "text"
  output: "console"
  
# Development helpers
dev_helpers:
  # Automatically install missing tools
  auto_install_tools: true
  
  # Show detailed timing information
  show_timing: true
  
  # Enable debug mode for troubleshooting
  debug_mode: false
  
  # Watch for file changes and re-run
  watch_mode: false
  watch_patterns:
    - "src/**/*"
    - "package.json"
    - ".chainops.yml"
