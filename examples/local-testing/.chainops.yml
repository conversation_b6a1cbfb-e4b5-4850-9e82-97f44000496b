name: "Example Local Pipeline"
description: "A sample pipeline for local testing demonstration"
timeout: "30m"

variables:
  NODE_VERSION: "18"
  APP_ENV: "development"
  BUILD_TARGET: "dist"

triggers:
  - push
  - pull_request
  - manual

stages:
  - name: "Setup"
    steps:
      - name: "Checkout Code"
        uses: "actions/checkout@v3"
        with:
          fetch-depth: 1

      - name: "Setup Node.js"
        uses: "actions/setup-node@v3"
        with:
          node-version: "${NODE_VERSION}"
          cache: "npm"

  - name: "Build"
    steps:
      - name: "Install Dependencies"
        run: |
          echo "Installing dependencies..."
          npm ci --prefer-offline --no-audit
          echo "Dependencies installed successfully"

      - name: "Lint Code"
        run: |
          echo "Running linter..."
          npm run lint
          echo "Linting completed"

      - name: "Build Application"
        run: |
          echo "Building application..."
          npm run build
          echo "Build completed successfully"
        environment:
          NODE_ENV: "production"

  - name: "Test"
    steps:
      - name: "Unit Tests"
        run: |
          echo "Running unit tests..."
          npm run test:unit
          echo "Unit tests completed"

      - name: "Integration Tests"
        run: |
          echo "Running integration tests..."
          npm run test:integration
          echo "Integration tests completed"

      - name: "Generate Coverage Report"
        run: |
          echo "Generating coverage report..."
          npm run test:coverage
          echo "Coverage report generated"

  - name: "Security"
    steps:
      - name: "Security Audit"
        run: |
          echo "Running security audit..."
          npm audit --audit-level=moderate
          echo "Security audit completed"

      - name: "Dependency Check"
        run: |
          echo "Checking for vulnerable dependencies..."
          npm run security:check
          echo "Dependency check completed"

  - name: "Package"
    steps:
      - name: "Create Artifacts"
        run: |
          echo "Creating deployment artifacts..."
          tar -czf app-${BUILD_TARGET}.tar.gz ${BUILD_TARGET}/
          echo "Artifacts created successfully"

      - name: "Upload Artifacts"
        uses: "actions/upload-artifact@v3"
        with:
          name: "build-artifacts"
          path: "app-${BUILD_TARGET}.tar.gz"

  - name: "Deploy"
    condition: "branch == 'main'"
    steps:
      - name: "Deploy to Staging"
        run: |
          echo "Deploying to staging environment..."
          echo "Deployment URL: https://staging.example.com"
          echo "Deployment completed successfully"
        environment:
          DEPLOY_ENV: "staging"
          DEPLOY_URL: "https://staging.example.com"

notifications:
  - type: "slack"
    webhook: "${SLACK_WEBHOOK_URL}"
    events: ["success", "failure"]
    
  - type: "email"
    recipients: ["<EMAIL>"]
    events: ["failure"]
