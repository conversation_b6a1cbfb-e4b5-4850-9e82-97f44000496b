package integration

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/chainops/chainops/api/rest"
	"github.com/chainops/chainops/internal/app"
	"github.com/chainops/chainops/pkg/types"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type PipelineIntegrationTestSuite struct {
	suite.Suite
	app    *app.App
	router *gin.Engine
	server *httptest.Server
}

func (suite *PipelineIntegrationTestSuite) SetupSuite() {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Initialize test app
	config := &app.Config{
		Database: app.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			User:     "chainops_test",
			Password: "password",
			Name:     "chainops_test",
			SSLMode:  "disable",
		},
		Server: app.ServerConfig{
			Port: 8080,
			Host: "localhost",
		},
		JWT: app.JWTConfig{
			Secret:     "test-secret-key",
			Expiration: 24 * time.Hour,
		},
	}

	var err error
	suite.app, err = app.New(config)
	suite.Require().NoError(err)

	// Setup router
	handler := rest.NewHandler(suite.app.Services)
	suite.router = handler.SetupRoutes()

	// Create test server
	suite.server = httptest.NewServer(suite.router)
}

func (suite *PipelineIntegrationTestSuite) TearDownSuite() {
	if suite.server != nil {
		suite.server.Close()
	}
	if suite.app != nil {
		suite.app.Close()
	}
}

func (suite *PipelineIntegrationTestSuite) TestCreatePipeline() {
	// Test data
	pipeline := map[string]interface{}{
		"name":         "Test Pipeline",
		"repository":   "https://github.com/test/repo",
		"yaml_content": "stages:\n  - name: test\n    jobs:\n      - name: test-job\n        image: alpine\n        script:\n          - echo 'test'",
		"is_active":    true,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(pipeline)
	suite.Require().NoError(err)

	// Create request
	req, err := http.NewRequest("POST", suite.server.URL+"/api/v1/pipelines", bytes.NewBuffer(jsonData))
	suite.Require().NoError(err)
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	client := &http.Client{}
	resp, err := client.Do(req)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	// Assert response
	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)

	// Parse response
	var response types.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.Require().NoError(err)

	assert.True(suite.T(), response.Success)
	assert.NotNil(suite.T(), response.Data)
}

func (suite *PipelineIntegrationTestSuite) TestListPipelines() {
	// Create request
	req, err := http.NewRequest("GET", suite.server.URL+"/api/v1/pipelines", nil)
	suite.Require().NoError(err)

	// Execute request
	client := &http.Client{}
	resp, err := client.Do(req)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	// Parse response
	var response types.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.Require().NoError(err)

	assert.True(suite.T(), response.Success)
	assert.NotNil(suite.T(), response.Data)
}

func (suite *PipelineIntegrationTestSuite) TestExecutePipeline() {
	// First create a pipeline
	pipeline := map[string]interface{}{
		"name":         "Execution Test Pipeline",
		"repository":   "https://github.com/test/repo",
		"yaml_content": "stages:\n  - name: test\n    jobs:\n      - name: test-job\n        image: alpine\n        script:\n          - echo 'test'",
		"is_active":    true,
	}

	jsonData, err := json.Marshal(pipeline)
	suite.Require().NoError(err)

	req, err := http.NewRequest("POST", suite.server.URL+"/api/v1/pipelines", bytes.NewBuffer(jsonData))
	suite.Require().NoError(err)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	var createResponse types.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&createResponse)
	suite.Require().NoError(err)

	// Extract pipeline ID from response
	pipelineData := createResponse.Data.(map[string]interface{})
	pipelineID := pipelineData["id"].(string)

	// Now execute the pipeline
	executeData := map[string]interface{}{
		"trigger_type": "manual",
		"variables": map[string]string{
			"ENVIRONMENT": "test",
		},
	}

	jsonData, err = json.Marshal(executeData)
	suite.Require().NoError(err)

	req, err = http.NewRequest("POST", suite.server.URL+"/api/v1/pipelines/"+pipelineID+"/execute", bytes.NewBuffer(jsonData))
	suite.Require().NoError(err)
	req.Header.Set("Content-Type", "application/json")

	resp, err = client.Do(req)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	var executeResponse types.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&executeResponse)
	suite.Require().NoError(err)

	assert.True(suite.T(), executeResponse.Success)
	assert.NotNil(suite.T(), executeResponse.Data)
}

func (suite *PipelineIntegrationTestSuite) TestTemplateWorkflow() {
	// Create a template
	template := map[string]interface{}{
		"name":        "Node.js Template",
		"description": "Standard Node.js CI/CD pipeline",
		"category":    "web",
		"content": `stages:
  - name: build
    jobs:
      - name: install
        image: node:{{.node_version}}
        script:
          - npm install
      - name: test
        image: node:{{.node_version}}
        script:
          - npm test`,
		"variables": []map[string]interface{}{
			{
				"name":        "node_version",
				"description": "Node.js version",
				"default":     "18",
				"required":    true,
			},
		},
		"is_public": true,
	}

	jsonData, err := json.Marshal(template)
	suite.Require().NoError(err)

	// Create template
	req, err := http.NewRequest("POST", suite.server.URL+"/api/v1/templates", bytes.NewBuffer(jsonData))
	suite.Require().NoError(err)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)

	var templateResponse types.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&templateResponse)
	suite.Require().NoError(err)

	templateData := templateResponse.Data.(map[string]interface{})
	templateID := templateData["id"].(string)

	// Use template to create pipeline
	instantiateData := map[string]interface{}{
		"name":       "My Node App",
		"repository": "https://github.com/user/node-app",
		"values": map[string]interface{}{
			"node_version": "20",
		},
	}

	jsonData, err = json.Marshal(instantiateData)
	suite.Require().NoError(err)

	req, err = http.NewRequest("POST", suite.server.URL+"/api/v1/templates/"+templateID+"/instantiate", bytes.NewBuffer(jsonData))
	suite.Require().NoError(err)
	req.Header.Set("Content-Type", "application/json")

	resp, err = client.Do(req)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)

	var instantiateResponse types.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&instantiateResponse)
	suite.Require().NoError(err)

	assert.True(suite.T(), instantiateResponse.Success)
	assert.NotNil(suite.T(), instantiateResponse.Data)
}

func TestPipelineIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(PipelineIntegrationTestSuite))
}
