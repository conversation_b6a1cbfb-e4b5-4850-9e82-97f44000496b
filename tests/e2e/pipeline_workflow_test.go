package e2e

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/chainops/chainops/pkg/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type PipelineWorkflowTestSuite struct {
	suite.Suite
	baseURL     string
	authToken   string
	client      *http.Client
	pipelineID  string
	executionID string
}

func (suite *PipelineWorkflowTestSuite) SetupSuite() {
	suite.baseURL = "http://localhost:8081"
	suite.client = &http.Client{Timeout: 30 * time.Second}
	
	// For now, we'll skip authentication in tests
	// In a real implementation, you'd authenticate here
	suite.authToken = ""
}

func (suite *PipelineWorkflowTestSuite) TestCompleteWorkflow() {
	// Test the complete pipeline workflow
	suite.Run("01_CreatePipeline", suite.testCreatePipeline)
	suite.Run("02_ListPipelines", suite.testListPipelines)
	suite.Run("03_GetPipeline", suite.testGetPipeline)
	suite.Run("04_ExecutePipeline", suite.testExecutePipeline)
	suite.Run("05_GetExecution", suite.testGetExecution)
	suite.Run("06_ListExecutions", suite.testListExecutions)
	suite.Run("07_UpdatePipeline", suite.testUpdatePipeline)
	suite.Run("08_DeletePipeline", suite.testDeletePipeline)
}

func (suite *PipelineWorkflowTestSuite) testCreatePipeline() {
	pipeline := map[string]interface{}{
		"name":         "E2E Test Pipeline",
		"repository":   "https://github.com/chainops/test-repo",
		"yaml_content": `
stages:
  - name: build
    jobs:
      - name: compile
        image: golang:1.21
        script:
          - echo "Building application..."
          - go build ./...
      - name: test
        image: golang:1.21
        script:
          - echo "Running tests..."
          - go test ./...
  - name: deploy
    jobs:
      - name: deploy-staging
        image: alpine
        script:
          - echo "Deploying to staging..."
        approval_required: true
`,
		"is_active": true,
	}

	response := suite.makeRequest("POST", "/api/v1/pipelines", pipeline)
	
	assert.Equal(suite.T(), http.StatusCreated, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
	
	// Extract pipeline ID for subsequent tests
	if data, ok := apiResponse.Data.(map[string]interface{}); ok {
		if id, ok := data["id"].(string); ok {
			suite.pipelineID = id
		}
	}
	
	suite.Require().NotEmpty(suite.pipelineID, "Pipeline ID should be returned")
}

func (suite *PipelineWorkflowTestSuite) testListPipelines() {
	response := suite.makeRequest("GET", "/api/v1/pipelines", nil)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
}

func (suite *PipelineWorkflowTestSuite) testGetPipeline() {
	response := suite.makeRequest("GET", fmt.Sprintf("/api/v1/pipelines/%s", suite.pipelineID), nil)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
	
	// Verify pipeline data
	if data, ok := apiResponse.Data.(map[string]interface{}); ok {
		assert.Equal(suite.T(), "E2E Test Pipeline", data["name"])
		assert.Equal(suite.T(), "https://github.com/chainops/test-repo", data["repository"])
		assert.Equal(suite.T(), true, data["is_active"])
	}
}

func (suite *PipelineWorkflowTestSuite) testExecutePipeline() {
	executeData := map[string]interface{}{
		"trigger_type": "manual",
		"variables": map[string]string{
			"ENVIRONMENT": "staging",
			"VERSION":     "1.0.0",
		},
	}

	response := suite.makeRequest("POST", fmt.Sprintf("/api/v1/pipelines/%s/execute", suite.pipelineID), executeData)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
	
	// Extract execution ID for subsequent tests
	if data, ok := apiResponse.Data.(map[string]interface{}); ok {
		if id, ok := data["id"].(string); ok {
			suite.executionID = id
		}
	}
	
	suite.Require().NotEmpty(suite.executionID, "Execution ID should be returned")
}

func (suite *PipelineWorkflowTestSuite) testGetExecution() {
	response := suite.makeRequest("GET", fmt.Sprintf("/api/v1/executions/%s", suite.executionID), nil)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
	
	// Verify execution data
	if data, ok := apiResponse.Data.(map[string]interface{}); ok {
		assert.Equal(suite.T(), suite.pipelineID, data["pipeline_id"])
		assert.Contains(suite.T(), []string{"pending", "running", "success", "failure"}, data["status"])
	}
}

func (suite *PipelineWorkflowTestSuite) testListExecutions() {
	response := suite.makeRequest("GET", "/api/v1/executions", nil)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
}

func (suite *PipelineWorkflowTestSuite) testUpdatePipeline() {
	updateData := map[string]interface{}{
		"name":        "Updated E2E Test Pipeline",
		"description": "Updated description for testing",
		"is_active":   true,
	}

	response := suite.makeRequest("PUT", fmt.Sprintf("/api/v1/pipelines/%s", suite.pipelineID), updateData)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
	
	// Verify the update
	if data, ok := apiResponse.Data.(map[string]interface{}); ok {
		assert.Equal(suite.T(), "Updated E2E Test Pipeline", data["name"])
		assert.Equal(suite.T(), "Updated description for testing", data["description"])
	}
}

func (suite *PipelineWorkflowTestSuite) testDeletePipeline() {
	response := suite.makeRequest("DELETE", fmt.Sprintf("/api/v1/pipelines/%s", suite.pipelineID), nil)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	
	// Verify the pipeline is deleted
	response = suite.makeRequest("GET", fmt.Sprintf("/api/v1/pipelines/%s", suite.pipelineID), nil)
	assert.Equal(suite.T(), http.StatusNotFound, response.StatusCode)
}

func (suite *PipelineWorkflowTestSuite) makeRequest(method, path string, body interface{}) *http.Response {
	var reqBody *bytes.Buffer
	
	if body != nil {
		jsonData, err := json.Marshal(body)
		suite.Require().NoError(err)
		reqBody = bytes.NewBuffer(jsonData)
	} else {
		reqBody = bytes.NewBuffer(nil)
	}
	
	req, err := http.NewRequest(method, suite.baseURL+path, reqBody)
	suite.Require().NoError(err)
	
	req.Header.Set("Content-Type", "application/json")
	if suite.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+suite.authToken)
	}
	
	resp, err := suite.client.Do(req)
	suite.Require().NoError(err)
	
	return resp
}

func TestPipelineWorkflowTestSuite(t *testing.T) {
	suite.Run(t, new(PipelineWorkflowTestSuite))
}

// Additional test suites for other workflows

type TemplateWorkflowTestSuite struct {
	suite.Suite
	baseURL    string
	authToken  string
	client     *http.Client
	templateID string
}

func (suite *TemplateWorkflowTestSuite) SetupSuite() {
	suite.baseURL = "http://localhost:8081"
	suite.client = &http.Client{Timeout: 30 * time.Second}
	suite.authToken = ""
}

func (suite *TemplateWorkflowTestSuite) TestTemplateWorkflow() {
	suite.Run("01_CreateTemplate", suite.testCreateTemplate)
	suite.Run("02_ListTemplates", suite.testListTemplates)
	suite.Run("03_GetTemplate", suite.testGetTemplate)
	suite.Run("04_InstantiateTemplate", suite.testInstantiateTemplate)
	suite.Run("05_UpdateTemplate", suite.testUpdateTemplate)
	suite.Run("06_DeleteTemplate", suite.testDeleteTemplate)
}

func (suite *TemplateWorkflowTestSuite) testCreateTemplate() {
	template := map[string]interface{}{
		"name":        "Node.js CI/CD Template",
		"description": "Standard Node.js CI/CD pipeline template",
		"category":    "web",
		"content": `stages:
  - name: build
    jobs:
      - name: install
        image: node:{{.node_version}}
        script:
          - npm ci
      - name: test
        image: node:{{.node_version}}
        script:
          - npm test
      - name: build
        image: node:{{.node_version}}
        script:
          - npm run build`,
		"variables": []map[string]interface{}{
			{
				"name":        "node_version",
				"description": "Node.js version to use",
				"default":     "18",
				"required":    true,
			},
		},
		"is_public": true,
	}

	response := suite.makeRequest("POST", "/api/v1/templates", template)
	
	assert.Equal(suite.T(), http.StatusCreated, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
	
	// Extract template ID
	if data, ok := apiResponse.Data.(map[string]interface{}); ok {
		if id, ok := data["id"].(string); ok {
			suite.templateID = id
		}
	}
	
	suite.Require().NotEmpty(suite.templateID, "Template ID should be returned")
}

func (suite *TemplateWorkflowTestSuite) testListTemplates() {
	response := suite.makeRequest("GET", "/api/v1/templates", nil)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
}

func (suite *TemplateWorkflowTestSuite) testGetTemplate() {
	response := suite.makeRequest("GET", fmt.Sprintf("/api/v1/templates/%s", suite.templateID), nil)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
}

func (suite *TemplateWorkflowTestSuite) testInstantiateTemplate() {
	instantiateData := map[string]interface{}{
		"name":       "My Node.js App",
		"repository": "https://github.com/user/node-app",
		"values": map[string]interface{}{
			"node_version": "20",
		},
	}

	response := suite.makeRequest("POST", fmt.Sprintf("/api/v1/templates/%s/instantiate", suite.templateID), instantiateData)
	
	assert.Equal(suite.T(), http.StatusCreated, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
	assert.NotNil(suite.T(), apiResponse.Data)
}

func (suite *TemplateWorkflowTestSuite) testUpdateTemplate() {
	updateData := map[string]interface{}{
		"name":        "Updated Node.js CI/CD Template",
		"description": "Updated Node.js CI/CD pipeline template",
	}

	response := suite.makeRequest("PUT", fmt.Sprintf("/api/v1/templates/%s", suite.templateID), updateData)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
}

func (suite *TemplateWorkflowTestSuite) testDeleteTemplate() {
	response := suite.makeRequest("DELETE", fmt.Sprintf("/api/v1/templates/%s", suite.templateID), nil)
	
	assert.Equal(suite.T(), http.StatusOK, response.StatusCode)
	
	var apiResponse types.APIResponse
	err := json.NewDecoder(response.Body).Decode(&apiResponse)
	suite.Require().NoError(err)
	
	assert.True(suite.T(), apiResponse.Success)
}

func (suite *TemplateWorkflowTestSuite) makeRequest(method, path string, body interface{}) *http.Response {
	var reqBody *bytes.Buffer
	
	if body != nil {
		jsonData, err := json.Marshal(body)
		suite.Require().NoError(err)
		reqBody = bytes.NewBuffer(jsonData)
	} else {
		reqBody = bytes.NewBuffer(nil)
	}
	
	req, err := http.NewRequest(method, suite.baseURL+path, reqBody)
	suite.Require().NoError(err)
	
	req.Header.Set("Content-Type", "application/json")
	if suite.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+suite.authToken)
	}
	
	resp, err := suite.client.Do(req)
	suite.Require().NoError(err)
	
	return resp
}

func TestTemplateWorkflowTestSuite(t *testing.T) {
	suite.Run(t, new(TemplateWorkflowTestSuite))
}
