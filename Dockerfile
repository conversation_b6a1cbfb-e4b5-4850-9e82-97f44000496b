# Multi-stage build for ChainOps

# Stage 1: Build the Go backend
FROM golang:1.21-alpine AS backend-builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application and migration tool
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o chainops ./cmd/server
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o migrate ./cmd/migrate

# Stage 2: Development image
FROM golang:1.21-alpine AS development

WORKDIR /app

# Install development dependencies
RUN apk add --no-cache git ca-certificates tzdata curl make gcc musl-dev

# Install air for hot reloading
RUN go install github.com/cosmtrek/air@latest

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Expose ports
EXPOSE 8080 8081

# Run with hot reloading
CMD ["air", "-c", ".air.toml"]

# Stage 3: Final production image
FROM alpine:latest AS production

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata curl docker-cli

WORKDIR /app

# Copy the binary from builder stage
COPY --from=backend-builder /app/chainops .

# Copy the migration tool
COPY --from=backend-builder /app/migrate ./migrate



# Copy configuration files
COPY config/ ./config/
COPY scripts/ ./scripts/

# Create non-root user
RUN addgroup -g 1001 -S chainops && \
    adduser -S chainops -u 1001 -G chainops

# Create necessary directories
RUN mkdir -p /app/logs /app/data && \
    chown -R chainops:chainops /app

# Switch to non-root user
USER chainops

# Expose ports
EXPOSE 8080 8081

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application
CMD ["./chainops"]
