package testing

import (
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/chainops/chainops/pkg/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
	"github.com/sirupsen/logrus"
)

// LocalRunner provides local pipeline testing capabilities
type LocalRunner struct {
	dockerClient *client.Client
	workDir      string
	logger       *logrus.Logger
	config       *LocalRunnerConfig
}

// LocalRunnerConfig represents local runner configuration
type LocalRunnerConfig struct {
	WorkspaceDir    string            `yaml:"workspace_dir"`
	CacheDir        string            `yaml:"cache_dir"`
	DefaultImage    string            `yaml:"default_image"`
	Environment     map[string]string `yaml:"environment"`
	Volumes         []VolumeMount     `yaml:"volumes"`
	NetworkMode     string            `yaml:"network_mode"`
	EnableDocker    bool              `yaml:"enable_docker"`
	EnableSecrets   bool              `yaml:"enable_secrets"`
	TimeoutDuration time.Duration     `yaml:"timeout_duration"`
}

// VolumeMount represents a volume mount configuration
type VolumeMount struct {
	Source string `yaml:"source"`
	Target string `yaml:"target"`
	Type   string `yaml:"type"` // bind, volume, tmpfs
}

// LocalExecution represents a local pipeline execution
type LocalExecution struct {
	ID          string                `json:"id"`
	PipelineID  string                `json:"pipeline_id"`
	Status      types.ExecutionStatus `json:"status"`
	StartedAt   time.Time             `json:"started_at"`
	CompletedAt *time.Time            `json:"completed_at,omitempty"`
	Jobs        []LocalJob            `json:"jobs"`
	Logs        []string              `json:"logs"`
	Environment map[string]string     `json:"environment"`
	WorkDir     string                `json:"work_dir"`
}

// LocalJob represents a local job execution
type LocalJob struct {
	ID          string                `json:"id"`
	Name        string                `json:"name"`
	Status      types.ExecutionStatus `json:"status"`
	StartedAt   time.Time             `json:"started_at"`
	CompletedAt *time.Time            `json:"completed_at,omitempty"`
	Steps       []LocalStep           `json:"steps"`
	Logs        []string              `json:"logs"`
	ExitCode    int                   `json:"exit_code"`
	Duration    time.Duration         `json:"duration"`
}

// LocalStep represents a local step execution
type LocalStep struct {
	ID          string                `json:"id"`
	Name        string                `json:"name"`
	Status      types.ExecutionStatus `json:"status"`
	StartedAt   time.Time             `json:"started_at"`
	CompletedAt *time.Time            `json:"completed_at,omitempty"`
	Command     string                `json:"command"`
	Output      string                `json:"output"`
	Error       string                `json:"error,omitempty"`
	ExitCode    int                   `json:"exit_code"`
	Duration    time.Duration         `json:"duration"`
}

// NewLocalRunner creates a new local runner instance
func NewLocalRunner(config *LocalRunnerConfig, logger *logrus.Logger) (*LocalRunner, error) {
	var dockerClient *client.Client
	var err error

	if config.EnableDocker {
		dockerClient, err = client.NewClientWithOpts(client.FromEnv)
		if err != nil {
			logger.WithError(err).Warn("Failed to create Docker client, Docker support disabled")
			config.EnableDocker = false
		}
	}

	// Create workspace directory if it doesn't exist
	if config.WorkspaceDir == "" {
		config.WorkspaceDir = filepath.Join(os.TempDir(), "chainops-local")
	}

	if err := os.MkdirAll(config.WorkspaceDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create workspace directory: %w", err)
	}

	// Create cache directory
	if config.CacheDir == "" {
		config.CacheDir = filepath.Join(config.WorkspaceDir, ".cache")
	}

	if err := os.MkdirAll(config.CacheDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create cache directory: %w", err)
	}

	// Set defaults
	if config.DefaultImage == "" {
		config.DefaultImage = "ubuntu:latest"
	}

	if config.TimeoutDuration == 0 {
		config.TimeoutDuration = 30 * time.Minute
	}

	return &LocalRunner{
		dockerClient: dockerClient,
		workDir:      config.WorkspaceDir,
		logger:       logger,
		config:       config,
	}, nil
}

// RunPipeline executes a pipeline locally
func (lr *LocalRunner) RunPipeline(ctx context.Context, pipelineConfig *types.PipelineConfig) (*LocalExecution, error) {
	execution := &LocalExecution{
		ID:          fmt.Sprintf("local-%d", time.Now().Unix()),
		PipelineID:  pipelineConfig.Name,
		Status:      types.StatusRunning,
		StartedAt:   time.Now(),
		Jobs:        []LocalJob{},
		Logs:        []string{},
		Environment: lr.mergeEnvironment(pipelineConfig.Variables),
		WorkDir:     lr.workDir,
	}

	lr.logger.WithFields(logrus.Fields{
		"execution_id": execution.ID,
		"pipeline":     pipelineConfig.Name,
	}).Info("Starting local pipeline execution")

	execution.Logs = append(execution.Logs, fmt.Sprintf("[%s] Starting pipeline execution: %s", time.Now().Format(time.RFC3339), pipelineConfig.Name))

	// Create execution workspace
	executionDir := filepath.Join(lr.workDir, execution.ID)
	if err := os.MkdirAll(executionDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create execution directory: %w", err)
	}

	// Execute stages sequentially
	for _, stage := range pipelineConfig.Stages {
		if err := lr.executeStage(ctx, execution, stage, executionDir); err != nil {
			execution.Status = types.StatusFailure
			execution.Logs = append(execution.Logs, fmt.Sprintf("[%s] Pipeline failed: %v", time.Now().Format(time.RFC3339), err))
			break
		}
	}

	// Complete execution
	now := time.Now()
	execution.CompletedAt = &now

	if execution.Status == types.StatusRunning {
		execution.Status = types.StatusSuccess
		execution.Logs = append(execution.Logs, fmt.Sprintf("[%s] Pipeline completed successfully", time.Now().Format(time.RFC3339)))
	}

	lr.logger.WithFields(logrus.Fields{
		"execution_id": execution.ID,
		"status":       execution.Status,
		"duration":     time.Since(execution.StartedAt),
	}).Info("Local pipeline execution completed")

	return execution, nil
}

// executeStage executes a pipeline stage
func (lr *LocalRunner) executeStage(ctx context.Context, execution *LocalExecution, stage types.Stage, workDir string) error {
	lr.logger.WithField("stage", stage.Name).Info("Executing stage")
	execution.Logs = append(execution.Logs, fmt.Sprintf("[%s] Starting stage: %s", time.Now().Format(time.RFC3339), stage.Name))

	// Execute steps in the stage
	for _, step := range stage.Steps {
		job := LocalJob{
			ID:        fmt.Sprintf("%s-%s", execution.ID, step.Name),
			Name:      step.Name,
			Status:    types.StatusRunning,
			StartedAt: time.Now(),
			Steps:     []LocalStep{},
			Logs:      []string{},
		}

		execution.Jobs = append(execution.Jobs, job)
		jobIndex := len(execution.Jobs) - 1

		if err := lr.executeStep(ctx, execution, &execution.Jobs[jobIndex], step, workDir); err != nil {
			execution.Jobs[jobIndex].Status = types.StatusFailure
			return fmt.Errorf("step %s failed: %w", step.Name, err)
		}

		now := time.Now()
		execution.Jobs[jobIndex].CompletedAt = &now
		execution.Jobs[jobIndex].Duration = time.Since(execution.Jobs[jobIndex].StartedAt)
		execution.Jobs[jobIndex].Status = types.StatusSuccess
	}

	return nil
}

// executeStep executes a pipeline step
func (lr *LocalRunner) executeStep(ctx context.Context, execution *LocalExecution, job *LocalJob, step types.Step, workDir string) error {
	lr.logger.WithField("step", step.Name).Info("Executing step")

	localStep := LocalStep{
		ID:        fmt.Sprintf("%s-%s", job.ID, step.Name),
		Name:      step.Name,
		Status:    types.StatusRunning,
		StartedAt: time.Now(),
	}

	job.Steps = append(job.Steps, localStep)
	stepIndex := len(job.Steps) - 1

	var err error

	// Determine execution method
	if step.Uses != "" {
		// Plugin/action execution
		err = lr.executePlugin(ctx, execution, job, &job.Steps[stepIndex], step, workDir)
	} else if step.Run != "" {
		// Shell command execution
		err = lr.executeCommand(ctx, execution, job, &job.Steps[stepIndex], step, workDir)
	} else {
		err = fmt.Errorf("step has neither 'uses' nor 'run' specified")
	}

	// Complete step
	now := time.Now()
	job.Steps[stepIndex].CompletedAt = &now
	job.Steps[stepIndex].Duration = time.Since(job.Steps[stepIndex].StartedAt)

	if err != nil {
		job.Steps[stepIndex].Status = types.StatusFailure
		job.Steps[stepIndex].Error = err.Error()
		job.Steps[stepIndex].ExitCode = 1
		return err
	}

	job.Steps[stepIndex].Status = types.StatusSuccess
	job.Steps[stepIndex].ExitCode = 0
	return nil
}

// executeCommand executes a shell command
func (lr *LocalRunner) executeCommand(ctx context.Context, execution *LocalExecution, job *LocalJob, step *LocalStep, stepConfig types.Step, workDir string) error {
	step.Command = stepConfig.Run

	// Prepare environment
	env := lr.mergeEnvironment(stepConfig.Environment)
	envSlice := make([]string, 0, len(env))
	for k, v := range env {
		envSlice = append(envSlice, fmt.Sprintf("%s=%s", k, v))
	}

	// Create command
	var cmd *exec.Cmd
	if lr.config.EnableDocker && stepConfig.With["image"] != "" {
		// Execute in Docker container
		return lr.executeInDocker(ctx, execution, job, step, stepConfig, workDir)
	} else {
		// Execute locally
		cmd = exec.CommandContext(ctx, "bash", "-c", stepConfig.Run)
		cmd.Dir = workDir
		cmd.Env = append(os.Environ(), envSlice...)
	}

	// Capture output
	output, err := cmd.CombinedOutput()
	step.Output = string(output)

	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			step.ExitCode = exitError.ExitCode()
		} else {
			step.ExitCode = 1
		}
		return fmt.Errorf("command failed: %w", err)
	}

	step.ExitCode = 0
	job.Logs = append(job.Logs, step.Output)
	execution.Logs = append(execution.Logs, fmt.Sprintf("[%s] Step %s completed", time.Now().Format(time.RFC3339), step.Name))

	return nil
}

// executePlugin executes a plugin/action
func (lr *LocalRunner) executePlugin(ctx context.Context, execution *LocalExecution, job *LocalJob, step *LocalStep, stepConfig types.Step, workDir string) error {
	// For now, treat plugins as Docker containers
	if !lr.config.EnableDocker {
		return fmt.Errorf("plugin execution requires Docker support")
	}

	// Parse plugin reference
	pluginRef := stepConfig.Uses
	if !strings.Contains(pluginRef, ":") {
		pluginRef += ":latest"
	}

	// Create a modified step config for Docker execution
	dockerStep := stepConfig
	dockerStep.With["image"] = pluginRef
	dockerStep.Run = "echo 'Plugin execution placeholder'"

	return lr.executeInDocker(ctx, execution, job, step, dockerStep, workDir)
}

// executeInDocker executes a step in a Docker container
func (lr *LocalRunner) executeInDocker(ctx context.Context, execution *LocalExecution, job *LocalJob, step *LocalStep, stepConfig types.Step, workDir string) error {
	if lr.dockerClient == nil {
		return fmt.Errorf("Docker client not available")
	}

	image := stepConfig.With["image"]
	if image == "" {
		image = lr.config.DefaultImage
	}

	// Pull image if needed (simplified for now)
	lr.logger.WithField("image", image).Debug("Using Docker image")
	// TODO: Implement proper image pulling with correct Docker API

	// Prepare environment
	env := lr.mergeEnvironment(stepConfig.Environment)
	envSlice := make([]string, 0, len(env))
	for k, v := range env {
		envSlice = append(envSlice, fmt.Sprintf("%s=%s", k, v))
	}

	// Create container
	containerConfig := &container.Config{
		Image:        image,
		Cmd:          []string{"bash", "-c", stepConfig.Run},
		Env:          envSlice,
		WorkingDir:   "/workspace",
		AttachStdout: true,
		AttachStderr: true,
	}

	hostConfig := &container.HostConfig{
		Binds: []string{
			fmt.Sprintf("%s:/workspace", workDir),
		},
		AutoRemove: true,
	}

	// Add configured volume mounts
	for _, mount := range lr.config.Volumes {
		hostConfig.Binds = append(hostConfig.Binds, fmt.Sprintf("%s:%s", mount.Source, mount.Target))
	}

	resp, err := lr.dockerClient.ContainerCreate(ctx, containerConfig, hostConfig, nil, nil, "")
	if err != nil {
		return fmt.Errorf("failed to create container: %w", err)
	}

	// Start container
	if err := lr.dockerClient.ContainerStart(ctx, resp.ID, container.StartOptions{}); err != nil {
		return fmt.Errorf("failed to start container: %w", err)
	}

	// Wait for completion
	statusCh, errCh := lr.dockerClient.ContainerWait(ctx, resp.ID, container.WaitConditionNotRunning)
	select {
	case err := <-errCh:
		if err != nil {
			return fmt.Errorf("container wait error: %w", err)
		}
	case status := <-statusCh:
		step.ExitCode = int(status.StatusCode)
	}

	// Get logs
	logs, err := lr.dockerClient.ContainerLogs(ctx, resp.ID, container.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
	})
	if err != nil {
		return fmt.Errorf("failed to get container logs: %w", err)
	}
	defer logs.Close()

	output, err := io.ReadAll(logs)
	if err != nil {
		return fmt.Errorf("failed to read container logs: %w", err)
	}

	step.Output = string(output)
	job.Logs = append(job.Logs, step.Output)
	execution.Logs = append(execution.Logs, fmt.Sprintf("[%s] Step %s completed in Docker", time.Now().Format(time.RFC3339), step.Name))

	if step.ExitCode != 0 {
		return fmt.Errorf("container exited with code %d", step.ExitCode)
	}

	return nil
}

// mergeEnvironment merges configuration environment with step environment
func (lr *LocalRunner) mergeEnvironment(stepEnv map[string]string) map[string]string {
	env := make(map[string]string)

	// Start with config environment
	for k, v := range lr.config.Environment {
		env[k] = v
	}

	// Override with step environment
	for k, v := range stepEnv {
		env[k] = v
	}

	return env
}

// GetDefaultConfig returns a default local runner configuration
func GetDefaultConfig() *LocalRunnerConfig {
	homeDir, _ := os.UserHomeDir()

	return &LocalRunnerConfig{
		WorkspaceDir:    filepath.Join(homeDir, ".chainops", "workspace"),
		CacheDir:        filepath.Join(homeDir, ".chainops", "cache"),
		DefaultImage:    "ubuntu:latest",
		Environment:     map[string]string{},
		Volumes:         []VolumeMount{},
		NetworkMode:     "bridge",
		EnableDocker:    true,
		EnableSecrets:   false,
		TimeoutDuration: 30 * time.Minute,
	}
}
