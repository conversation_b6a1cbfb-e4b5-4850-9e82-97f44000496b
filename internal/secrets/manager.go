package secrets

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"strings"

	"github.com/chainops/chainops/internal/storage"
	"github.com/chainops/chainops/pkg/types"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// SecretProvider represents a secret storage provider
type SecretProvider interface {
	// GetSecret retrieves a secret by key
	GetSecret(ctx context.Context, key string) (string, error)

	// SetSecret stores a secret with the given key
	SetSecret(ctx context.Context, key, value string) error

	// DeleteSecret deletes a secret by key
	DeleteSecret(ctx context.Context, key string) error

	// ListSecrets lists all secret keys
	ListSecrets(ctx context.Context) ([]string, error)

	// Close closes the provider connection
	Close() error
}

// SecretManager manages secrets across different providers
type SecretManager struct {
	providers       map[string]SecretProvider
	defaultProvider string
	secretRepo      *storage.SecretRepository
	encryptionKey   []byte
	logger          *logrus.Logger
}

// SecretConfig represents secret configuration
type SecretConfig struct {
	Provider      string                    `yaml:"provider"`
	EncryptionKey string                    `yaml:"encryption_key"`
	Providers     map[string]ProviderConfig `yaml:"providers"`
}

// ProviderConfig represents provider-specific configuration
type ProviderConfig struct {
	Type   string                 `yaml:"type"`
	Config map[string]interface{} `yaml:"config"`
}

// SecretRequest represents a secret creation/update request
type SecretRequest struct {
	Name         string `json:"name" binding:"required"`
	Value        string `json:"value" binding:"required"`
	Description  string `json:"description"`
	Organization string `json:"organization"`
}

// SecretResponse represents a secret response (without value)
type SecretResponse struct {
	ID           uuid.UUID `json:"id"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	Organization string    `json:"organization"`
	CreatedBy    string    `json:"created_by"`
	CreatedAt    string    `json:"created_at"`
	UpdatedAt    string    `json:"updated_at"`
}

// NewSecretManager creates a new secret manager
func NewSecretManager(
	config SecretConfig,
	secretRepo *storage.SecretRepository,
	logger *logrus.Logger,
) (*SecretManager, error) {
	// Generate encryption key from config
	encryptionKey := sha256.Sum256([]byte(config.EncryptionKey))

	manager := &SecretManager{
		providers:     make(map[string]SecretProvider),
		secretRepo:    secretRepo,
		encryptionKey: encryptionKey[:],
		logger:        logger,
	}

	// Initialize providers
	for name, providerConfig := range config.Providers {
		provider, err := manager.createProvider(providerConfig)
		if err != nil {
			logger.WithError(err).WithField("provider", name).Error("Failed to create secret provider")
			continue
		}

		manager.providers[name] = provider
		if manager.defaultProvider == "" {
			manager.defaultProvider = name
		}
	}

	// Set default provider
	if config.Provider != "" {
		manager.defaultProvider = config.Provider
	}

	return manager, nil
}

// createProvider creates a secret provider based on configuration
func (sm *SecretManager) createProvider(config ProviderConfig) (SecretProvider, error) {
	switch config.Type {
	case "vault":
		// TODO: Implement VaultProvider
		return nil, fmt.Errorf("vault provider not implemented yet")
	case "k8s":
		// TODO: Implement KubernetesProvider
		return nil, fmt.Errorf("kubernetes provider not implemented yet")
	case "aws":
		// TODO: Implement AWSSecretsManagerProvider
		return nil, fmt.Errorf("aws provider not implemented yet")
	case "azure":
		// TODO: Implement AzureKeyVaultProvider
		return nil, fmt.Errorf("azure provider not implemented yet")
	case "local":
		// TODO: Implement LocalProvider
		return nil, fmt.Errorf("local provider not implemented yet")
	default:
		return nil, fmt.Errorf("unsupported provider type: %s", config.Type)
	}
}

// CreateSecret creates a new secret
func (sm *SecretManager) CreateSecret(ctx context.Context, orgID string, req SecretRequest) (*types.Secret, error) {
	// Encrypt the secret value
	encryptedValue, err := sm.encrypt(req.Value)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt secret: %w", err)
	}

	// Create secret record
	secret := &types.Secret{
		Name:           req.Name,
		Description:    req.Description,
		EncryptedValue: encryptedValue,
	}

	// Parse organization ID
	if orgID != "" {
		orgUUID, err := uuid.Parse(orgID)
		if err != nil {
			return nil, fmt.Errorf("invalid organization ID: %w", err)
		}
		secret.OrganizationID = orgUUID
	}

	// Store in database
	if err := sm.secretRepo.Create(secret); err != nil {
		return nil, fmt.Errorf("failed to create secret: %w", err)
	}

	// Store in external provider if configured
	if sm.defaultProvider != "" {
		provider := sm.providers[sm.defaultProvider]
		key := sm.buildSecretKey(orgID, req.Name)
		if err := provider.SetSecret(ctx, key, req.Value); err != nil {
			sm.logger.WithError(err).Warn("Failed to store secret in external provider")
		}
	}

	sm.logger.WithFields(logrus.Fields{
		"secret_id":    secret.ID,
		"secret_name":  secret.Name,
		"organization": orgID,
	}).Info("Secret created")

	return secret, nil
}

// GetSecret retrieves a secret by name
func (sm *SecretManager) GetSecret(ctx context.Context, orgID, name string) (string, error) {
	// Try external provider first
	if sm.defaultProvider != "" {
		provider := sm.providers[sm.defaultProvider]
		key := sm.buildSecretKey(orgID, name)
		if value, err := provider.GetSecret(ctx, key); err == nil {
			return value, nil
		}
	}

	// Fallback to database
	secret, err := sm.secretRepo.GetByName(orgID, name)
	if err != nil {
		return "", fmt.Errorf("secret not found: %w", err)
	}

	// Decrypt the value
	value, err := sm.decrypt(secret.EncryptedValue)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt secret: %w", err)
	}

	return value, nil
}

// ListSecrets lists all secrets for an organization
func (sm *SecretManager) ListSecrets(ctx context.Context, orgID string) ([]SecretResponse, error) {
	secrets, err := sm.secretRepo.GetByOrganizationID(orgID)
	if err != nil {
		return nil, fmt.Errorf("failed to list secrets: %w", err)
	}

	var responses []SecretResponse
	for _, secret := range secrets {
		responses = append(responses, SecretResponse{
			ID:          secret.ID,
			Name:        secret.Name,
			Description: secret.Description,
			CreatedAt:   secret.CreatedAt.Format("2006-01-02T15:04:05Z"),
			UpdatedAt:   secret.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		})
	}

	return responses, nil
}

// UpdateSecret updates an existing secret
func (sm *SecretManager) UpdateSecret(ctx context.Context, orgID, name string, req SecretRequest) error {
	// Get existing secret
	secret, err := sm.secretRepo.GetByName(orgID, name)
	if err != nil {
		return fmt.Errorf("secret not found: %w", err)
	}

	// Encrypt new value
	encryptedValue, err := sm.encrypt(req.Value)
	if err != nil {
		return fmt.Errorf("failed to encrypt secret: %w", err)
	}

	// Update secret
	secret.EncryptedValue = encryptedValue
	secret.Description = req.Description

	if err := sm.secretRepo.Update(secret); err != nil {
		return fmt.Errorf("failed to update secret: %w", err)
	}

	// Update in external provider
	if sm.defaultProvider != "" {
		provider := sm.providers[sm.defaultProvider]
		key := sm.buildSecretKey(orgID, name)
		if err := provider.SetSecret(ctx, key, req.Value); err != nil {
			sm.logger.WithError(err).Warn("Failed to update secret in external provider")
		}
	}

	sm.logger.WithFields(logrus.Fields{
		"secret_id":    secret.ID,
		"secret_name":  secret.Name,
		"organization": orgID,
	}).Info("Secret updated")

	return nil
}

// DeleteSecret deletes a secret
func (sm *SecretManager) DeleteSecret(ctx context.Context, orgID, name string) error {
	// Delete from database
	if err := sm.secretRepo.Delete(orgID, name); err != nil {
		return fmt.Errorf("failed to delete secret: %w", err)
	}

	// Delete from external provider
	if sm.defaultProvider != "" {
		provider := sm.providers[sm.defaultProvider]
		key := sm.buildSecretKey(orgID, name)
		if err := provider.DeleteSecret(ctx, key); err != nil {
			sm.logger.WithError(err).Warn("Failed to delete secret from external provider")
		}
	}

	sm.logger.WithFields(logrus.Fields{
		"secret_name":  name,
		"organization": orgID,
	}).Info("Secret deleted")

	return nil
}

// InjectSecrets injects secrets into environment variables
func (sm *SecretManager) InjectSecrets(ctx context.Context, orgID string, env map[string]string) (map[string]string, error) {
	result := make(map[string]string)

	// Copy existing environment variables
	for k, v := range env {
		result[k] = v
	}

	// Process secret references
	for key, value := range env {
		if strings.HasPrefix(value, "$SECRET{") && strings.HasSuffix(value, "}") {
			// Extract secret name
			secretName := value[8 : len(value)-1] // Remove $SECRET{ and }

			// Get secret value
			secretValue, err := sm.GetSecret(ctx, orgID, secretName)
			if err != nil {
				sm.logger.WithError(err).WithField("secret", secretName).Error("Failed to resolve secret")
				continue
			}

			result[key] = secretValue
		}
	}

	return result, nil
}

// encrypt encrypts a value using AES-GCM
func (sm *SecretManager) encrypt(plaintext string) (string, error) {
	block, err := aes.NewCipher(sm.encryptionKey)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decrypt decrypts a value using AES-GCM
func (sm *SecretManager) decrypt(ciphertext string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(sm.encryptionKey)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertextBytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertextBytes, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// buildSecretKey builds a secret key for external providers
func (sm *SecretManager) buildSecretKey(orgID, name string) string {
	if orgID == "" {
		return fmt.Sprintf("chainops/secrets/%s", name)
	}
	return fmt.Sprintf("chainops/secrets/%s/%s", orgID, name)
}

// Close closes all providers
func (sm *SecretManager) Close() error {
	var errors []error

	for name, provider := range sm.providers {
		if err := provider.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close provider %s: %w", name, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors closing providers: %v", errors)
	}

	return nil
}
