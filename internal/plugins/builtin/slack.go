package builtin

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/chainops/chainops/internal/plugins"
)

// SlackPlugin implements Slack notifications
type SlackPlugin struct {
	config     map[string]interface{}
	webhookURL string
	client     *http.Client
}

// NewSlackPlugin creates a new Slack plugin
func NewSlackPlugin() plugins.Plugin {
	return &SlackPlugin{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetInfo returns plugin information
func (p *SlackPlugin) GetInfo() plugins.PluginInfo {
	return plugins.PluginInfo{
		Name:        "slack",
		Version:     "1.0.0",
		Description: "Slack notification plugin for sending messages and alerts",
		Author:      "ChainOps Team",
		Type:        plugins.PluginTypeNotification,
		Tags:        []string{"slack", "notification", "messaging"},
		Config: map[string]string{
			"webhook_url": "Slack webhook URL",
			"channel":     "Default channel",
			"username":    "Bot username",
			"icon_emoji":  "Bot icon emoji",
		},
	}
}

// Initialize initializes the plugin
func (p *SlackPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// Get webhook URL
	if webhookURL, ok := config["webhook_url"].(string); ok {
		p.webhookURL = webhookURL
	} else {
		return fmt.Errorf("webhook_url is required for Slack plugin")
	}

	return nil
}

// Execute executes the plugin
func (p *SlackPlugin) Execute(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	message := getStringParam(params, "message", "")
	if message == "" {
		return nil, fmt.Errorf("message parameter is required")
	}

	// Create Slack message
	slackMessage := SlackMessage{
		Text:      message,
		Channel:   getStringParam(params, "channel", getStringParam(p.config, "channel", "")),
		Username:  getStringParam(params, "username", getStringParam(p.config, "username", "ChainOps")),
		IconEmoji: getStringParam(params, "icon_emoji", getStringParam(p.config, "icon_emoji", ":robot_face:")),
	}

	// Add color based on level
	level := getStringParam(params, "level", "info")
	color := p.getColorForLevel(level)

	// Create attachment if additional fields are provided
	if title := getStringParam(params, "title", ""); title != "" {
		attachment := SlackAttachment{
			Color:     color,
			Title:     title,
			Text:      message,
			Timestamp: time.Now().Unix(),
		}

		// Add fields
		if fields, ok := params["fields"].(map[string]string); ok {
			for key, value := range fields {
				attachment.Fields = append(attachment.Fields, SlackField{
					Title: key,
					Value: value,
					Short: true,
				})
			}
		}

		slackMessage.Attachments = []SlackAttachment{attachment}
		slackMessage.Text = "" // Clear text when using attachments
	}

	// Send message
	err := p.sendMessage(ctx, slackMessage)

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  "Slack message sent successfully",
	}

	if err != nil {
		result.Error = err.Error()
		result.Success = false
	}

	return result, nil
}

// SendNotification sends a notification to Slack
func (p *SlackPlugin) SendNotification(ctx context.Context, notification plugins.Notification) error {
	// Create Slack message from notification
	slackMessage := SlackMessage{
		Text:      notification.Message,
		Username:  getStringParam(p.config, "username", "ChainOps"),
		IconEmoji: getStringParam(p.config, "icon_emoji", ":robot_face:"),
	}

	// Create attachment
	color := p.getColorForLevel(notification.Level)
	attachment := SlackAttachment{
		Color:     color,
		Title:     notification.Title,
		Text:      notification.Message,
		Timestamp: time.Now().Unix(),
	}

	// Add metadata as fields
	if notification.Metadata != nil {
		for key, value := range notification.Metadata {
			if strValue, ok := value.(string); ok {
				attachment.Fields = append(attachment.Fields, SlackField{
					Title: key,
					Value: strValue,
					Short: true,
				})
			}
		}
	}

	slackMessage.Attachments = []SlackAttachment{attachment}

	return p.sendMessage(ctx, slackMessage)
}

// sendMessage sends a message to Slack
func (p *SlackPlugin) sendMessage(ctx context.Context, message SlackMessage) error {
	// Marshal message to JSON
	payload, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal Slack message: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", p.webhookURL, bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := p.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send Slack message: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Slack API returned status %d", resp.StatusCode)
	}

	return nil
}

// getColorForLevel returns color based on notification level
func (p *SlackPlugin) getColorForLevel(level string) string {
	switch level {
	case "error":
		return "danger"
	case "warning":
		return "warning"
	case "success":
		return "good"
	default:
		return "#36a64f" // Default green
	}
}

// Cleanup performs cleanup operations
func (p *SlackPlugin) Cleanup() error {
	// No cleanup needed for Slack plugin
	return nil
}

// SlackMessage represents a Slack message
type SlackMessage struct {
	Text        string            `json:"text,omitempty"`
	Channel     string            `json:"channel,omitempty"`
	Username    string            `json:"username,omitempty"`
	IconEmoji   string            `json:"icon_emoji,omitempty"`
	IconURL     string            `json:"icon_url,omitempty"`
	Attachments []SlackAttachment `json:"attachments,omitempty"`
}

// SlackAttachment represents a Slack message attachment
type SlackAttachment struct {
	Color      string       `json:"color,omitempty"`
	Title      string       `json:"title,omitempty"`
	TitleLink  string       `json:"title_link,omitempty"`
	Text       string       `json:"text,omitempty"`
	Fields     []SlackField `json:"fields,omitempty"`
	Timestamp  int64        `json:"ts,omitempty"`
	Footer     string       `json:"footer,omitempty"`
	FooterIcon string       `json:"footer_icon,omitempty"`
}

// SlackField represents a field in a Slack attachment
type SlackField struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}
