package builtin

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"github.com/chainops/chainops/internal/plugins"
	"github.com/chainops/chainops/pkg/types"
)

// DockerPlugin implements Docker operations
type DockerPlugin struct {
	config map[string]interface{}
}

// NewDockerPlugin creates a new Docker plugin
func NewDockerPlugin() plugins.Plugin {
	return &DockerPlugin{}
}

// GetInfo returns plugin information
func (p *DockerPlugin) GetInfo() plugins.PluginInfo {
	return plugins.PluginInfo{
		Name:        "docker",
		Version:     "1.0.0",
		Description: "Docker operations plugin for building and pushing images",
		Author:      "ChainOps Team",
		Type:        plugins.PluginTypeStep,
		Tags:        []string{"docker", "container", "build"},
		Config: map[string]string{
			"registry": "Docker registry URL",
			"username": "Registry username",
			"password": "Registry password",
		},
	}
}

// Initialize initializes the plugin
func (p *DockerPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// Check if Docker is available
	if err := exec.Command("docker", "version").Run(); err != nil {
		return fmt.Errorf("Docker is not available: %w", err)
	}

	return nil
}

// Execute executes the plugin
func (p *DockerPlugin) Execute(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	action, ok := params["action"].(string)
	if !ok {
		return nil, fmt.Errorf("action parameter is required")
	}

	switch action {
	case "build":
		return p.buildImage(ctx, params)
	case "push":
		return p.pushImage(ctx, params)
	case "pull":
		return p.pullImage(ctx, params)
	case "run":
		return p.runContainer(ctx, params)
	default:
		return nil, fmt.Errorf("unsupported action: %s", action)
	}
}

// ExecuteStep executes a pipeline step
func (p *DockerPlugin) ExecuteStep(ctx context.Context, step *types.PipelineStep, env map[string]string) (*plugins.StepResult, error) {
	startTime := time.Now()

	// Parse step configuration
	action := step.With["action"]
	if action == "" {
		return &plugins.StepResult{
			Success:  false,
			ExitCode: 1,
			Error:    "Docker action not specified",
			Duration: time.Since(startTime).Milliseconds(),
		}, nil
	}

	var cmd *exec.Cmd
	var output strings.Builder

	switch action {
	case "build":
		cmd = p.buildCommand(ctx, step, env)
	case "push":
		cmd = p.pushCommand(ctx, step, env)
	case "pull":
		cmd = p.pullCommand(ctx, step, env)
	default:
		return &plugins.StepResult{
			Success:  false,
			ExitCode: 1,
			Error:    fmt.Sprintf("Unsupported Docker action: %s", action),
			Duration: time.Since(startTime).Milliseconds(),
		}, nil
	}

	// Set up command context and output capture
	cmd.Stdout = &output
	cmd.Stderr = &output

	// Execute command
	err := cmd.Run()
	duration := time.Since(startTime).Milliseconds()

	result := &plugins.StepResult{
		Success:  err == nil,
		Output:   output.String(),
		Duration: duration,
	}

	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			result.ExitCode = exitError.ExitCode()
		} else {
			result.ExitCode = 1
		}
		result.Error = err.Error()
	}

	return result, nil
}

// buildCommand creates a Docker build command
func (p *DockerPlugin) buildCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"build"}

	// Add build arguments
	if dockerfile := step.With["dockerfile"]; dockerfile != "" {
		args = append(args, "-f", dockerfile)
	}

	if tag := step.With["tag"]; tag != "" {
		args = append(args, "-t", tag)
	}

	// Add build args
	if buildArgs := step.With["build_args"]; buildArgs != "" {
		for _, arg := range strings.Split(buildArgs, ",") {
			args = append(args, "--build-arg", strings.TrimSpace(arg))
		}
	}

	// Add context path
	context := step.With["context"]
	if context == "" {
		context = "."
	}
	args = append(args, context)

	return exec.CommandContext(ctx, "docker", args...)
}

// pushCommand creates a Docker push command
func (p *DockerPlugin) pushCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	tag := step.With["tag"]
	if tag == "" {
		tag = step.With["image"]
	}

	return exec.CommandContext(ctx, "docker", "push", tag)
}

// pullCommand creates a Docker pull command
func (p *DockerPlugin) pullCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	image := step.With["image"]
	return exec.CommandContext(ctx, "docker", "pull", image)
}

// buildImage builds a Docker image
func (p *DockerPlugin) buildImage(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	dockerfile := getStringParam(params, "dockerfile", "Dockerfile")
	tag := getStringParam(params, "tag", "")
	context := getStringParam(params, "context", ".")

	if tag == "" {
		return nil, fmt.Errorf("tag parameter is required for build")
	}

	args := []string{"build", "-f", dockerfile, "-t", tag, context}

	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// pushImage pushes a Docker image
func (p *DockerPlugin) pushImage(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	tag := getStringParam(params, "tag", "")
	if tag == "" {
		return nil, fmt.Errorf("tag parameter is required for push")
	}

	cmd := exec.CommandContext(ctx, "docker", "push", tag)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// pullImage pulls a Docker image
func (p *DockerPlugin) pullImage(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	image := getStringParam(params, "image", "")
	if image == "" {
		return nil, fmt.Errorf("image parameter is required for pull")
	}

	cmd := exec.CommandContext(ctx, "docker", "pull", image)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// runContainer runs a Docker container
func (p *DockerPlugin) runContainer(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	image := getStringParam(params, "image", "")
	if image == "" {
		return nil, fmt.Errorf("image parameter is required for run")
	}

	args := []string{"run", "--rm"}

	// Add environment variables
	if envVars, ok := params["env"].(map[string]string); ok {
		for key, value := range envVars {
			args = append(args, "-e", fmt.Sprintf("%s=%s", key, value))
		}
	}

	// Add volumes
	if volumes, ok := params["volumes"].([]string); ok {
		for _, volume := range volumes {
			args = append(args, "-v", volume)
		}
	}

	// Add command
	args = append(args, image)
	if command, ok := params["command"].(string); ok {
		args = append(args, "sh", "-c", command)
	}

	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// Cleanup performs cleanup operations
func (p *DockerPlugin) Cleanup() error {
	// No cleanup needed for Docker plugin
	return nil
}
