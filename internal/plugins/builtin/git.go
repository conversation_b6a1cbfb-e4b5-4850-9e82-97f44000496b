package builtin

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"github.com/chainops/chainops/internal/plugins"
	"github.com/chainops/chainops/pkg/types"
)

// GitPlugin implements Git operations
type GitPlugin struct {
	config map[string]interface{}
}

// NewGitPlugin creates a new Git plugin
func NewGitPlugin() plugins.Plugin {
	return &GitPlugin{}
}

// GetInfo returns plugin information
func (p *GitPlugin) GetInfo() plugins.PluginInfo {
	return plugins.PluginInfo{
		Name:        "git",
		Version:     "1.0.0",
		Description: "Git operations plugin for cloning, pulling, and managing repositories",
		Author:      "ChainOps Team",
		Type:        plugins.PluginTypeStep,
		Tags:        []string{"git", "scm", "version-control"},
		Config: map[string]string{
			"username": "Git username",
			"token":    "Git access token",
			"ssh_key":  "SSH private key path",
		},
	}
}

// Initialize initializes the plugin
func (p *GitPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// Check if git is available
	if err := exec.Command("git", "version").Run(); err != nil {
		return fmt.Errorf("git is not available: %w", err)
	}

	return nil
}

// Execute executes the plugin
func (p *GitPlugin) Execute(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	action, ok := params["action"].(string)
	if !ok {
		return nil, fmt.Errorf("action parameter is required")
	}

	switch action {
	case "clone":
		return p.cloneRepository(ctx, params)
	case "pull":
		return p.pullRepository(ctx, params)
	case "checkout":
		return p.checkoutBranch(ctx, params)
	case "commit":
		return p.commitChanges(ctx, params)
	case "push":
		return p.pushChanges(ctx, params)
	case "tag":
		return p.createTag(ctx, params)
	default:
		return nil, fmt.Errorf("unsupported action: %s", action)
	}
}

// ExecuteStep executes a pipeline step
func (p *GitPlugin) ExecuteStep(ctx context.Context, step *types.PipelineStep, env map[string]string) (*plugins.StepResult, error) {
	startTime := time.Now()

	// Parse step configuration
	action := step.With["action"]
	if action == "" {
		return &plugins.StepResult{
			Success:  false,
			ExitCode: 1,
			Error:    "Git action not specified",
			Duration: time.Since(startTime).Milliseconds(),
		}, nil
	}

	var cmd *exec.Cmd
	var output strings.Builder

	switch action {
	case "clone":
		cmd = p.cloneCommand(ctx, step, env)
	case "pull":
		cmd = p.pullCommand(ctx, step, env)
	case "checkout":
		cmd = p.checkoutCommand(ctx, step, env)
	case "commit":
		cmd = p.commitCommand(ctx, step, env)
	case "push":
		cmd = p.pushCommand(ctx, step, env)
	case "tag":
		cmd = p.tagCommand(ctx, step, env)
	default:
		return &plugins.StepResult{
			Success:  false,
			ExitCode: 1,
			Error:    fmt.Sprintf("Unsupported Git action: %s", action),
			Duration: time.Since(startTime).Milliseconds(),
		}, nil
	}

	// Set up command context and output capture
	cmd.Stdout = &output
	cmd.Stderr = &output

	// Execute command
	err := cmd.Run()
	duration := time.Since(startTime).Milliseconds()

	result := &plugins.StepResult{
		Success:  err == nil,
		Output:   output.String(),
		Duration: duration,
	}

	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			result.ExitCode = exitError.ExitCode()
		} else {
			result.ExitCode = 1
		}
		result.Error = err.Error()
	}

	return result, nil
}

// cloneCommand creates a git clone command
func (p *GitPlugin) cloneCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"clone"}

	// Add depth for shallow clone
	if depth := step.With["depth"]; depth != "" {
		args = append(args, "--depth", depth)
	}

	// Add branch
	if branch := step.With["branch"]; branch != "" {
		args = append(args, "--branch", branch)
	}

	// Add single branch
	if singleBranch := step.With["single_branch"]; singleBranch == "true" {
		args = append(args, "--single-branch")
	}

	// Add repository URL
	if repo := step.With["repository"]; repo != "" {
		args = append(args, repo)
	}

	// Add destination directory
	if dir := step.With["directory"]; dir != "" {
		args = append(args, dir)
	}

	return exec.CommandContext(ctx, "git", args...)
}

// pullCommand creates a git pull command
func (p *GitPlugin) pullCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"pull"}

	// Add remote
	if remote := step.With["remote"]; remote != "" {
		args = append(args, remote)
	} else {
		args = append(args, "origin")
	}

	// Add branch
	if branch := step.With["branch"]; branch != "" {
		args = append(args, branch)
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	// Set working directory
	if dir := step.With["directory"]; dir != "" {
		cmd.Dir = dir
	}

	return cmd
}

// checkoutCommand creates a git checkout command
func (p *GitPlugin) checkoutCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"checkout"}

	// Add create branch flag
	if create := step.With["create"]; create == "true" {
		args = append(args, "-b")
	}

	// Add branch name
	if branch := step.With["branch"]; branch != "" {
		args = append(args, branch)
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	// Set working directory
	if dir := step.With["directory"]; dir != "" {
		cmd.Dir = dir
	}

	return cmd
}

// commitCommand creates a git commit command
func (p *GitPlugin) commitCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"commit"}

	// Add message
	if message := step.With["message"]; message != "" {
		args = append(args, "-m", message)
	}

	// Add all flag
	if all := step.With["all"]; all == "true" {
		args = append(args, "-a")
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	// Set working directory
	if dir := step.With["directory"]; dir != "" {
		cmd.Dir = dir
	}

	return cmd
}

// pushCommand creates a git push command
func (p *GitPlugin) pushCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"push"}

	// Add remote
	if remote := step.With["remote"]; remote != "" {
		args = append(args, remote)
	} else {
		args = append(args, "origin")
	}

	// Add branch
	if branch := step.With["branch"]; branch != "" {
		args = append(args, branch)
	}

	// Add force flag
	if force := step.With["force"]; force == "true" {
		args = append(args, "--force")
	}

	// Add tags
	if tags := step.With["tags"]; tags == "true" {
		args = append(args, "--tags")
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	// Set working directory
	if dir := step.With["directory"]; dir != "" {
		cmd.Dir = dir
	}

	return cmd
}

// tagCommand creates a git tag command
func (p *GitPlugin) tagCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"tag"}

	// Add tag name
	if tag := step.With["tag"]; tag != "" {
		args = append(args, tag)
	}

	// Add message for annotated tag
	if message := step.With["message"]; message != "" {
		args = append(args, "-m", message)
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	// Set working directory
	if dir := step.With["directory"]; dir != "" {
		cmd.Dir = dir
	}

	return cmd
}

// cloneRepository clones a Git repository
func (p *GitPlugin) cloneRepository(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	repo := getStringParam(params, "repository", "")
	if repo == "" {
		return nil, fmt.Errorf("repository parameter is required")
	}

	args := []string{"clone"}

	// Add optional parameters
	if depth := getStringParam(params, "depth", ""); depth != "" {
		args = append(args, "--depth", depth)
	}

	if branch := getStringParam(params, "branch", ""); branch != "" {
		args = append(args, "--branch", branch)
	}

	args = append(args, repo)

	if dir := getStringParam(params, "directory", ""); dir != "" {
		args = append(args, dir)
	}

	cmd := exec.CommandContext(ctx, "git", args...)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// pullRepository pulls changes from a Git repository
func (p *GitPlugin) pullRepository(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	remote := getStringParam(params, "remote", "origin")
	branch := getStringParam(params, "branch", "")

	args := []string{"pull", remote}
	if branch != "" {
		args = append(args, branch)
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	if dir := getStringParam(params, "directory", ""); dir != "" {
		cmd.Dir = dir
	}

	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// checkoutBranch checks out a Git branch
func (p *GitPlugin) checkoutBranch(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	branch := getStringParam(params, "branch", "")
	if branch == "" {
		return nil, fmt.Errorf("branch parameter is required")
	}

	args := []string{"checkout"}

	if create := getStringParam(params, "create", ""); create == "true" {
		args = append(args, "-b")
	}

	args = append(args, branch)

	cmd := exec.CommandContext(ctx, "git", args...)

	if dir := getStringParam(params, "directory", ""); dir != "" {
		cmd.Dir = dir
	}

	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// commitChanges commits changes to Git
func (p *GitPlugin) commitChanges(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	message := getStringParam(params, "message", "")
	if message == "" {
		return nil, fmt.Errorf("message parameter is required")
	}

	args := []string{"commit", "-m", message}

	if all := getStringParam(params, "all", ""); all == "true" {
		args = append(args, "-a")
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	if dir := getStringParam(params, "directory", ""); dir != "" {
		cmd.Dir = dir
	}

	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// pushChanges pushes changes to Git
func (p *GitPlugin) pushChanges(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	remote := getStringParam(params, "remote", "origin")
	branch := getStringParam(params, "branch", "")

	args := []string{"push", remote}
	if branch != "" {
		args = append(args, branch)
	}

	if force := getStringParam(params, "force", ""); force == "true" {
		args = append(args, "--force")
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	if dir := getStringParam(params, "directory", ""); dir != "" {
		cmd.Dir = dir
	}

	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// createTag creates a Git tag
func (p *GitPlugin) createTag(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	tag := getStringParam(params, "tag", "")
	if tag == "" {
		return nil, fmt.Errorf("tag parameter is required")
	}

	args := []string{"tag", tag}

	if message := getStringParam(params, "message", ""); message != "" {
		args = append(args, "-m", message)
	}

	cmd := exec.CommandContext(ctx, "git", args...)

	if dir := getStringParam(params, "directory", ""); dir != "" {
		cmd.Dir = dir
	}

	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// Cleanup performs cleanup operations
func (p *GitPlugin) Cleanup() error {
	// No cleanup needed for Git plugin
	return nil
}
