package builtin

import (
	"context"
	"fmt"
	"os/exec"
	"strconv"
	"time"
)

// getStringParam gets a string parameter with default value
func getStringParam(params map[string]interface{}, key, defaultValue string) string {
	if value, ok := params[key].(string); ok {
		return value
	}
	return defaultValue
}

// getBoolParam gets a boolean parameter with default value
func getBoolParam(params map[string]interface{}, key string, defaultValue bool) bool {
	if value, ok := params[key].(bool); ok {
		return value
	}
	if value, ok := params[key].(string); ok {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// getIntParam gets an integer parameter with default value
func getIntParam(params map[string]interface{}, key string, defaultValue int) int {
	if value, ok := params[key].(int); ok {
		return value
	}
	if value, ok := params[key].(float64); ok {
		return int(value)
	}
	if value, ok := params[key].(string); ok {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// getSliceParam gets a slice parameter with default value
func getSliceParam(params map[string]interface{}, key string, defaultValue []string) []string {
	if value, ok := params[key].([]interface{}); ok {
		result := make([]string, len(value))
		for i, v := range value {
			result[i] = fmt.Sprintf("%v", v)
		}
		return result
	}
	if value, ok := params[key].([]string); ok {
		return value
	}
	return defaultValue
}

// execCommandWithContext executes a command with context and timeout
func execCommandWithContext(ctx context.Context, name string, args ...string) *exec.Cmd {
	return exec.CommandContext(ctx, name, args...)
}

// execCommandWithTimeout executes a command with timeout
func execCommandWithTimeout(timeout time.Duration, name string, args ...string) *exec.Cmd {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	return exec.CommandContext(ctx, name, args...)
}

// validateRequiredParams validates that required parameters are present
func validateRequiredParams(params map[string]interface{}, required []string) error {
	for _, param := range required {
		if _, ok := params[param]; !ok {
			return fmt.Errorf("required parameter '%s' is missing", param)
		}
	}
	return nil
}

// mergeParams merges default parameters with provided parameters
func mergeParams(defaults, provided map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	
	// Copy defaults
	for k, v := range defaults {
		result[k] = v
	}
	
	// Override with provided
	for k, v := range provided {
		result[k] = v
	}
	
	return result
}
