package builtin

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"github.com/chainops/chainops/internal/plugins"
	"github.com/chainops/chainops/pkg/types"
)

// KubernetesPlugin implements Kubernetes operations
type KubernetesPlugin struct {
	config map[string]interface{}
}

// NewKubernetesPlugin creates a new Kubernetes plugin
func NewKubernetesPlugin() plugins.Plugin {
	return &KubernetesPlugin{}
}

// GetInfo returns plugin information
func (p *KubernetesPlugin) GetInfo() plugins.PluginInfo {
	return plugins.PluginInfo{
		Name:        "kubernetes",
		Version:     "1.0.0",
		Description: "Kubernetes operations plugin for deploying and managing resources",
		Author:      "ChainOps Team",
		Type:        plugins.PluginTypeStep,
		Tags:        []string{"kubernetes", "k8s", "deploy", "orchestration"},
		Config: map[string]string{
			"kubeconfig": "Path to kubeconfig file",
			"namespace":  "Default namespace",
			"context":    "Kubernetes context",
		},
	}
}

// Initialize initializes the plugin
func (p *KubernetesPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// Check if kubectl is available
	if err := exec.Command("kubectl", "version", "--client").Run(); err != nil {
		return fmt.Errorf("kubectl is not available: %w", err)
	}

	return nil
}

// Execute executes the plugin
func (p *KubernetesPlugin) Execute(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	action, ok := params["action"].(string)
	if !ok {
		return nil, fmt.Errorf("action parameter is required")
	}

	switch action {
	case "apply":
		return p.applyManifests(ctx, params)
	case "delete":
		return p.deleteResources(ctx, params)
	case "get":
		return p.getResources(ctx, params)
	case "rollout":
		return p.rolloutStatus(ctx, params)
	case "scale":
		return p.scaleResource(ctx, params)
	default:
		return nil, fmt.Errorf("unsupported action: %s", action)
	}
}

// ExecuteStep executes a pipeline step
func (p *KubernetesPlugin) ExecuteStep(ctx context.Context, step *types.PipelineStep, env map[string]string) (*plugins.StepResult, error) {
	startTime := time.Now()

	// Parse step configuration
	action := step.With["action"]
	if action == "" {
		return &plugins.StepResult{
			Success:  false,
			ExitCode: 1,
			Error:    "Kubernetes action not specified",
			Duration: time.Since(startTime).Milliseconds(),
		}, nil
	}

	var cmd *exec.Cmd
	var output strings.Builder

	switch action {
	case "apply":
		cmd = p.applyCommand(ctx, step, env)
	case "delete":
		cmd = p.deleteCommand(ctx, step, env)
	case "get":
		cmd = p.getCommand(ctx, step, env)
	case "rollout":
		cmd = p.rolloutCommand(ctx, step, env)
	case "scale":
		cmd = p.scaleCommand(ctx, step, env)
	default:
		return &plugins.StepResult{
			Success:  false,
			ExitCode: 1,
			Error:    fmt.Sprintf("Unsupported Kubernetes action: %s", action),
			Duration: time.Since(startTime).Milliseconds(),
		}, nil
	}

	// Set up command context and output capture
	cmd.Stdout = &output
	cmd.Stderr = &output

	// Execute command
	err := cmd.Run()
	duration := time.Since(startTime).Milliseconds()

	result := &plugins.StepResult{
		Success:  err == nil,
		Output:   output.String(),
		Duration: duration,
	}

	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			result.ExitCode = exitError.ExitCode()
		} else {
			result.ExitCode = 1
		}
		result.Error = err.Error()
	}

	return result, nil
}

// applyCommand creates a kubectl apply command
func (p *KubernetesPlugin) applyCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"apply"}

	// Add namespace
	if namespace := step.With["namespace"]; namespace != "" {
		args = append(args, "-n", namespace)
	}

	// Add manifests
	if manifests := step.With["manifests"]; manifests != "" {
		for _, manifest := range strings.Split(manifests, ",") {
			args = append(args, "-f", strings.TrimSpace(manifest))
		}
	}

	// Add inline manifest
	if manifest := step.With["manifest"]; manifest != "" {
		args = append(args, "-f", "-")
	}

	// Add dry-run option
	if dryRun := step.With["dry_run"]; dryRun == "true" {
		args = append(args, "--dry-run=client")
	}

	return exec.CommandContext(ctx, "kubectl", args...)
}

// deleteCommand creates a kubectl delete command
func (p *KubernetesPlugin) deleteCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"delete"}

	// Add namespace
	if namespace := step.With["namespace"]; namespace != "" {
		args = append(args, "-n", namespace)
	}

	// Add resource type and name
	if resourceType := step.With["resource_type"]; resourceType != "" {
		args = append(args, resourceType)
		if name := step.With["name"]; name != "" {
			args = append(args, name)
		}
	}

	// Add manifests
	if manifests := step.With["manifests"]; manifests != "" {
		for _, manifest := range strings.Split(manifests, ",") {
			args = append(args, "-f", strings.TrimSpace(manifest))
		}
	}

	// Add ignore not found
	if ignoreNotFound := step.With["ignore_not_found"]; ignoreNotFound == "true" {
		args = append(args, "--ignore-not-found")
	}

	return exec.CommandContext(ctx, "kubectl", args...)
}

// getCommand creates a kubectl get command
func (p *KubernetesPlugin) getCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"get"}

	// Add resource type
	if resourceType := step.With["resource_type"]; resourceType != "" {
		args = append(args, resourceType)
	}

	// Add namespace
	if namespace := step.With["namespace"]; namespace != "" {
		args = append(args, "-n", namespace)
	}

	// Add output format
	if output := step.With["output"]; output != "" {
		args = append(args, "-o", output)
	} else {
		args = append(args, "-o", "wide")
	}

	return exec.CommandContext(ctx, "kubectl", args...)
}

// rolloutCommand creates a kubectl rollout command
func (p *KubernetesPlugin) rolloutCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"rollout"}

	// Add rollout action
	if rolloutAction := step.With["rollout_action"]; rolloutAction != "" {
		args = append(args, rolloutAction)
	} else {
		args = append(args, "status")
	}

	// Add resource type and name
	if resourceType := step.With["resource_type"]; resourceType != "" {
		resource := resourceType
		if name := step.With["name"]; name != "" {
			resource = resourceType + "/" + name
		}
		args = append(args, resource)
	}

	// Add namespace
	if namespace := step.With["namespace"]; namespace != "" {
		args = append(args, "-n", namespace)
	}

	// Add timeout
	if timeout := step.With["timeout"]; timeout != "" {
		args = append(args, "--timeout", timeout)
	}

	return exec.CommandContext(ctx, "kubectl", args...)
}

// scaleCommand creates a kubectl scale command
func (p *KubernetesPlugin) scaleCommand(ctx context.Context, step *types.PipelineStep, env map[string]string) *exec.Cmd {
	args := []string{"scale"}

	// Add resource type and name
	if resourceType := step.With["resource_type"]; resourceType != "" {
		resource := resourceType
		if name := step.With["name"]; name != "" {
			resource = resourceType + "/" + name
		}
		args = append(args, resource)
	}

	// Add replicas
	if replicas := step.With["replicas"]; replicas != "" {
		args = append(args, "--replicas", replicas)
	}

	// Add namespace
	if namespace := step.With["namespace"]; namespace != "" {
		args = append(args, "-n", namespace)
	}

	return exec.CommandContext(ctx, "kubectl", args...)
}

// applyManifests applies Kubernetes manifests
func (p *KubernetesPlugin) applyManifests(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	manifests := getStringParam(params, "manifests", "")
	namespace := getStringParam(params, "namespace", "default")

	if manifests == "" {
		return nil, fmt.Errorf("manifests parameter is required")
	}

	args := []string{"apply", "-n", namespace}
	for _, manifest := range strings.Split(manifests, ",") {
		args = append(args, "-f", strings.TrimSpace(manifest))
	}

	cmd := exec.CommandContext(ctx, "kubectl", args...)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// deleteResources deletes Kubernetes resources
func (p *KubernetesPlugin) deleteResources(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	resourceType := getStringParam(params, "resource_type", "")
	name := getStringParam(params, "name", "")
	namespace := getStringParam(params, "namespace", "default")

	if resourceType == "" {
		return nil, fmt.Errorf("resource_type parameter is required")
	}

	args := []string{"delete", resourceType}
	if name != "" {
		args = append(args, name)
	}
	args = append(args, "-n", namespace)

	cmd := exec.CommandContext(ctx, "kubectl", args...)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// getResources gets Kubernetes resources
func (p *KubernetesPlugin) getResources(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	resourceType := getStringParam(params, "resource_type", "pods")
	namespace := getStringParam(params, "namespace", "default")
	output := getStringParam(params, "output", "wide")

	args := []string{"get", resourceType, "-n", namespace, "-o", output}

	cmd := exec.CommandContext(ctx, "kubectl", args...)
	cmdOutput, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(cmdOutput),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// rolloutStatus checks rollout status
func (p *KubernetesPlugin) rolloutStatus(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	resourceType := getStringParam(params, "resource_type", "deployment")
	name := getStringParam(params, "name", "")
	namespace := getStringParam(params, "namespace", "default")

	if name == "" {
		return nil, fmt.Errorf("name parameter is required")
	}

	args := []string{"rollout", "status", resourceType + "/" + name, "-n", namespace}

	cmd := exec.CommandContext(ctx, "kubectl", args...)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// scaleResource scales a Kubernetes resource
func (p *KubernetesPlugin) scaleResource(ctx context.Context, params map[string]interface{}) (*plugins.PluginResult, error) {
	resourceType := getStringParam(params, "resource_type", "deployment")
	name := getStringParam(params, "name", "")
	replicas := getStringParam(params, "replicas", "")
	namespace := getStringParam(params, "namespace", "default")

	if name == "" || replicas == "" {
		return nil, fmt.Errorf("name and replicas parameters are required")
	}

	args := []string{"scale", resourceType + "/" + name, "--replicas", replicas, "-n", namespace}

	cmd := exec.CommandContext(ctx, "kubectl", args...)
	output, err := cmd.CombinedOutput()

	result := &plugins.PluginResult{
		Success: err == nil,
		Output:  string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result, nil
}

// Cleanup performs cleanup operations
func (p *KubernetesPlugin) Cleanup() error {
	// No cleanup needed for Kubernetes plugin
	return nil
}
