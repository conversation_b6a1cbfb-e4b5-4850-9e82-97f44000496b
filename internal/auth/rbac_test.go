package auth

import (
	"context"
	"testing"

	"github.com/chainops/chainops/pkg/types"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockRoleRepository is a mock implementation of RoleRepository
type MockRoleRepository struct {
	mock.Mock
}

func (m *MockRoleRepository) Create(role *types.Role) error {
	args := m.Called(role)
	return args.Error(0)
}

func (m *MockRoleRepository) GetByID(id uuid.UUID) (*types.Role, error) {
	args := m.Called(id)
	return args.Get(0).(*types.Role), args.Error(1)
}

func (m *MockRoleRepository) Update(role *types.Role) error {
	args := m.Called(role)
	return args.Error(0)
}

func (m *MockRoleRepository) Delete(id uuid.UUID) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockRoleRepository) List() ([]*types.Role, error) {
	args := m.Called()
	return args.Get(0).([]*types.Role), args.Error(1)
}

func (m *MockRoleRepository) GetByName(name string) (*types.Role, error) {
	args := m.Called(name)
	return args.Get(0).(*types.Role), args.Error(1)
}

// MockPermissionRepository is a mock implementation of PermissionRepository
type MockPermissionRepository struct {
	mock.Mock
}

func (m *MockPermissionRepository) Create(permission *types.Permission) error {
	args := m.Called(permission)
	return args.Error(0)
}

func (m *MockPermissionRepository) GetByID(id uuid.UUID) (*types.Permission, error) {
	args := m.Called(id)
	return args.Get(0).(*types.Permission), args.Error(1)
}

func (m *MockPermissionRepository) List() ([]*types.Permission, error) {
	args := m.Called()
	return args.Get(0).([]*types.Permission), args.Error(1)
}

func (m *MockPermissionRepository) GetByResourceAction(resource, action string) (*types.Permission, error) {
	args := m.Called(resource, action)
	return args.Get(0).(*types.Permission), args.Error(1)
}

// MockUserRoleRepository is a mock implementation of UserRoleRepository
type MockUserRoleRepository struct {
	mock.Mock
}

func (m *MockUserRoleRepository) Create(userRole *types.UserRole) error {
	args := m.Called(userRole)
	return args.Error(0)
}

func (m *MockUserRoleRepository) GetByUserID(userID uuid.UUID) ([]*types.UserRole, error) {
	args := m.Called(userID)
	return args.Get(0).([]*types.UserRole), args.Error(1)
}

func (m *MockUserRoleRepository) DeleteByUserAndRole(userID, roleID uuid.UUID) error {
	args := m.Called(userID, roleID)
	return args.Error(0)
}

func TestRBACManager_CreateRole(t *testing.T) {
	// Setup
	mockRoleRepo := new(MockRoleRepository)
	mockPermRepo := new(MockPermissionRepository)
	mockUserRoleRepo := new(MockUserRoleRepository)

	manager := &RBACManager{
		roleRepo:     mockRoleRepo,
		permRepo:     mockPermRepo,
		userRoleRepo: mockUserRoleRepo,
		logger:       nil,
	}

	// Test data
	req := CreateRoleRequest{
		Name:        "Developer",
		Description: stringPtr("Can create and execute pipelines"),
	}

	// Mock expectations
	mockRoleRepo.On("Create", mock.AnythingOfType("*types.Role")).Return(nil)

	// Execute
	ctx := context.Background()
	role, err := manager.CreateRole(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, role)
	assert.Equal(t, "Developer", role.Name)
	assert.Equal(t, "Can create and execute pipelines", role.Description)

	// Verify mocks
	mockRoleRepo.AssertExpectations(t)
}

func TestRBACManager_AssignRole(t *testing.T) {
	// Setup
	mockRoleRepo := new(MockRoleRepository)
	mockUserRoleRepo := new(MockUserRoleRepository)

	manager := &RBACManager{
		roleRepo:     mockRoleRepo,
		userRoleRepo: mockUserRoleRepo,
		logger:       nil,
	}

	// Test data
	userID := uuid.New()
	roleID := uuid.New()
	role := &types.Role{
		ID:   roleID,
		Name: "Developer",
	}

	// Mock expectations
	mockRoleRepo.On("GetByID", roleID).Return(role, nil)
	mockUserRoleRepo.On("Create", mock.AnythingOfType("*types.UserRole")).Return(nil)

	// Execute
	ctx := context.Background()
	err := manager.AssignRole(ctx, userID, roleID)

	// Assert
	assert.NoError(t, err)

	// Verify mocks
	mockRoleRepo.AssertExpectations(t)
	mockUserRoleRepo.AssertExpectations(t)
}

func TestRBACManager_CheckPermission(t *testing.T) {
	// Setup
	mockUserRoleRepo := new(MockUserRoleRepository)

	manager := &RBACManager{
		userRoleRepo: mockUserRoleRepo,
		logger:       nil,
	}

	// Test data
	userID := uuid.New()
	userRoles := []*types.UserRole{
		{
			UserID: userID,
			Role: &types.Role{
				Name: "Developer",
				Permissions: []types.Permission{
					{
						ID:       uuid.New(),
						Resource: "pipeline",
						Action:   "create",
					},
					{
						ID:       uuid.New(),
						Resource: "pipeline",
						Action:   "execute",
					},
				},
			},
		},
	}

	// Mock expectations
	mockUserRoleRepo.On("GetByUserID", userID).Return(userRoles, nil)

	// Execute
	ctx := context.Background()
	hasPermission, err := manager.CheckPermission(ctx, userID, "pipeline", "create")

	// Assert
	assert.NoError(t, err)
	assert.True(t, hasPermission)

	// Test permission that user doesn't have
	hasPermission, err = manager.CheckPermission(ctx, userID, "pipeline", "delete")
	assert.NoError(t, err)
	assert.False(t, hasPermission)

	// Verify mocks
	mockUserRoleRepo.AssertExpectations(t)
}

func TestRBACManager_GetUserRoles(t *testing.T) {
	// Setup
	mockUserRoleRepo := new(MockUserRoleRepository)

	manager := &RBACManager{
		userRoleRepo: mockUserRoleRepo,
		logger:       nil,
	}

	// Test data
	userID := uuid.New()
	userRoles := []*types.UserRole{
		{
			UserID: userID,
			Role: &types.Role{
				Name: "Developer",
			},
		},
		{
			UserID: userID,
			Role: &types.Role{
				Name: "Reviewer",
			},
		},
	}

	// Mock expectations
	mockUserRoleRepo.On("GetByUserID", userID).Return(userRoles, nil)

	// Execute
	ctx := context.Background()
	roles, err := manager.GetUserRoles(ctx, userID)

	// Assert
	assert.NoError(t, err)
	assert.Len(t, roles, 2)
	assert.Equal(t, "Developer", roles[0].Name)
	assert.Equal(t, "Reviewer", roles[1].Name)

	// Verify mocks
	mockUserRoleRepo.AssertExpectations(t)
}

func TestRBACManager_CreatePermission(t *testing.T) {
	// Setup
	mockPermRepo := new(MockPermissionRepository)

	manager := &RBACManager{
		permRepo: mockPermRepo,
		logger:   nil,
	}

	// Test data
	req := CreatePermissionRequest{
		Name:        "Create Pipeline",
		Description: stringPtr("Allows creating new pipelines"),
		Resource:    "pipeline",
		Action:      "create",
	}

	// Mock expectations
	mockPermRepo.On("Create", mock.AnythingOfType("*types.Permission")).Return(nil)

	// Execute
	ctx := context.Background()
	permission, err := manager.CreatePermission(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, permission)
	assert.Equal(t, "Create Pipeline", permission.Name)
	assert.Equal(t, "pipeline", permission.Resource)
	assert.Equal(t, "create", permission.Action)

	// Verify mocks
	mockPermRepo.AssertExpectations(t)
}

// Helper function for string pointers
func stringPtr(s string) *string {
	return &s
}
