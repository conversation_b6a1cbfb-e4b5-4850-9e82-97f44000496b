package auth

import (
	"context"
	"fmt"

	"github.com/chainops/chainops/internal/storage"
	"github.com/chainops/chainops/pkg/types"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// RBACManager handles role-based access control
type RBACManager struct {
	logger         *logrus.Logger
	roleRepo       *storage.RoleRepository
	permissionRepo *storage.PermissionRepository
	userRoleRepo   *storage.UserRoleRepository
}

// NewRBACManager creates a new RBAC manager
func NewRBACManager(
	logger *logrus.Logger,
	roleRepo *storage.RoleRepository,
	permissionRepo *storage.PermissionRepository,
	userRoleRepo *storage.UserRoleRepository,
) *RBACManager {
	return &RBACManager{
		logger:         logger,
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
		userRoleRepo:   userRoleRepo,
	}
}

// CreateRole creates a new role
func (m *RBACManager) CreateRole(ctx context.Context, req CreateRoleRequest) (*types.Role, error) {
	role := &types.Role{
		OrganizationID: req.OrganizationID,
		Name:           req.Name,
		Description:    req.Description,
		IsSystem:       false,
		IsDefault:      req.IsDefault,
	}

	if err := m.roleRepo.Create(role); err != nil {
		m.logger.WithError(err).WithField("role_name", req.Name).Error("Failed to create role")
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"role_id":   role.ID,
		"role_name": role.Name,
	}).Info("Role created successfully")

	return role, nil
}

// GetRole retrieves a role by ID
func (m *RBACManager) GetRole(ctx context.Context, id uuid.UUID) (*types.Role, error) {
	role, err := m.roleRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	return role, nil
}

// UpdateRole updates an existing role
func (m *RBACManager) UpdateRole(ctx context.Context, id uuid.UUID, req UpdateRoleRequest) (*types.Role, error) {
	role, err := m.roleRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("role not found: %w", err)
	}

	if role.IsSystem {
		return nil, fmt.Errorf("cannot update system role")
	}

	if req.Name != nil {
		role.Name = *req.Name
	}
	if req.Description != nil {
		role.Description = req.Description
	}
	if req.IsDefault != nil {
		role.IsDefault = *req.IsDefault
	}

	if err := m.roleRepo.Update(role); err != nil {
		m.logger.WithError(err).WithField("role_id", id).Error("Failed to update role")
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"role_id":   role.ID,
		"role_name": role.Name,
	}).Info("Role updated successfully")

	return role, nil
}

// DeleteRole deletes a role
func (m *RBACManager) DeleteRole(ctx context.Context, id uuid.UUID) error {
	role, err := m.roleRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("role not found: %w", err)
	}

	if role.IsSystem {
		return fmt.Errorf("cannot delete system role")
	}

	if err := m.roleRepo.Delete(id); err != nil {
		m.logger.WithError(err).WithField("role_id", id).Error("Failed to delete role")
		return fmt.Errorf("failed to delete role: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"role_id":   role.ID,
		"role_name": role.Name,
	}).Info("Role deleted successfully")

	return nil
}

// ListRoles lists all roles
func (m *RBACManager) ListRoles(ctx context.Context) ([]*types.Role, error) {
	roles, err := m.roleRepo.List()
	if err != nil {
		return nil, fmt.Errorf("failed to list roles: %w", err)
	}
	return roles, nil
}

// CreatePermission creates a new permission
func (m *RBACManager) CreatePermission(ctx context.Context, req CreatePermissionRequest) (*types.Permission, error) {
	permission := &types.Permission{
		Name:        req.Name,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
	}

	if err := m.permissionRepo.Create(permission); err != nil {
		m.logger.WithError(err).WithField("permission_name", req.Name).Error("Failed to create permission")
		return nil, fmt.Errorf("failed to create permission: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"permission_id":   permission.ID,
		"permission_name": permission.Name,
	}).Info("Permission created successfully")

	return permission, nil
}

// GetPermission retrieves a permission by ID
func (m *RBACManager) GetPermission(ctx context.Context, id uuid.UUID) (*types.Permission, error) {
	permission, err := m.permissionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get permission: %w", err)
	}
	return permission, nil
}

// ListPermissions lists all permissions
func (m *RBACManager) ListPermissions(ctx context.Context) ([]*types.Permission, error) {
	permissions, err := m.permissionRepo.List()
	if err != nil {
		return nil, fmt.Errorf("failed to list permissions: %w", err)
	}
	return permissions, nil
}

// AssignRole assigns a role to a user
func (m *RBACManager) AssignRole(ctx context.Context, userID, roleID uuid.UUID) error {
	// Check if role exists
	_, err := m.roleRepo.GetByID(roleID)
	if err != nil {
		return fmt.Errorf("role not found: %w", err)
	}

	if err := m.userRoleRepo.AssignRole(userID, roleID); err != nil {
		m.logger.WithError(err).WithFields(logrus.Fields{
			"user_id": userID,
			"role_id": roleID,
		}).Error("Failed to assign role to user")
		return fmt.Errorf("failed to assign role: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"role_id": roleID,
	}).Info("Role assigned to user successfully")

	return nil
}

// RevokeRole revokes a role from a user
func (m *RBACManager) RevokeRole(ctx context.Context, userID, roleID uuid.UUID) error {
	if err := m.userRoleRepo.RevokeRole(userID, roleID); err != nil {
		m.logger.WithError(err).WithFields(logrus.Fields{
			"user_id": userID,
			"role_id": roleID,
		}).Error("Failed to revoke role from user")
		return fmt.Errorf("failed to revoke role: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"role_id": roleID,
	}).Info("Role revoked from user successfully")

	return nil
}

// GetUserRoles retrieves all roles for a user
func (m *RBACManager) GetUserRoles(ctx context.Context, userID uuid.UUID) ([]*types.Role, error) {
	roles, err := m.userRoleRepo.GetUserRoles(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}
	return roles, nil
}

// GetUserPermissions retrieves all permissions for a user
func (m *RBACManager) GetUserPermissions(ctx context.Context, userID uuid.UUID) ([]*types.Permission, error) {
	permissions, err := m.userRoleRepo.GetUserPermissions(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}
	return permissions, nil
}

// HasPermission checks if a user has a specific permission
func (m *RBACManager) HasPermission(ctx context.Context, userID uuid.UUID, resource, action string) (bool, error) {
	permissions, err := m.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	for _, permission := range permissions {
		if permission.Resource == resource && permission.Action == action {
			return true, nil
		}
	}

	return false, nil
}

// Request types
type CreateRoleRequest struct {
	OrganizationID *uuid.UUID `json:"organization_id"`
	Name           string     `json:"name" binding:"required"`
	Description    *string    `json:"description"`
	IsDefault      bool       `json:"is_default"`
}

type UpdateRoleRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	IsDefault   *bool   `json:"is_default"`
}

type CreatePermissionRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description"`
	Resource    string  `json:"resource" binding:"required"`
	Action      string  `json:"action" binding:"required"`
}
