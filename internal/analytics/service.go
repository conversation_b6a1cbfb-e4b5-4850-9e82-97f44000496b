package analytics

import (
	"context"
	"fmt"
	"time"

	"github.com/chainops/chainops/pkg/types"
	"github.com/sirupsen/logrus"
)

// Service provides analytics and reporting functionality
type Service struct {
	metricsRepo MetricsRepository
	logger      *logrus.Logger
}

// MetricsRepository interface for metrics data access
type MetricsRepository interface {
	RecordPipelineExecution(ctx context.Context, execution *types.PipelineExecution) error
	RecordJobExecution(ctx context.Context, job *types.Job) error
	RecordDeployment(ctx context.Context, deployment *types.Deployment) error
	GetPipelineMetrics(ctx context.Context, filter MetricsFilter) (*PipelineMetrics, error)
	GetJobMetrics(ctx context.Context, filter MetricsFilter) (*JobMetrics, error)
	GetDeploymentMetrics(ctx context.Context, filter MetricsFilter) (*DeploymentMetrics, error)
	GetResourceUsage(ctx context.Context, filter MetricsFilter) (*ResourceUsage, error)
	GetTrendData(ctx context.Context, filter MetricsFilter) (*TrendData, error)
}

// MetricsFilter represents filtering criteria for metrics queries
type MetricsFilter struct {
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
	PipelineID   string    `json:"pipeline_id,omitempty"`
	Repository   string    `json:"repository,omitempty"`
	Branch       string    `json:"branch,omitempty"`
	Environment  string    `json:"environment,omitempty"`
	Status       string    `json:"status,omitempty"`
	UserID       string    `json:"user_id,omitempty"`
	Granularity  string    `json:"granularity"` // hour, day, week, month
}

// PipelineMetrics represents pipeline execution metrics
type PipelineMetrics struct {
	TotalExecutions    int64                    `json:"total_executions"`
	SuccessfulExecutions int64                  `json:"successful_executions"`
	FailedExecutions   int64                    `json:"failed_executions"`
	SuccessRate        float64                  `json:"success_rate"`
	AverageDuration    time.Duration            `json:"average_duration"`
	MedianDuration     time.Duration            `json:"median_duration"`
	P95Duration        time.Duration            `json:"p95_duration"`
	ExecutionsByStatus map[string]int64         `json:"executions_by_status"`
	ExecutionsByBranch map[string]int64         `json:"executions_by_branch"`
	DailyExecutions    []DailyMetric            `json:"daily_executions"`
	TopFailureReasons  []FailureReason          `json:"top_failure_reasons"`
}

// JobMetrics represents job execution metrics
type JobMetrics struct {
	TotalJobs          int64                    `json:"total_jobs"`
	SuccessfulJobs     int64                    `json:"successful_jobs"`
	FailedJobs         int64                    `json:"failed_jobs"`
	SuccessRate        float64                  `json:"success_rate"`
	AverageDuration    time.Duration            `json:"average_duration"`
	JobsByStage        map[string]int64         `json:"jobs_by_stage"`
	JobsByStatus       map[string]int64         `json:"jobs_by_status"`
	SlowestJobs        []JobPerformance         `json:"slowest_jobs"`
	MostFailedJobs     []JobFailure             `json:"most_failed_jobs"`
}

// DeploymentMetrics represents deployment metrics
type DeploymentMetrics struct {
	TotalDeployments     int64                    `json:"total_deployments"`
	SuccessfulDeployments int64                   `json:"successful_deployments"`
	FailedDeployments    int64                    `json:"failed_deployments"`
	SuccessRate          float64                  `json:"success_rate"`
	AverageDuration      time.Duration            `json:"average_duration"`
	DeploymentsByEnv     map[string]int64         `json:"deployments_by_environment"`
	DeploymentsByStrategy map[string]int64        `json:"deployments_by_strategy"`
	RollbackRate         float64                  `json:"rollback_rate"`
	MTTR                 time.Duration            `json:"mttr"` // Mean Time To Recovery
	ChangeFailureRate    float64                  `json:"change_failure_rate"`
}

// ResourceUsage represents resource consumption metrics
type ResourceUsage struct {
	TotalCPUHours      float64                  `json:"total_cpu_hours"`
	TotalMemoryGBHours float64                  `json:"total_memory_gb_hours"`
	TotalStorageGB     float64                  `json:"total_storage_gb"`
	CostEstimate       float64                  `json:"cost_estimate"`
	UsageByPipeline    map[string]ResourceMetric `json:"usage_by_pipeline"`
	UsageByEnvironment map[string]ResourceMetric `json:"usage_by_environment"`
	DailyUsage         []DailyResourceUsage     `json:"daily_usage"`
}

// TrendData represents trend analysis data
type TrendData struct {
	ExecutionTrend     []TrendPoint `json:"execution_trend"`
	SuccessRateTrend   []TrendPoint `json:"success_rate_trend"`
	DurationTrend      []TrendPoint `json:"duration_trend"`
	FailureRateTrend   []TrendPoint `json:"failure_rate_trend"`
	ResourceUsageTrend []TrendPoint `json:"resource_usage_trend"`
}

// Supporting types
type DailyMetric struct {
	Date       time.Time `json:"date"`
	Count      int64     `json:"count"`
	Successful int64     `json:"successful"`
	Failed     int64     `json:"failed"`
}

type FailureReason struct {
	Reason string `json:"reason"`
	Count  int64  `json:"count"`
}

type JobPerformance struct {
	JobName     string        `json:"job_name"`
	PipelineID  string        `json:"pipeline_id"`
	Duration    time.Duration `json:"duration"`
	ExecutionID string        `json:"execution_id"`
}

type JobFailure struct {
	JobName     string `json:"job_name"`
	PipelineID  string `json:"pipeline_id"`
	FailureCount int64  `json:"failure_count"`
	LastFailure time.Time `json:"last_failure"`
}

type ResourceMetric struct {
	CPUHours      float64 `json:"cpu_hours"`
	MemoryGBHours float64 `json:"memory_gb_hours"`
	StorageGB     float64 `json:"storage_gb"`
	Cost          float64 `json:"cost"`
}

type DailyResourceUsage struct {
	Date          time.Time `json:"date"`
	CPUHours      float64   `json:"cpu_hours"`
	MemoryGBHours float64   `json:"memory_gb_hours"`
	StorageGB     float64   `json:"storage_gb"`
	Cost          float64   `json:"cost"`
}

type TrendPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
}

// NewService creates a new analytics service
func NewService(metricsRepo MetricsRepository, logger *logrus.Logger) *Service {
	return &Service{
		metricsRepo: metricsRepo,
		logger:      logger,
	}
}

// RecordPipelineExecution records a pipeline execution for analytics
func (s *Service) RecordPipelineExecution(ctx context.Context, execution *types.PipelineExecution) error {
	if err := s.metricsRepo.RecordPipelineExecution(ctx, execution); err != nil {
		s.logger.WithError(err).WithField("execution_id", execution.ID).Error("Failed to record pipeline execution")
		return fmt.Errorf("failed to record pipeline execution: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"execution_id": execution.ID,
		"pipeline_id":  execution.PipelineID,
		"status":       execution.Status,
		"duration":     execution.Duration,
	}).Debug("Pipeline execution recorded")

	return nil
}

// RecordJobExecution records a job execution for analytics
func (s *Service) RecordJobExecution(ctx context.Context, job *types.Job) error {
	if err := s.metricsRepo.RecordJobExecution(ctx, job); err != nil {
		s.logger.WithError(err).WithField("job_id", job.ID).Error("Failed to record job execution")
		return fmt.Errorf("failed to record job execution: %w", err)
	}

	return nil
}

// RecordDeployment records a deployment for analytics
func (s *Service) RecordDeployment(ctx context.Context, deployment *types.Deployment) error {
	if err := s.metricsRepo.RecordDeployment(ctx, deployment); err != nil {
		s.logger.WithError(err).WithField("deployment_id", deployment.ID).Error("Failed to record deployment")
		return fmt.Errorf("failed to record deployment: %w", err)
	}

	return nil
}

// GetPipelineMetrics retrieves pipeline metrics
func (s *Service) GetPipelineMetrics(ctx context.Context, filter MetricsFilter) (*PipelineMetrics, error) {
	metrics, err := s.metricsRepo.GetPipelineMetrics(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get pipeline metrics")
		return nil, fmt.Errorf("failed to get pipeline metrics: %w", err)
	}

	// Calculate derived metrics
	if metrics.TotalExecutions > 0 {
		metrics.SuccessRate = float64(metrics.SuccessfulExecutions) / float64(metrics.TotalExecutions) * 100
	}

	return metrics, nil
}

// GetJobMetrics retrieves job metrics
func (s *Service) GetJobMetrics(ctx context.Context, filter MetricsFilter) (*JobMetrics, error) {
	metrics, err := s.metricsRepo.GetJobMetrics(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get job metrics")
		return nil, fmt.Errorf("failed to get job metrics: %w", err)
	}

	// Calculate derived metrics
	if metrics.TotalJobs > 0 {
		metrics.SuccessRate = float64(metrics.SuccessfulJobs) / float64(metrics.TotalJobs) * 100
	}

	return metrics, nil
}

// GetDeploymentMetrics retrieves deployment metrics
func (s *Service) GetDeploymentMetrics(ctx context.Context, filter MetricsFilter) (*DeploymentMetrics, error) {
	metrics, err := s.metricsRepo.GetDeploymentMetrics(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get deployment metrics")
		return nil, fmt.Errorf("failed to get deployment metrics: %w", err)
	}

	// Calculate derived metrics
	if metrics.TotalDeployments > 0 {
		metrics.SuccessRate = float64(metrics.SuccessfulDeployments) / float64(metrics.TotalDeployments) * 100
		metrics.ChangeFailureRate = float64(metrics.FailedDeployments) / float64(metrics.TotalDeployments) * 100
	}

	return metrics, nil
}

// GetResourceUsage retrieves resource usage metrics
func (s *Service) GetResourceUsage(ctx context.Context, filter MetricsFilter) (*ResourceUsage, error) {
	usage, err := s.metricsRepo.GetResourceUsage(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get resource usage")
		return nil, fmt.Errorf("failed to get resource usage: %w", err)
	}

	return usage, nil
}

// GetTrendData retrieves trend analysis data
func (s *Service) GetTrendData(ctx context.Context, filter MetricsFilter) (*TrendData, error) {
	trends, err := s.metricsRepo.GetTrendData(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get trend data")
		return nil, fmt.Errorf("failed to get trend data: %w", err)
	}

	return trends, nil
}

// GetDashboardData retrieves comprehensive dashboard data
func (s *Service) GetDashboardData(ctx context.Context, filter MetricsFilter) (*DashboardData, error) {
	// Get all metrics in parallel
	pipelineMetrics, err := s.GetPipelineMetrics(ctx, filter)
	if err != nil {
		return nil, err
	}

	jobMetrics, err := s.GetJobMetrics(ctx, filter)
	if err != nil {
		return nil, err
	}

	deploymentMetrics, err := s.GetDeploymentMetrics(ctx, filter)
	if err != nil {
		return nil, err
	}

	resourceUsage, err := s.GetResourceUsage(ctx, filter)
	if err != nil {
		return nil, err
	}

	trendData, err := s.GetTrendData(ctx, filter)
	if err != nil {
		return nil, err
	}

	return &DashboardData{
		PipelineMetrics:   pipelineMetrics,
		JobMetrics:        jobMetrics,
		DeploymentMetrics: deploymentMetrics,
		ResourceUsage:     resourceUsage,
		TrendData:         trendData,
		GeneratedAt:       time.Now(),
	}, nil
}

// DashboardData represents comprehensive dashboard data
type DashboardData struct {
	PipelineMetrics   *PipelineMetrics   `json:"pipeline_metrics"`
	JobMetrics        *JobMetrics        `json:"job_metrics"`
	DeploymentMetrics *DeploymentMetrics `json:"deployment_metrics"`
	ResourceUsage     *ResourceUsage     `json:"resource_usage"`
	TrendData         *TrendData         `json:"trend_data"`
	GeneratedAt       time.Time          `json:"generated_at"`
}
