package notifications

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/chainops/chainops/pkg/types"
	"github.com/sirupsen/logrus"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	NotificationEmail   NotificationType = "email"
	NotificationSlack   NotificationType = "slack"
	NotificationWebhook NotificationType = "webhook"
	NotificationDiscord NotificationType = "discord"
	NotificationTeams   NotificationType = "teams"
)

// NotificationLevel represents the severity level
type NotificationLevel string

const (
	LevelInfo    NotificationLevel = "info"
	LevelWarning NotificationLevel = "warning"
	LevelError   NotificationLevel = "error"
	LevelSuccess NotificationLevel = "success"
)

// NotificationEvent represents different events that can trigger notifications
type NotificationEvent string

const (
	EventPipelineStarted   NotificationEvent = "pipeline.started"
	EventPipelineCompleted NotificationEvent = "pipeline.completed"
	EventPipelineFailed    NotificationEvent = "pipeline.failed"
	EventJobStarted        NotificationEvent = "job.started"
	EventJobCompleted      NotificationEvent = "job.completed"
	EventJobFailed         NotificationEvent = "job.failed"
	EventDeploymentStarted NotificationEvent = "deployment.started"
	EventDeploymentSuccess NotificationEvent = "deployment.success"
	EventDeploymentFailed  NotificationEvent = "deployment.failed"
)

// Notification represents a notification message
type Notification struct {
	ID         string                 `json:"id"`
	Type       NotificationType       `json:"type"`
	Level      NotificationLevel      `json:"level"`
	Event      NotificationEvent      `json:"event"`
	Title      string                 `json:"title"`
	Message    string                 `json:"message"`
	Recipients []string               `json:"recipients"`
	Metadata   map[string]interface{} `json:"metadata"`
	Timestamp  time.Time              `json:"timestamp"`
	Retries    int                    `json:"retries"`
	MaxRetries int                    `json:"max_retries"`
}

// NotificationProvider represents a notification provider interface
type NotificationProvider interface {
	Send(ctx context.Context, notification *Notification) error
	GetType() NotificationType
	IsEnabled() bool
}

// NotificationManager manages notifications
type NotificationManager struct {
	providers map[NotificationType]NotificationProvider
	config    NotificationConfig
	logger    *logrus.Logger
}

// NotificationConfig represents notification configuration
type NotificationConfig struct {
	Enabled   bool                            `yaml:"enabled"`
	Providers map[string]ProviderConfig       `yaml:"providers"`
	Rules     []NotificationRule              `yaml:"rules"`
	Templates map[string]NotificationTemplate `yaml:"templates"`
}

// ProviderConfig represents provider-specific configuration
type ProviderConfig struct {
	Type    string                 `yaml:"type"`
	Enabled bool                   `yaml:"enabled"`
	Config  map[string]interface{} `yaml:"config"`
}

// NotificationRule represents notification routing rules
type NotificationRule struct {
	Name       string              `yaml:"name"`
	Events     []NotificationEvent `yaml:"events"`
	Levels     []NotificationLevel `yaml:"levels"`
	Providers  []string            `yaml:"providers"`
	Recipients []string            `yaml:"recipients"`
	Conditions map[string]string   `yaml:"conditions"`
}

// NotificationTemplate represents a notification template
type NotificationTemplate struct {
	Title   string `yaml:"title"`
	Message string `yaml:"message"`
}

// NewNotificationManager creates a new notification manager
func NewNotificationManager(config NotificationConfig, logger *logrus.Logger) *NotificationManager {
	manager := &NotificationManager{
		providers: make(map[NotificationType]NotificationProvider),
		config:    config,
		logger:    logger,
	}

	// Initialize providers
	for name, providerConfig := range config.Providers {
		if !providerConfig.Enabled {
			continue
		}

		provider, err := manager.createProvider(providerConfig)
		if err != nil {
			logger.WithError(err).WithField("provider", name).Error("Failed to create notification provider")
			continue
		}

		manager.providers[provider.GetType()] = provider
		logger.WithField("provider", name).Info("Notification provider initialized")
	}

	return manager
}

// createProvider creates a notification provider
func (nm *NotificationManager) createProvider(config ProviderConfig) (NotificationProvider, error) {
	switch NotificationType(config.Type) {
	case NotificationSlack:
		return NewSlackProvider(config.Config)
	case NotificationEmail:
		return NewEmailProvider(config.Config)
	case NotificationWebhook:
		return NewWebhookProvider(config.Config)
	case NotificationDiscord:
		return NewDiscordProvider(config.Config)
	case NotificationTeams:
		return NewTeamsProvider(config.Config)
	default:
		return nil, fmt.Errorf("unsupported notification provider: %s", config.Type)
	}
}

// SendNotification sends a notification
func (nm *NotificationManager) SendNotification(ctx context.Context, notification *Notification) error {
	if !nm.config.Enabled {
		return nil
	}

	// Apply notification rules
	rules := nm.getMatchingRules(notification)
	if len(rules) == 0 {
		nm.logger.WithField("event", notification.Event).Debug("No matching notification rules")
		return nil
	}

	// Send to all matching providers
	var errors []error
	for _, rule := range rules {
		for _, providerName := range rule.Providers {
			provider, exists := nm.providers[NotificationType(providerName)]
			if !exists || !provider.IsEnabled() {
				continue
			}

			// Create notification copy with rule-specific recipients
			notificationCopy := *notification
			if len(rule.Recipients) > 0 {
				notificationCopy.Recipients = rule.Recipients
			}

			if err := provider.Send(ctx, &notificationCopy); err != nil {
				errors = append(errors, fmt.Errorf("provider %s: %w", providerName, err))
				nm.logger.WithError(err).WithField("provider", providerName).Error("Failed to send notification")
			}
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("notification errors: %v", errors)
	}

	return nil
}

// SendPipelineNotification sends a pipeline-related notification
func (nm *NotificationManager) SendPipelineNotification(
	ctx context.Context,
	event NotificationEvent,
	pipeline *types.Pipeline,
	execution *types.PipelineExecution,
) error {
	level := nm.getLevelForEvent(event)
	title, message := nm.buildPipelineMessage(event, pipeline, execution)

	notification := &Notification{
		ID:        fmt.Sprintf("pipeline-%s-%s", execution.ID, event),
		Type:      NotificationSlack, // Default type, will be overridden by rules
		Level:     level,
		Event:     event,
		Title:     title,
		Message:   message,
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"pipeline_id":   pipeline.ID,
			"pipeline_name": pipeline.Name,
			"execution_id":  execution.ID,
			"status":        execution.Status,
			"repository":    pipeline.Repository,
			"branch":        execution.Branch,
		},
	}

	return nm.SendNotification(ctx, notification)
}

// SendJobNotification sends a job-related notification
func (nm *NotificationManager) SendJobNotification(
	ctx context.Context,
	event NotificationEvent,
	job *types.Job,
	execution *types.PipelineExecution,
) error {
	level := nm.getLevelForEvent(event)
	title, message := nm.buildJobMessage(event, job, execution)

	notification := &Notification{
		ID:        fmt.Sprintf("job-%s-%s", job.ID, event),
		Type:      NotificationSlack,
		Level:     level,
		Event:     event,
		Title:     title,
		Message:   message,
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"job_id":       job.ID,
			"job_name":     job.Name,
			"execution_id": execution.ID,
			"status":       job.Status,
			"stage":        job.Stage,
		},
	}

	return nm.SendNotification(ctx, notification)
}

// getMatchingRules returns notification rules that match the notification
func (nm *NotificationManager) getMatchingRules(notification *Notification) []NotificationRule {
	var matchingRules []NotificationRule

	for _, rule := range nm.config.Rules {
		if nm.ruleMatches(rule, notification) {
			matchingRules = append(matchingRules, rule)
		}
	}

	return matchingRules
}

// ruleMatches checks if a rule matches the notification
func (nm *NotificationManager) ruleMatches(rule NotificationRule, notification *Notification) bool {
	// Check events
	if len(rule.Events) > 0 {
		eventMatches := false
		for _, event := range rule.Events {
			if event == notification.Event {
				eventMatches = true
				break
			}
		}
		if !eventMatches {
			return false
		}
	}

	// Check levels
	if len(rule.Levels) > 0 {
		levelMatches := false
		for _, level := range rule.Levels {
			if level == notification.Level {
				levelMatches = true
				break
			}
		}
		if !levelMatches {
			return false
		}
	}

	// Check conditions
	for key, expectedValue := range rule.Conditions {
		if actualValue, exists := notification.Metadata[key]; exists {
			if fmt.Sprintf("%v", actualValue) != expectedValue {
				return false
			}
		} else {
			return false
		}
	}

	return true
}

// getLevelForEvent returns the appropriate level for an event
func (nm *NotificationManager) getLevelForEvent(event NotificationEvent) NotificationLevel {
	switch event {
	case EventPipelineFailed, EventJobFailed, EventDeploymentFailed:
		return LevelError
	case EventPipelineCompleted, EventJobCompleted, EventDeploymentSuccess:
		return LevelSuccess
	case EventPipelineStarted, EventJobStarted, EventDeploymentStarted:
		return LevelInfo
	default:
		return LevelInfo
	}
}

// buildPipelineMessage builds a message for pipeline events
func (nm *NotificationManager) buildPipelineMessage(
	event NotificationEvent,
	pipeline *types.Pipeline,
	execution *types.PipelineExecution,
) (string, string) {
	switch event {
	case EventPipelineStarted:
		return fmt.Sprintf("Pipeline Started: %s", pipeline.Name),
			fmt.Sprintf("Pipeline '%s' has started execution on branch '%s'", pipeline.Name, execution.Branch)
	case EventPipelineCompleted:
		return fmt.Sprintf("Pipeline Completed: %s", pipeline.Name),
			fmt.Sprintf("Pipeline '%s' completed successfully on branch '%s'", pipeline.Name, execution.Branch)
	case EventPipelineFailed:
		return fmt.Sprintf("Pipeline Failed: %s", pipeline.Name),
			fmt.Sprintf("Pipeline '%s' failed on branch '%s'", pipeline.Name, execution.Branch)
	default:
		return fmt.Sprintf("Pipeline Event: %s", pipeline.Name),
			fmt.Sprintf("Pipeline '%s' event: %s", pipeline.Name, event)
	}
}

// buildJobMessage builds a message for job events
func (nm *NotificationManager) buildJobMessage(
	event NotificationEvent,
	job *types.Job,
	execution *types.PipelineExecution,
) (string, string) {
	switch event {
	case EventJobStarted:
		return fmt.Sprintf("Job Started: %s", job.Name),
			fmt.Sprintf("Job '%s' in stage '%s' has started", job.Name, job.Stage)
	case EventJobCompleted:
		return fmt.Sprintf("Job Completed: %s", job.Name),
			fmt.Sprintf("Job '%s' in stage '%s' completed successfully", job.Name, job.Stage)
	case EventJobFailed:
		return fmt.Sprintf("Job Failed: %s", job.Name),
			fmt.Sprintf("Job '%s' in stage '%s' failed", job.Name, job.Stage)
	default:
		return fmt.Sprintf("Job Event: %s", job.Name),
			fmt.Sprintf("Job '%s' event: %s", job.Name, event)
	}
}

// SlackProvider implements Slack notifications
type SlackProvider struct {
	webhookURL string
	channel    string
	username   string
	enabled    bool
}

// NewSlackProvider creates a new Slack provider
func NewSlackProvider(config map[string]interface{}) (*SlackProvider, error) {
	webhookURL, ok := config["webhook_url"].(string)
	if !ok || webhookURL == "" {
		return nil, fmt.Errorf("webhook_url is required for Slack provider")
	}

	channel, _ := config["channel"].(string)
	username, _ := config["username"].(string)
	enabled, _ := config["enabled"].(bool)

	if username == "" {
		username = "ChainOps"
	}

	return &SlackProvider{
		webhookURL: webhookURL,
		channel:    channel,
		username:   username,
		enabled:    enabled,
	}, nil
}

// Send sends a Slack notification
func (sp *SlackProvider) Send(ctx context.Context, notification *Notification) error {
	color := sp.getColorForLevel(notification.Level)

	payload := map[string]interface{}{
		"username": sp.username,
		"attachments": []map[string]interface{}{
			{
				"color":     color,
				"title":     notification.Title,
				"text":      notification.Message,
				"timestamp": notification.Timestamp.Unix(),
				"fields": []map[string]interface{}{
					{
						"title": "Event",
						"value": string(notification.Event),
						"short": true,
					},
					{
						"title": "Level",
						"value": string(notification.Level),
						"short": true,
					},
				},
			},
		},
	}

	if sp.channel != "" {
		payload["channel"] = sp.channel
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal Slack payload: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", sp.webhookURL, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send Slack notification: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Slack API returned status %d", resp.StatusCode)
	}

	return nil
}

// GetType returns the provider type
func (sp *SlackProvider) GetType() NotificationType {
	return NotificationSlack
}

// IsEnabled returns whether the provider is enabled
func (sp *SlackProvider) IsEnabled() bool {
	return sp.enabled
}

// getColorForLevel returns Slack color for notification level
func (sp *SlackProvider) getColorForLevel(level NotificationLevel) string {
	switch level {
	case LevelError:
		return "danger"
	case LevelWarning:
		return "warning"
	case LevelSuccess:
		return "good"
	default:
		return "#36a64f"
	}
}

// EmailProvider implements email notifications
type EmailProvider struct {
	smtpHost     string
	smtpPort     int
	smtpUsername string
	smtpPassword string
	fromEmail    string
	fromName     string
	enabled      bool
}

// NewEmailProvider creates a new email provider
func NewEmailProvider(config map[string]interface{}) (NotificationProvider, error) {
	smtpHost, ok := config["smtp_host"].(string)
	if !ok || smtpHost == "" {
		return nil, fmt.Errorf("smtp_host is required for email provider")
	}

	smtpPort, ok := config["smtp_port"].(int)
	if !ok {
		smtpPort = 587 // Default SMTP port
	}

	smtpUsername, _ := config["smtp_username"].(string)
	smtpPassword, _ := config["smtp_password"].(string)
	fromEmail, _ := config["from_email"].(string)
	fromName, _ := config["from_name"].(string)
	enabled, _ := config["enabled"].(bool)

	if fromEmail == "" {
		fromEmail = "<EMAIL>"
	}
	if fromName == "" {
		fromName = "ChainOps"
	}

	return &EmailProvider{
		smtpHost:     smtpHost,
		smtpPort:     smtpPort,
		smtpUsername: smtpUsername,
		smtpPassword: smtpPassword,
		fromEmail:    fromEmail,
		fromName:     fromName,
		enabled:      enabled,
	}, nil
}

// Send sends an email notification
func (ep *EmailProvider) Send(ctx context.Context, notification *Notification) error {
	// Implementation would use net/smtp or a library like gomail
	// For now, return a placeholder
	return fmt.Errorf("email provider implementation pending")
}

// GetType returns the provider type
func (ep *EmailProvider) GetType() NotificationType {
	return NotificationEmail
}

// IsEnabled returns whether the provider is enabled
func (ep *EmailProvider) IsEnabled() bool {
	return ep.enabled
}

func NewWebhookProvider(config map[string]interface{}) (NotificationProvider, error) {
	// TODO: Implement webhook provider
	return nil, fmt.Errorf("webhook provider not implemented")
}

func NewDiscordProvider(config map[string]interface{}) (NotificationProvider, error) {
	// TODO: Implement Discord provider
	return nil, fmt.Errorf("discord provider not implemented")
}

// TeamsProvider implements Microsoft Teams notifications
type TeamsProvider struct {
	webhookURL string
	enabled    bool
}

// NewTeamsProvider creates a new Teams provider
func NewTeamsProvider(config map[string]interface{}) (NotificationProvider, error) {
	webhookURL, ok := config["webhook_url"].(string)
	if !ok || webhookURL == "" {
		return nil, fmt.Errorf("webhook_url is required for Teams provider")
	}

	enabled, _ := config["enabled"].(bool)

	return &TeamsProvider{
		webhookURL: webhookURL,
		enabled:    enabled,
	}, nil
}

// Send sends a Teams notification
func (tp *TeamsProvider) Send(ctx context.Context, notification *Notification) error {
	color := tp.getColorForLevel(notification.Level)

	payload := map[string]interface{}{
		"@type":      "MessageCard",
		"@context":   "http://schema.org/extensions",
		"themeColor": color,
		"summary":    notification.Title,
		"sections": []map[string]interface{}{
			{
				"activityTitle":    notification.Title,
				"activitySubtitle": string(notification.Event),
				"text":             notification.Message,
				"facts": []map[string]interface{}{
					{
						"name":  "Level",
						"value": string(notification.Level),
					},
					{
						"name":  "Event",
						"value": string(notification.Event),
					},
					{
						"name":  "Time",
						"value": notification.Timestamp.Format("2006-01-02 15:04:05"),
					},
				},
			},
		},
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal Teams payload: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", tp.webhookURL, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send Teams notification: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Teams API returned status %d", resp.StatusCode)
	}

	return nil
}

// GetType returns the provider type
func (tp *TeamsProvider) GetType() NotificationType {
	return NotificationTeams
}

// IsEnabled returns whether the provider is enabled
func (tp *TeamsProvider) IsEnabled() bool {
	return tp.enabled
}

// getColorForLevel returns Teams color for notification level
func (tp *TeamsProvider) getColorForLevel(level NotificationLevel) string {
	switch level {
	case LevelError:
		return "FF0000"
	case LevelWarning:
		return "FFA500"
	case LevelSuccess:
		return "00FF00"
	default:
		return "0078D4"
	}
}
