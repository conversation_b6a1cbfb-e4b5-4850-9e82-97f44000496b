package streaming

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/chainops/chainops/pkg/logger"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// LogLevel represents log levels
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug"
	LogLevelInfo  LogLevel = "info"
	LogLevelWarn  LogLevel = "warn"
	LogLevelError LogLevel = "error"
)

// LogEntry represents a log entry
type LogEntry struct {
	ID          uuid.UUID              `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Level       LogLevel               `json:"level"`
	Message     string                 `json:"message"`
	Source      string                 `json:"source"`
	PipelineID  uuid.UUID              `json:"pipeline_id,omitempty"`
	ExecutionID uuid.UUID              `json:"execution_id,omitempty"`
	JobID       uuid.UUID              `json:"job_id,omitempty"`
	StepID      uuid.UUID              `json:"step_id,omitempty"`
	Fields      map[string]interface{} `json:"fields,omitempty"`
}

// LogStream represents a log stream
type LogStream struct {
	ID          uuid.UUID `json:"id"`
	PipelineID  uuid.UUID `json:"pipeline_id,omitempty"`
	ExecutionID uuid.UUID `json:"execution_id,omitempty"`
	JobID       uuid.UUID `json:"job_id,omitempty"`
	Filter      LogFilter `json:"filter"`
	CreatedAt   time.Time `json:"created_at"`
}

// LogFilter represents filters for log streaming
type LogFilter struct {
	Levels      []LogLevel `json:"levels,omitempty"`
	Sources     []string   `json:"sources,omitempty"`
	PipelineID  uuid.UUID  `json:"pipeline_id,omitempty"`
	ExecutionID uuid.UUID  `json:"execution_id,omitempty"`
	JobID       uuid.UUID  `json:"job_id,omitempty"`
	Since       *time.Time `json:"since,omitempty"`
	Until       *time.Time `json:"until,omitempty"`
	Search      string     `json:"search,omitempty"`
}

// LogStreamClient represents a WebSocket client for log streaming
type LogStreamClient struct {
	ID       uuid.UUID
	Conn     *websocket.Conn
	Stream   *LogStream
	Send     chan *LogEntry
	Hub      *LogStreamHub
	mu       sync.RWMutex
	closed   bool
}

// LogStreamHub manages WebSocket connections for log streaming
type LogStreamHub struct {
	clients    map[uuid.UUID]*LogStreamClient
	register   chan *LogStreamClient
	unregister chan *LogStreamClient
	broadcast  chan *LogEntry
	mu         sync.RWMutex
	logger     *logger.Logger
}

// LogStreamer manages real-time log streaming
type LogStreamer struct {
	hub        *LogStreamHub
	logBuffer  *LogBuffer
	logger     *logger.Logger
}

// LogBuffer stores recent log entries for replay
type LogBuffer struct {
	entries []LogEntry
	maxSize int
	mu      sync.RWMutex
}

// NewLogStreamer creates a new log streamer
func NewLogStreamer(logger *logger.Logger) *LogStreamer {
	hub := &LogStreamHub{
		clients:    make(map[uuid.UUID]*LogStreamClient),
		register:   make(chan *LogStreamClient),
		unregister: make(chan *LogStreamClient),
		broadcast:  make(chan *LogEntry, 1000),
		logger:     logger,
	}

	buffer := &LogBuffer{
		entries: make([]LogEntry, 0),
		maxSize: 10000, // Keep last 10k log entries
	}

	streamer := &LogStreamer{
		hub:       hub,
		logBuffer: buffer,
		logger:    logger,
	}

	// Start hub
	go hub.run()

	return streamer
}

// CreateStream creates a new log stream
func (s *LogStreamer) CreateStream(filter LogFilter) *LogStream {
	stream := &LogStream{
		ID:          uuid.New(),
		PipelineID:  filter.PipelineID,
		ExecutionID: filter.ExecutionID,
		JobID:       filter.JobID,
		Filter:      filter,
		CreatedAt:   time.Now(),
	}

	return stream
}

// ConnectClient connects a WebSocket client to a log stream
func (s *LogStreamer) ConnectClient(conn *websocket.Conn, stream *LogStream) *LogStreamClient {
	client := &LogStreamClient{
		ID:     uuid.New(),
		Conn:   conn,
		Stream: stream,
		Send:   make(chan *LogEntry, 256),
		Hub:    s.hub,
	}

	s.hub.register <- client

	// Send historical logs if requested
	if stream.Filter.Since != nil {
		go s.sendHistoricalLogs(client, *stream.Filter.Since)
	}

	// Start client goroutines
	go client.writePump()
	go client.readPump()

	return client
}

// sendHistoricalLogs sends historical log entries to a client
func (s *LogStreamer) sendHistoricalLogs(client *LogStreamClient, since time.Time) {
	s.logBuffer.mu.RLock()
	defer s.logBuffer.mu.RUnlock()

	for _, entry := range s.logBuffer.entries {
		if entry.Timestamp.After(since) && s.matchesFilter(entry, client.Stream.Filter) {
			select {
			case client.Send <- &entry:
			case <-time.After(5 * time.Second):
				// Client is slow, skip this entry
				continue
			}
		}
	}
}

// StreamLog streams a log entry to connected clients
func (s *LogStreamer) StreamLog(entry LogEntry) {
	// Add to buffer
	s.logBuffer.Add(entry)

	// Broadcast to hub
	select {
	case s.hub.broadcast <- &entry:
	default:
		// Hub is full, drop the log entry
		s.logger.Warn("Log streaming hub is full, dropping log entry")
	}
}

// matchesFilter checks if a log entry matches the filter
func (s *LogStreamer) matchesFilter(entry LogEntry, filter LogFilter) bool {
	// Check levels
	if len(filter.Levels) > 0 {
		found := false
		for _, level := range filter.Levels {
			if entry.Level == level {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check sources
	if len(filter.Sources) > 0 {
		found := false
		for _, source := range filter.Sources {
			if entry.Source == source {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check pipeline ID
	if filter.PipelineID != uuid.Nil && entry.PipelineID != filter.PipelineID {
		return false
	}

	// Check execution ID
	if filter.ExecutionID != uuid.Nil && entry.ExecutionID != filter.ExecutionID {
		return false
	}

	// Check job ID
	if filter.JobID != uuid.Nil && entry.JobID != filter.JobID {
		return false
	}

	// Check time range
	if filter.Since != nil && entry.Timestamp.Before(*filter.Since) {
		return false
	}
	if filter.Until != nil && entry.Timestamp.After(*filter.Until) {
		return false
	}

	// Check search term
	if filter.Search != "" {
		// Simple substring search in message
		if entry.Message == "" || entry.Message != filter.Search {
			return false
		}
	}

	return true
}

// LogStreamHub methods

// run starts the hub
func (h *LogStreamHub) run() {
	for {
		select {
		case client := <-h.register:
			h.mu.Lock()
			h.clients[client.ID] = client
			h.mu.Unlock()
			h.logger.WithField("client_id", client.ID).Debug("Client connected to log stream")

		case client := <-h.unregister:
			h.mu.Lock()
			if _, ok := h.clients[client.ID]; ok {
				delete(h.clients, client.ID)
				close(client.Send)
			}
			h.mu.Unlock()
			h.logger.WithField("client_id", client.ID).Debug("Client disconnected from log stream")

		case entry := <-h.broadcast:
			h.mu.RLock()
			for _, client := range h.clients {
				if h.matchesClientFilter(entry, client) {
					select {
					case client.Send <- entry:
					default:
						// Client is slow, close it
						delete(h.clients, client.ID)
						close(client.Send)
					}
				}
			}
			h.mu.RUnlock()
		}
	}
}

// matchesClientFilter checks if an entry matches a client's filter
func (h *LogStreamHub) matchesClientFilter(entry *LogEntry, client *LogStreamClient) bool {
	return h.matchesFilter(*entry, client.Stream.Filter)
}

// matchesFilter checks if a log entry matches the filter (duplicate of LogStreamer method)
func (h *LogStreamHub) matchesFilter(entry LogEntry, filter LogFilter) bool {
	// Same implementation as LogStreamer.matchesFilter
	// (duplicated for simplicity, could be refactored)
	
	// Check levels
	if len(filter.Levels) > 0 {
		found := false
		for _, level := range filter.Levels {
			if entry.Level == level {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check pipeline ID
	if filter.PipelineID != uuid.Nil && entry.PipelineID != filter.PipelineID {
		return false
	}

	// Check execution ID
	if filter.ExecutionID != uuid.Nil && entry.ExecutionID != filter.ExecutionID {
		return false
	}

	// Check job ID
	if filter.JobID != uuid.Nil && entry.JobID != filter.JobID {
		return false
	}

	return true
}

// LogStreamClient methods

// readPump handles reading from the WebSocket connection
func (c *LogStreamClient) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(512)
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, _, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.Hub.logger.WithError(err).Error("WebSocket error")
			}
			break
		}
	}
}

// writePump handles writing to the WebSocket connection
func (c *LogStreamClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case entry, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}

			// Send the log entry as JSON
			if err := json.NewEncoder(w).Encode(entry); err != nil {
				w.Close()
				return
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// LogBuffer methods

// Add adds a log entry to the buffer
func (b *LogBuffer) Add(entry LogEntry) {
	b.mu.Lock()
	defer b.mu.Unlock()

	b.entries = append(b.entries, entry)

	// Keep buffer size under limit
	if len(b.entries) > b.maxSize {
		// Remove oldest entries
		copy(b.entries, b.entries[len(b.entries)-b.maxSize:])
		b.entries = b.entries[:b.maxSize]
	}
}

// GetEntries returns entries matching the filter
func (b *LogBuffer) GetEntries(filter LogFilter) []LogEntry {
	b.mu.RLock()
	defer b.mu.RUnlock()

	var result []LogEntry
	for _, entry := range b.entries {
		// Apply filter logic here
		if b.matchesFilter(entry, filter) {
			result = append(result, entry)
		}
	}

	return result
}

// matchesFilter checks if an entry matches the filter
func (b *LogBuffer) matchesFilter(entry LogEntry, filter LogFilter) bool {
	// Same filter logic as above
	return true // Simplified for now
}
