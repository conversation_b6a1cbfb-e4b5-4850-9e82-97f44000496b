package deployment

import (
	"time"

	"github.com/chainops/chainops/pkg/types"
	"github.com/google/uuid"
)

// DeploymentRequest represents a deployment request
type DeploymentRequest struct {
	ID           string                 `json:"id"`
	PipelineID   uuid.UUID              `json:"pipeline_id"`
	ExecutionID  uuid.UUID              `json:"execution_id"`
	Environment  string                 `json:"environment"`
	Strategy     string                 `json:"strategy"`
	Application  string                 `json:"application"`
	Version      string                 `json:"version"`
	Manifests    []string               `json:"manifests"`
	Values       map[string]interface{} `json:"values"`
	Secrets      map[string]string      `json:"secrets"`
	Timeout      time.Duration          `json:"timeout"`
	HealthChecks []HealthCheck          `json:"health_checks"`
	Config       map[string]interface{} `json:"config"`
	CreatedBy    uuid.UUID              `json:"created_by"`
}

// DeploymentConfig holds configuration for a deployment
type DeploymentConfig struct {
	ID           string                 `json:"id"`
	Strategy     string                 `json:"strategy"`
	Environment  string                 `json:"environment"`
	Application  string                 `json:"application"`
	Version      string                 `json:"version"`
	Manifests    []string               `json:"manifests"`
	Values       map[string]interface{} `json:"values"`
	Secrets      map[string]string      `json:"secrets"`
	Timeout      time.Duration          `json:"timeout"`
	HealthChecks []HealthCheck          `json:"health_checks"`
}

// DeploymentResult holds the result of a deployment
type DeploymentResult struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	Resources []DeployedResource     `json:"resources"`
	Endpoints []ServiceEndpoint      `json:"endpoints"`
	Metrics   DeploymentMetrics      `json:"metrics"`
	Logs      []string               `json:"logs"`
	Artifacts []string               `json:"artifacts"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// DeploymentStatus represents the current status of a deployment
type DeploymentStatus struct {
	ID           string                 `json:"id"`
	Status       types.DeploymentStatus `json:"status"`
	Environment  string                 `json:"environment"`
	Strategy     string                 `json:"strategy"`
	Progress     int                    `json:"progress"` // 0-100
	Phase        string                 `json:"phase"`
	Message      string                 `json:"message"`
	Resources    []DeployedResource     `json:"resources"`
	HealthChecks []HealthCheckResult    `json:"health_checks"`
	CreatedAt    time.Time              `json:"created_at"`
	StartedAt    *time.Time             `json:"started_at"`
	CompletedAt  *time.Time             `json:"completed_at"`
}

// DeployedResource represents a deployed Kubernetes resource
type DeployedResource struct {
	Kind      string `json:"kind"`
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	Status    string `json:"status"`
	Ready     bool   `json:"ready"`
	Message   string `json:"message"`
}

// ServiceEndpoint represents a service endpoint
type ServiceEndpoint struct {
	Name     string `json:"name"`
	URL      string `json:"url"`
	Type     string `json:"type"` // "internal", "external", "loadbalancer"
	Protocol string `json:"protocol"`
	Port     int    `json:"port"`
}

// HealthCheck defines a health check configuration
type HealthCheck struct {
	Name        string        `json:"name"`
	Type        string        `json:"type"` // "http", "tcp", "command"
	URL         string        `json:"url,omitempty"`
	Command     []string      `json:"command,omitempty"`
	Interval    time.Duration `json:"interval"`
	Timeout     time.Duration `json:"timeout"`
	Retries     int           `json:"retries"`
	SuccessCode int           `json:"success_code,omitempty"`
}

// HealthCheckResult represents the result of a health check
type HealthCheckResult struct {
	Name      string        `json:"name"`
	Status    string        `json:"status"` // "passing", "failing", "unknown"
	Message   string        `json:"message"`
	Timestamp time.Time     `json:"timestamp"`
	Duration  time.Duration `json:"duration"`
}

// DeploymentMetrics holds metrics for a deployment
type DeploymentMetrics struct {
	Duration         time.Duration `json:"duration"`
	ResourcesCount   int           `json:"resources_count"`
	SuccessRate      float64       `json:"success_rate"`
	HealthScore      float64       `json:"health_score"`
	RolloutTime      time.Duration `json:"rollout_time"`
	VerificationTime time.Duration `json:"verification_time"`
}

// DeploymentFilters for querying deployments
type DeploymentFilters struct {
	Environment string                 `json:"environment,omitempty"`
	Application string                 `json:"application,omitempty"`
	Strategy    string                 `json:"strategy,omitempty"`
	Status      types.DeploymentStatus `json:"status,omitempty"`
	CreatedBy   uuid.UUID              `json:"created_by,omitempty"`
	StartDate   *time.Time             `json:"start_date,omitempty"`
	EndDate     *time.Time             `json:"end_date,omitempty"`
	Limit       int                    `json:"limit,omitempty"`
	Offset      int                    `json:"offset,omitempty"`
	OrderBy     string                 `json:"order_by,omitempty"`
}

// RollingDeploymentConfig for rolling deployment strategy
type RollingDeploymentConfig struct {
	MaxUnavailable string        `json:"max_unavailable"` // "25%" or "1"
	MaxSurge       string        `json:"max_surge"`       // "25%" or "1"
	BatchSize      int           `json:"batch_size"`
	PauseTime      time.Duration `json:"pause_time"`
}

// CanaryDeploymentConfig for canary deployment strategy
type CanaryDeploymentConfig struct {
	Steps        []CanaryStep       `json:"steps"`
	Analysis     CanaryAnalysis     `json:"analysis"`
	TrafficSplit TrafficSplitConfig `json:"traffic_split"`
}

// CanaryStep represents a step in canary deployment
type CanaryStep struct {
	Weight   int           `json:"weight"`   // Traffic percentage
	Duration time.Duration `json:"duration"` // How long to wait
	Pause    bool          `json:"pause"`    // Manual approval required
}

// CanaryAnalysis defines analysis for canary deployments
type CanaryAnalysis struct {
	Enabled          bool          `json:"enabled"`
	Interval         time.Duration `json:"interval"`
	Threshold        float64       `json:"threshold"`
	SuccessRate      float64       `json:"success_rate"`
	LatencyP99       time.Duration `json:"latency_p99"`
	ErrorRate        float64       `json:"error_rate"`
	FailureThreshold int           `json:"failure_threshold"`
}

// TrafficSplitConfig defines traffic splitting configuration
type TrafficSplitConfig struct {
	Provider string             `json:"provider"` // "istio", "nginx", "traefik"
	Headers  map[string]string  `json:"headers"`
	Rules    []TrafficSplitRule `json:"rules"`
}

// TrafficSplitRule defines a traffic splitting rule
type TrafficSplitRule struct {
	Match   map[string]string `json:"match"`
	Weight  int               `json:"weight"`
	Headers map[string]string `json:"headers"`
}

// RecreateDeploymentConfig for recreate deployment strategy
type RecreateDeploymentConfig struct {
	GracePeriod time.Duration `json:"grace_period"`
	PreStop     []string      `json:"pre_stop"`
	PostStart   []string      `json:"post_start"`
}

// DeploymentEvent represents events during deployment
type DeploymentEvent struct {
	Type         string                 `json:"type"`
	DeploymentID string                 `json:"deployment_id"`
	Timestamp    time.Time              `json:"timestamp"`
	Phase        string                 `json:"phase"`
	Message      string                 `json:"message"`
	Data         map[string]interface{} `json:"data"`
}

// EnvironmentConfig defines environment-specific configuration
type EnvironmentConfig struct {
	Name       string            `json:"name"`
	Type       string            `json:"type"` // "development", "staging", "production"
	Namespace  string            `json:"namespace"`
	Cluster    string            `json:"cluster"`
	Region     string            `json:"region"`
	Variables  map[string]string `json:"variables"`
	Secrets    []string          `json:"secrets"`
	Resources  ResourceLimits    `json:"resources"`
	Policies   []PolicyConfig    `json:"policies"`
	Monitoring MonitoringConfig  `json:"monitoring"`
}

// ResourceLimits defines resource limits for an environment
type ResourceLimits struct {
	CPU      string `json:"cpu"`
	Memory   string `json:"memory"`
	Storage  string `json:"storage"`
	Replicas int    `json:"replicas"`
}

// PolicyConfig defines policies for an environment
type PolicyConfig struct {
	Type   string                 `json:"type"` // "network", "security", "resource"
	Rules  []string               `json:"rules"`
	Config map[string]interface{} `json:"config"`
}

// MonitoringConfig defines monitoring configuration
type MonitoringConfig struct {
	Enabled    bool     `json:"enabled"`
	Metrics    []string `json:"metrics"`
	Alerts     []string `json:"alerts"`
	Dashboards []string `json:"dashboards"`
	LogLevel   string   `json:"log_level"`
}
