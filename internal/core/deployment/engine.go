package deployment

import (
	"context"
	"fmt"
	"time"

	"github.com/chainops/chainops/internal/storage"
	"github.com/chainops/chainops/pkg/types"
	"github.com/sirupsen/logrus"
)

// Engine manages deployment operations
type Engine struct {
	logger       *logrus.Logger
	config       EngineConfig
	deployRepo   *storage.DeploymentRepository
	cache        storage.Cache
	messageQueue storage.MessageQueue

	// Deployment strategies
	strategies map[string]DeploymentStrategy
}

// EngineConfig holds deployment engine configuration
type EngineConfig struct {
	DefaultStrategy    string        `yaml:"default_strategy"`
	HealthCheckTimeout time.Duration `yaml:"health_check_timeout"`
	RollbackTimeout    time.Duration `yaml:"rollback_timeout"`
	MaxConcurrent      int           `yaml:"max_concurrent"`
}

// DeploymentStrategy interface for different deployment strategies
type DeploymentStrategy interface {
	Deploy(ctx context.Context, config *DeploymentConfig) (*DeploymentResult, error)
	Rollback(ctx context.Context, deploymentID string) error
	GetStatus(ctx context.Context, deploymentID string) (*DeploymentStatus, error)
	HealthCheck(ctx context.Context, config *DeploymentConfig) error
	Validate(config *DeploymentConfig) error
}

// NewEngine creates a new deployment engine
func NewEngine(logger *logrus.Logger, config EngineConfig) *Engine {
	engine := &Engine{
		logger:     logger,
		config:     config,
		strategies: make(map[string]DeploymentStrategy),
	}

	// Register built-in strategies
	engine.registerStrategies()

	return engine
}

// Initialize initializes the deployment engine
func (e *Engine) Initialize(
	deployRepo *storage.DeploymentRepository,
	cache storage.Cache,
	messageQueue storage.MessageQueue,
) error {
	e.deployRepo = deployRepo
	e.cache = cache
	e.messageQueue = messageQueue

	return nil
}

// registerStrategies registers built-in deployment strategies
func (e *Engine) registerStrategies() {
	e.strategies["rolling"] = NewRollingDeployment(e.logger)
	e.strategies["blue-green"] = NewSimpleBlueGreenDeployment(e.logger)
	e.strategies["canary"] = NewCanaryDeployment(e.logger)
	e.strategies["recreate"] = NewRecreateDeployment(e.logger)
}

// Deploy executes a deployment
func (e *Engine) Deploy(ctx context.Context, req *DeploymentRequest) (*types.Deployment, error) {
	logger := e.logger.WithFields(logrus.Fields{
		"deployment_id": req.ID,
		"strategy":      req.Strategy,
		"environment":   req.Environment,
	})

	logger.Info("Starting deployment")

	// Validate request
	if err := e.validateDeploymentRequest(req); err != nil {
		return nil, fmt.Errorf("invalid deployment request: %w", err)
	}

	// Get deployment strategy
	strategy, exists := e.strategies[req.Strategy]
	if !exists {
		return nil, fmt.Errorf("unsupported deployment strategy: %s", req.Strategy)
	}

	// Create deployment record
	deployment := &types.Deployment{
		ID:          req.ID,
		PipelineID:  req.PipelineID,
		ExecutionID: req.ExecutionID,
		Environment: req.Environment,
		Strategy:    req.Strategy,
		Status:      types.DeploymentStatusPending,
		Config:      req.Config,
		CreatedBy:   req.CreatedBy,
		CreatedAt:   time.Now(),
	}

	// Save deployment record
	if err := e.deployRepo.Create(deployment); err != nil {
		return nil, fmt.Errorf("failed to create deployment record: %w", err)
	}

	// Execute deployment asynchronously
	go e.executeDeployment(ctx, deployment, strategy, req)

	return deployment, nil
}

// executeDeployment executes the deployment
func (e *Engine) executeDeployment(ctx context.Context, deployment *types.Deployment, strategy DeploymentStrategy, req *DeploymentRequest) {
	logger := e.logger.WithField("deployment_id", deployment.ID)

	// Update status to running
	deployment.Status = types.DeploymentStatusRunning
	deployment.StartedAt = &time.Time{}
	*deployment.StartedAt = time.Now()

	if err := e.deployRepo.Update(deployment); err != nil {
		logger.WithError(err).Error("Failed to update deployment status")
		return
	}

	// Create deployment config
	config := &DeploymentConfig{
		ID:           deployment.ID,
		Strategy:     deployment.Strategy,
		Environment:  deployment.Environment,
		Application:  req.Application,
		Version:      req.Version,
		Manifests:    req.Manifests,
		Values:       req.Values,
		Secrets:      req.Secrets,
		Timeout:      req.Timeout,
		HealthChecks: req.HealthChecks,
	}

	// Execute deployment
	result, err := strategy.Deploy(ctx, config)
	if err != nil {
		logger.WithError(err).Error("Deployment failed")
		e.handleDeploymentFailure(deployment, err)
		return
	}

	// Update deployment with results
	deployment.Status = types.DeploymentStatusSuccess
	deployment.CompletedAt = &time.Time{}
	*deployment.CompletedAt = time.Now()

	// Convert local DeploymentResult to types.DeploymentResult
	deployment.Result = &types.DeploymentResult{
		Success:   result.Success,
		Message:   result.Message,
		Resources: convertResources(result.Resources),
		Endpoints: convertEndpoints(result.Endpoints),
		Metadata:  result.Metadata,
	}

	if err := e.deployRepo.Update(deployment); err != nil {
		logger.WithError(err).Error("Failed to update deployment status")
	}

	logger.Info("Deployment completed successfully")

	// Send notification
	e.sendDeploymentNotification(deployment, "success")
}

// handleDeploymentFailure handles deployment failures
func (e *Engine) handleDeploymentFailure(deployment *types.Deployment, err error) {
	deployment.Status = types.DeploymentStatusFailure
	deployment.CompletedAt = &time.Time{}
	*deployment.CompletedAt = time.Now()
	deployment.Error = err.Error()

	if updateErr := e.deployRepo.Update(deployment); updateErr != nil {
		e.logger.WithError(updateErr).Error("Failed to update deployment status")
	}

	// Send notification
	e.sendDeploymentNotification(deployment, "failure")
}

// Rollback rolls back a deployment
func (e *Engine) Rollback(ctx context.Context, deploymentID string) error {
	logger := e.logger.WithField("deployment_id", deploymentID)
	logger.Info("Starting deployment rollback")

	// Get deployment
	deployment, err := e.deployRepo.GetByID(deploymentID)
	if err != nil {
		return fmt.Errorf("deployment not found: %w", err)
	}

	// Get strategy
	strategy, exists := e.strategies[deployment.Strategy]
	if !exists {
		return fmt.Errorf("unsupported deployment strategy: %s", deployment.Strategy)
	}

	// Execute rollback
	if err := strategy.Rollback(ctx, deploymentID); err != nil {
		logger.WithError(err).Error("Rollback failed")
		return fmt.Errorf("rollback failed: %w", err)
	}

	// Update deployment status
	deployment.Status = types.DeploymentStatusRolledBack
	deployment.RolledBackAt = &time.Time{}
	*deployment.RolledBackAt = time.Now()

	if err := e.deployRepo.Update(deployment); err != nil {
		logger.WithError(err).Error("Failed to update deployment status")
	}

	logger.Info("Deployment rolled back successfully")
	return nil
}

// GetDeploymentStatus returns the status of a deployment
func (e *Engine) GetDeploymentStatus(ctx context.Context, deploymentID string) (*DeploymentStatus, error) {
	// Get deployment from database
	deployment, err := e.deployRepo.GetByID(deploymentID)
	if err != nil {
		return nil, fmt.Errorf("deployment not found: %w", err)
	}

	// Get strategy
	strategy, exists := e.strategies[deployment.Strategy]
	if !exists {
		return nil, fmt.Errorf("unsupported deployment strategy: %s", deployment.Strategy)
	}

	// Get live status from strategy
	status, err := strategy.GetStatus(ctx, deploymentID)
	if err != nil {
		// Return database status if live status is unavailable
		status = &DeploymentStatus{
			ID:          deployment.ID,
			Status:      deployment.Status,
			Environment: deployment.Environment,
			Strategy:    deployment.Strategy,
			CreatedAt:   deployment.CreatedAt,
			StartedAt:   deployment.StartedAt,
			CompletedAt: deployment.CompletedAt,
		}
	}

	return status, nil
}

// validateDeploymentRequest validates a deployment request
func (e *Engine) validateDeploymentRequest(req *DeploymentRequest) error {
	if req.ID == "" {
		return fmt.Errorf("deployment ID is required")
	}

	if req.Strategy == "" {
		req.Strategy = e.config.DefaultStrategy
	}

	if req.Environment == "" {
		return fmt.Errorf("environment is required")
	}

	if req.Application == "" {
		return fmt.Errorf("application name is required")
	}

	if req.Version == "" {
		return fmt.Errorf("version is required")
	}

	// Validate strategy exists
	if _, exists := e.strategies[req.Strategy]; !exists {
		return fmt.Errorf("unsupported deployment strategy: %s", req.Strategy)
	}

	return nil
}

// sendDeploymentNotification sends deployment notifications
func (e *Engine) sendDeploymentNotification(deployment *types.Deployment, eventType string) {
	notification := map[string]interface{}{
		"type":       "deployment",
		"event":      eventType,
		"deployment": deployment,
		"timestamp":  time.Now(),
	}

	if err := e.messageQueue.Publish("notification.deployment", notification); err != nil {
		e.logger.WithError(err).Error("Failed to send deployment notification")
	}
}

// ListDeployments lists deployments with filters
func (e *Engine) ListDeployments(ctx context.Context, filters DeploymentFilters) ([]*types.Deployment, error) {
	// Convert local filters to types.DeploymentFilters
	typesFilters := types.DeploymentFilters{
		Environment: filters.Environment,
		Application: filters.Application,
		Strategy:    filters.Strategy,
		Status:      filters.Status,
		CreatedBy:   filters.CreatedBy,
		StartDate:   filters.StartDate,
		EndDate:     filters.EndDate,
		Limit:       filters.Limit,
		Offset:      filters.Offset,
		OrderBy:     filters.OrderBy,
	}
	return e.deployRepo.List(typesFilters)
}

// GetDeploymentHistory returns deployment history for an application
func (e *Engine) GetDeploymentHistory(ctx context.Context, environment, application string, limit int) ([]*types.Deployment, error) {
	filters := types.DeploymentFilters{
		Environment: environment,
		Application: application,
		Limit:       limit,
		OrderBy:     "created_at DESC",
	}

	return e.deployRepo.List(filters)
}

// convertResources converts local DeployedResource to interface{} slice
func convertResources(resources []DeployedResource) []interface{} {
	result := make([]interface{}, len(resources))
	for i, resource := range resources {
		result[i] = map[string]interface{}{
			"kind":      resource.Kind,
			"name":      resource.Name,
			"namespace": resource.Namespace,
			"status":    resource.Status,
			"ready":     resource.Ready,
			"message":   resource.Message,
		}
	}
	return result
}

// convertEndpoints converts local ServiceEndpoint to interface{} slice
func convertEndpoints(endpoints []ServiceEndpoint) []interface{} {
	result := make([]interface{}, len(endpoints))
	for i, endpoint := range endpoints {
		result[i] = map[string]interface{}{
			"name":     endpoint.Name,
			"url":      endpoint.URL,
			"type":     endpoint.Type,
			"protocol": endpoint.Protocol,
			"port":     endpoint.Port,
		}
	}
	return result
}
