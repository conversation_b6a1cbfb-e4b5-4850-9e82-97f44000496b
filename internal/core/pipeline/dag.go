package pipeline

import (
	"fmt"
	"sort"
	"strings"
)

// DAGNode represents a node in the pipeline DAG
type DAGNode struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Type         string                 `json:"type"`         // stage, step, job
	Status       string                 `json:"status"`       // pending, running, success, failed, skipped
	Dependencies []string               `json:"dependencies"` // IDs of nodes this depends on
	Dependents   []string               `json:"dependents"`   // IDs of nodes that depend on this
	Position     Position               `json:"position"`     // Visual position
	Metadata     map[string]interface{} `json:"metadata"`     // Additional metadata
	Config       interface{}            `json:"config"`       // Stage/Step configuration
	StartTime    *int64                 `json:"start_time,omitempty"`
	EndTime      *int64                 `json:"end_time,omitempty"`
	Duration     *int64                 `json:"duration,omitempty"`
	Logs         []string               `json:"logs,omitempty"`
}

// Position represents the visual position of a node
type Position struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// DAGEdge represents an edge (dependency) in the pipeline DAG
type DAGEdge struct {
	ID     string `json:"id"`
	From   string `json:"from"`   // Source node ID
	To     string `json:"to"`     // Target node ID
	Type   string `json:"type"`   // dependency, success, failure
	Label  string `json:"label,omitempty"`
	Style  string `json:"style,omitempty"` // solid, dashed, dotted
}

// PipelineDAG represents the complete pipeline as a directed acyclic graph
type PipelineDAG struct {
	ID          string               `json:"id"`
	Name        string               `json:"name"`
	Description string               `json:"description,omitempty"`
	Nodes       map[string]*DAGNode  `json:"nodes"`
	Edges       map[string]*DAGEdge  `json:"edges"`
	Layout      LayoutConfig         `json:"layout"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// LayoutConfig represents the layout configuration for the DAG
type LayoutConfig struct {
	Algorithm string                 `json:"algorithm"` // hierarchical, force, circular
	Direction string                 `json:"direction"` // top-bottom, left-right, bottom-top, right-left
	Spacing   SpacingConfig          `json:"spacing"`
	Options   map[string]interface{} `json:"options,omitempty"`
}

// SpacingConfig represents spacing configuration
type SpacingConfig struct {
	NodeSpacing  int `json:"node_spacing"`
	LevelSpacing int `json:"level_spacing"`
	EdgeSpacing  int `json:"edge_spacing"`
}

// DAGBuilder builds a DAG from pipeline configuration
type DAGBuilder struct {
	nodeCounter int
	edgeCounter int
}

// NewDAGBuilder creates a new DAG builder
func NewDAGBuilder() *DAGBuilder {
	return &DAGBuilder{
		nodeCounter: 0,
		edgeCounter: 0,
	}
}

// BuildDAG builds a DAG from enhanced pipeline configuration
func (b *DAGBuilder) BuildDAG(config *EnhancedPipelineConfig) (*PipelineDAG, error) {
	dag := &PipelineDAG{
		ID:          fmt.Sprintf("pipeline-%s", config.Name),
		Name:        config.Name,
		Description: config.Description,
		Nodes:       make(map[string]*DAGNode),
		Edges:       make(map[string]*DAGEdge),
		Layout: LayoutConfig{
			Algorithm: "hierarchical",
			Direction: "top-bottom",
			Spacing: SpacingConfig{
				NodeSpacing:  100,
				LevelSpacing: 150,
				EdgeSpacing:  50,
			},
		},
		Metadata: make(map[string]interface{}),
	}
	
	// Build nodes for stages and steps
	stageNodes := make(map[string]*DAGNode)
	stepNodes := make(map[string]*DAGNode)
	
	for _, stage := range config.Stages {
		// Create stage node
		stageNode := b.createStageNode(stage)
		dag.Nodes[stageNode.ID] = stageNode
		stageNodes[stage.Name] = stageNode
		
		// Create step nodes
		for _, step := range stage.Steps {
			stepNode := b.createStepNode(step, stage.Name)
			dag.Nodes[stepNode.ID] = stepNode
			stepNodes[fmt.Sprintf("%s.%s", stage.Name, step.Name)] = stepNode
			
			// Create edge from stage to step
			edge := b.createEdge(stageNode.ID, stepNode.ID, "contains")
			dag.Edges[edge.ID] = edge
		}
	}
	
	// Build dependencies between stages
	for _, stage := range config.Stages {
		stageNode := stageNodes[stage.Name]
		
		for _, dependency := range stage.DependsOn {
			if depNode, exists := stageNodes[dependency]; exists {
				edge := b.createEdge(depNode.ID, stageNode.ID, "dependency")
				dag.Edges[edge.ID] = edge
				
				stageNode.Dependencies = append(stageNode.Dependencies, depNode.ID)
				depNode.Dependents = append(depNode.Dependents, stageNode.ID)
			}
		}
	}
	
	// Calculate layout positions
	if err := b.calculateLayout(dag); err != nil {
		return nil, fmt.Errorf("failed to calculate layout: %w", err)
	}
	
	return dag, nil
}

// createStageNode creates a DAG node for a stage
func (b *DAGBuilder) createStageNode(stage StageConfig) *DAGNode {
	b.nodeCounter++
	
	return &DAGNode{
		ID:           fmt.Sprintf("stage-%d", b.nodeCounter),
		Name:         stage.Name,
		Type:         "stage",
		Status:       "pending",
		Dependencies: make([]string, 0),
		Dependents:   make([]string, 0),
		Position:     Position{X: 0, Y: 0},
		Metadata: map[string]interface{}{
			"display_name": stage.DisplayName,
			"description":  stage.Description,
			"condition":    stage.Condition,
			"timeout":      stage.Timeout,
		},
		Config: stage,
	}
}

// createStepNode creates a DAG node for a step
func (b *DAGBuilder) createStepNode(step StepConfig, stageName string) *DAGNode {
	b.nodeCounter++
	
	return &DAGNode{
		ID:           fmt.Sprintf("step-%d", b.nodeCounter),
		Name:         step.Name,
		Type:         "step",
		Status:       "pending",
		Dependencies: make([]string, 0),
		Dependents:   make([]string, 0),
		Position:     Position{X: 0, Y: 0},
		Metadata: map[string]interface{}{
			"stage":        stageName,
			"display_name": step.DisplayName,
			"description":  step.Description,
			"uses":         step.Uses,
			"run":          step.Run,
			"condition":    step.Condition,
			"timeout":      step.Timeout,
		},
		Config: step,
	}
}

// createEdge creates a DAG edge
func (b *DAGBuilder) createEdge(from, to, edgeType string) *DAGEdge {
	b.edgeCounter++
	
	style := "solid"
	if edgeType == "condition" {
		style = "dashed"
	}
	
	return &DAGEdge{
		ID:    fmt.Sprintf("edge-%d", b.edgeCounter),
		From:  from,
		To:    to,
		Type:  edgeType,
		Label: edgeType,
		Style: style,
	}
}

// calculateLayout calculates the visual layout of the DAG
func (b *DAGBuilder) calculateLayout(dag *PipelineDAG) error {
	switch dag.Layout.Algorithm {
	case "hierarchical":
		return b.calculateHierarchicalLayout(dag)
	case "force":
		return b.calculateForceLayout(dag)
	default:
		return b.calculateHierarchicalLayout(dag)
	}
}

// calculateHierarchicalLayout calculates a hierarchical layout
func (b *DAGBuilder) calculateHierarchicalLayout(dag *PipelineDAG) error {
	// Topological sort to determine levels
	levels, err := b.topologicalSort(dag)
	if err != nil {
		return err
	}
	
	// Position nodes based on levels
	spacing := dag.Layout.Spacing
	
	for level, nodes := range levels {
		y := level * spacing.LevelSpacing
		
		// Center nodes horizontally within the level
		totalWidth := (len(nodes) - 1) * spacing.NodeSpacing
		startX := -totalWidth / 2
		
		for i, nodeID := range nodes {
			if node, exists := dag.Nodes[nodeID]; exists {
				node.Position.X = startX + (i * spacing.NodeSpacing)
				node.Position.Y = y
			}
		}
	}
	
	return nil
}

// calculateForceLayout calculates a force-directed layout
func (b *DAGBuilder) calculateForceLayout(dag *PipelineDAG) error {
	// Simple force-directed layout implementation
	// In a real implementation, you'd use a more sophisticated algorithm
	
	nodeCount := len(dag.Nodes)
	if nodeCount == 0 {
		return nil
	}
	
	// Initialize random positions
	i := 0
	for _, node := range dag.Nodes {
		angle := float64(i) * 2.0 * 3.14159 / float64(nodeCount)
		radius := 200.0
		
		node.Position.X = int(radius * float64(i%5) * 0.8)
		node.Position.Y = int(radius * float64(i/5) * 0.8)
		i++
	}
	
	return nil
}

// topologicalSort performs topological sorting to determine node levels
func (b *DAGBuilder) topologicalSort(dag *PipelineDAG) ([][]string, error) {
	// Calculate in-degrees
	inDegree := make(map[string]int)
	for nodeID := range dag.Nodes {
		inDegree[nodeID] = 0
	}
	
	for _, edge := range dag.Edges {
		if edge.Type == "dependency" || edge.Type == "contains" {
			inDegree[edge.To]++
		}
	}
	
	// Find nodes with no incoming edges
	queue := make([]string, 0)
	for nodeID, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, nodeID)
		}
	}
	
	levels := make([][]string, 0)
	processed := make(map[string]bool)
	
	for len(queue) > 0 {
		currentLevel := make([]string, len(queue))
		copy(currentLevel, queue)
		levels = append(levels, currentLevel)
		
		nextQueue := make([]string, 0)
		
		for _, nodeID := range queue {
			processed[nodeID] = true
			
			// Find all nodes that depend on this node
			for _, edge := range dag.Edges {
				if edge.From == nodeID && (edge.Type == "dependency" || edge.Type == "contains") {
					inDegree[edge.To]--
					if inDegree[edge.To] == 0 && !processed[edge.To] {
						nextQueue = append(nextQueue, edge.To)
					}
				}
			}
		}
		
		queue = nextQueue
	}
	
	// Check for cycles
	if len(processed) != len(dag.Nodes) {
		return nil, fmt.Errorf("cycle detected in pipeline DAG")
	}
	
	return levels, nil
}

// GetExecutionOrder returns the execution order of nodes
func (dag *PipelineDAG) GetExecutionOrder() ([]string, error) {
	builder := NewDAGBuilder()
	levels, err := builder.topologicalSort(dag)
	if err != nil {
		return nil, err
	}
	
	var order []string
	for _, level := range levels {
		// Sort nodes within each level for consistent ordering
		sort.Strings(level)
		order = append(order, level...)
	}
	
	return order, nil
}

// GetNodesByType returns nodes filtered by type
func (dag *PipelineDAG) GetNodesByType(nodeType string) []*DAGNode {
	var nodes []*DAGNode
	for _, node := range dag.Nodes {
		if node.Type == nodeType {
			nodes = append(nodes, node)
		}
	}
	return nodes
}

// GetNodeDependencies returns the dependencies of a node
func (dag *PipelineDAG) GetNodeDependencies(nodeID string) []*DAGNode {
	var dependencies []*DAGNode
	
	if node, exists := dag.Nodes[nodeID]; exists {
		for _, depID := range node.Dependencies {
			if depNode, exists := dag.Nodes[depID]; exists {
				dependencies = append(dependencies, depNode)
			}
		}
	}
	
	return dependencies
}

// GetNodeDependents returns the dependents of a node
func (dag *PipelineDAG) GetNodeDependents(nodeID string) []*DAGNode {
	var dependents []*DAGNode
	
	if node, exists := dag.Nodes[nodeID]; exists {
		for _, depID := range node.Dependents {
			if depNode, exists := dag.Nodes[depID]; exists {
				dependents = append(dependents, depNode)
			}
		}
	}
	
	return dependents
}

// UpdateNodeStatus updates the status of a node
func (dag *PipelineDAG) UpdateNodeStatus(nodeID, status string) {
	if node, exists := dag.Nodes[nodeID]; exists {
		node.Status = status
	}
}

// GetCriticalPath returns the critical path through the DAG
func (dag *PipelineDAG) GetCriticalPath() []string {
	// Simple implementation - in practice, you'd calculate based on execution times
	order, err := dag.GetExecutionOrder()
	if err != nil {
		return nil
	}
	
	// For now, return the longest path
	return order
}

// ToJSON converts the DAG to JSON representation
func (dag *PipelineDAG) ToJSON() map[string]interface{} {
	return map[string]interface{}{
		"id":          dag.ID,
		"name":        dag.Name,
		"description": dag.Description,
		"nodes":       dag.Nodes,
		"edges":       dag.Edges,
		"layout":      dag.Layout,
		"metadata":    dag.Metadata,
	}
}

// FromJSON creates a DAG from JSON representation
func (dag *PipelineDAG) FromJSON(data map[string]interface{}) error {
	// Implementation would deserialize JSON back to DAG structure
	// This is a placeholder for the actual implementation
	return fmt.Errorf("FromJSON not implemented yet")
}

// Validate validates the DAG structure
func (dag *PipelineDAG) Validate() error {
	// Check for cycles
	builder := NewDAGBuilder()
	_, err := builder.topologicalSort(dag)
	if err != nil {
		return err
	}
	
	// Validate edges reference existing nodes
	for _, edge := range dag.Edges {
		if _, exists := dag.Nodes[edge.From]; !exists {
			return fmt.Errorf("edge %s references non-existent source node %s", edge.ID, edge.From)
		}
		if _, exists := dag.Nodes[edge.To]; !exists {
			return fmt.Errorf("edge %s references non-existent target node %s", edge.ID, edge.To)
		}
	}
	
	return nil
}

// Clone creates a deep copy of the DAG
func (dag *PipelineDAG) Clone() *PipelineDAG {
	clone := &PipelineDAG{
		ID:          dag.ID,
		Name:        dag.Name,
		Description: dag.Description,
		Nodes:       make(map[string]*DAGNode),
		Edges:       make(map[string]*DAGEdge),
		Layout:      dag.Layout,
		Metadata:    make(map[string]interface{}),
	}
	
	// Clone nodes
	for id, node := range dag.Nodes {
		clonedNode := *node
		clonedNode.Dependencies = make([]string, len(node.Dependencies))
		copy(clonedNode.Dependencies, node.Dependencies)
		clonedNode.Dependents = make([]string, len(node.Dependents))
		copy(clonedNode.Dependents, node.Dependents)
		clone.Nodes[id] = &clonedNode
	}
	
	// Clone edges
	for id, edge := range dag.Edges {
		clonedEdge := *edge
		clone.Edges[id] = &clonedEdge
	}
	
	// Clone metadata
	for key, value := range dag.Metadata {
		clone.Metadata[key] = value
	}
	
	return clone
}
