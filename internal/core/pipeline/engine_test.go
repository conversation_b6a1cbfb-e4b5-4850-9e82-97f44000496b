package pipeline

import (
"testing"

"github.com/chainops/chainops/pkg/types"
"github.com/stretchr/testify/assert"
)

func TestEngine_validatePipelineConfig(t *testing.T) {
engine := &Engine{
logger: nil,
}

// Test valid config
validConfig := &types.PipelineConfig{
Name: "test",
Stages: []types.Stage{
{
Name: "test-stage",
Steps: []types.Step{
{
Name: "test-step",
Run:  "echo 'test'",
},
},
},
},
}

err := engine.validatePipelineConfig(validConfig)
assert.NoError(t, err)

// Test invalid config - no name
invalidConfig := &types.PipelineConfig{
Stages: []types.Stage{
{
Name: "test-stage",
Steps: []types.Step{
{
Name: "test-step",
Run:  "echo 'test'",
},
},
},
},
}

err = engine.validatePipelineConfig(invalidConfig)
assert.Error(t, err)
assert.Contains(t, err.Error(), "pipeline name is required")
}

func TestEngine_mergeEnvironment(t *testing.T) {
stageEnv := map[string]string{
"STAGE_VAR": "stage_value",
"COMMON":    "stage_common",
}

stepEnv := map[string]string{
"STEP_VAR": "step_value",
"COMMON":   "step_common",
}

result := mergeEnvironment(stageEnv, stepEnv)

assert.Equal(t, "stage_value", result["STAGE_VAR"])
assert.Equal(t, "step_value", result["STEP_VAR"])
assert.Equal(t, "step_common", result["COMMON"])
}
