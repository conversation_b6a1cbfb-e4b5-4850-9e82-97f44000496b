package pipeline

import (
	"context"
	"testing"
	"time"

	"github.com/chainops/chainops/pkg/types"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockExecutor is a mock implementation of the Executor interface
type MockExecutor struct {
	mock.Mock
}

func (m *MockExecutor) Execute(ctx context.Context, job *types.Job, execution *types.Execution) (*types.JobResult, error) {
	args := m.Called(ctx, job, execution)
	return args.Get(0).(*types.JobResult), args.Error(1)
}

func (m *MockExecutor) Cancel(ctx context.Context, executionID uuid.UUID) error {
	args := m.Called(ctx, executionID)
	return args.Error(0)
}

func (m *MockExecutor) GetStatus(ctx context.Context, executionID uuid.UUID) (*types.ExecutionStatus, error) {
	args := m.Called(ctx, executionID)
	return args.Get(0).(*types.ExecutionStatus), args.Error(1)
}

// MockRepository is a mock implementation of repository interfaces
type MockPipelineRepository struct {
	mock.Mock
}

func (m *MockPipelineRepository) Create(pipeline *types.Pipeline) error {
	args := m.Called(pipeline)
	return args.Error(0)
}

func (m *MockPipelineRepository) GetByID(id uuid.UUID) (*types.Pipeline, error) {
	args := m.Called(id)
	return args.Get(0).(*types.Pipeline), args.Error(1)
}

func (m *MockPipelineRepository) Update(pipeline *types.Pipeline) error {
	args := m.Called(pipeline)
	return args.Error(0)
}

func (m *MockPipelineRepository) Delete(id uuid.UUID) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockPipelineRepository) List(limit, offset int) ([]*types.Pipeline, error) {
	args := m.Called(limit, offset)
	return args.Get(0).([]*types.Pipeline), args.Error(1)
}

func (m *MockPipelineRepository) GetByRepository(repository string) ([]*types.Pipeline, error) {
	args := m.Called(repository)
	return args.Get(0).([]*types.Pipeline), args.Error(1)
}

type MockExecutionRepository struct {
	mock.Mock
}

func (m *MockExecutionRepository) Create(execution *types.Execution) error {
	args := m.Called(execution)
	return args.Error(0)
}

func (m *MockExecutionRepository) GetByID(id uuid.UUID) (*types.Execution, error) {
	args := m.Called(id)
	return args.Get(0).(*types.Execution), args.Error(1)
}

func (m *MockExecutionRepository) Update(execution *types.Execution) error {
	args := m.Called(execution)
	return args.Error(0)
}

func (m *MockExecutionRepository) GetByPipelineID(pipelineID uuid.UUID, limit, offset int) ([]*types.Execution, error) {
	args := m.Called(pipelineID, limit, offset)
	return args.Get(0).([]*types.Execution), args.Error(1)
}

func TestEngine_ExecutePipeline(t *testing.T) {
	// Setup
	mockExecutor := new(MockExecutor)
	mockPipelineRepo := new(MockPipelineRepository)
	mockExecutionRepo := new(MockExecutionRepository)

	engine := &Engine{
		executor:        mockExecutor,
		pipelineRepo:    mockPipelineRepo,
		executionRepo:   mockExecutionRepo,
		logger:          nil, // Use nil for testing
	}

	// Test data
	pipelineID := uuid.New()
	pipeline := &types.Pipeline{
		ID:           pipelineID,
		Name:         "Test Pipeline",
		Repository:   "https://github.com/test/repo",
		YAMLContent:  "stages:\n  - name: test\n    jobs:\n      - name: test-job\n        image: alpine\n        script:\n          - echo 'test'",
		IsActive:     true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	execution := &types.Execution{
		ID:         uuid.New(),
		PipelineID: pipelineID,
		Status:     types.StatusPending,
		CreatedAt:  time.Now(),
	}

	// Mock expectations
	mockPipelineRepo.On("GetByID", pipelineID).Return(pipeline, nil)
	mockExecutionRepo.On("Create", mock.AnythingOfType("*types.Execution")).Return(nil)
	mockExecutionRepo.On("Update", mock.AnythingOfType("*types.Execution")).Return(nil)

	// Execute
	ctx := context.Background()
	result, err := engine.ExecutePipeline(ctx, pipelineID, map[string]string{})

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, pipelineID, result.PipelineID)

	// Verify mocks
	mockPipelineRepo.AssertExpectations(t)
	mockExecutionRepo.AssertExpectations(t)
}

func TestEngine_CancelExecution(t *testing.T) {
	// Setup
	mockExecutor := new(MockExecutor)
	mockExecutionRepo := new(MockExecutionRepository)

	engine := &Engine{
		executor:      mockExecutor,
		executionRepo: mockExecutionRepo,
		logger:        nil,
	}

	// Test data
	executionID := uuid.New()
	execution := &types.Execution{
		ID:     executionID,
		Status: types.StatusRunning,
	}

	// Mock expectations
	mockExecutionRepo.On("GetByID", executionID).Return(execution, nil)
	mockExecutor.On("Cancel", mock.Anything, executionID).Return(nil)
	mockExecutionRepo.On("Update", mock.AnythingOfType("*types.Execution")).Return(nil)

	// Execute
	ctx := context.Background()
	err := engine.CancelExecution(ctx, executionID)

	// Assert
	assert.NoError(t, err)

	// Verify mocks
	mockExecutor.AssertExpectations(t)
	mockExecutionRepo.AssertExpectations(t)
}

func TestEngine_GetExecutionStatus(t *testing.T) {
	// Setup
	mockExecutionRepo := new(MockExecutionRepository)

	engine := &Engine{
		executionRepo: mockExecutionRepo,
		logger:        nil,
	}

	// Test data
	executionID := uuid.New()
	execution := &types.Execution{
		ID:     executionID,
		Status: types.StatusSuccess,
	}

	// Mock expectations
	mockExecutionRepo.On("GetByID", executionID).Return(execution, nil)

	// Execute
	ctx := context.Background()
	status, err := engine.GetExecutionStatus(ctx, executionID)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, types.StatusSuccess, *status)

	// Verify mocks
	mockExecutionRepo.AssertExpectations(t)
}

func TestEngine_ParsePipelineYAML(t *testing.T) {
	engine := &Engine{}

	yamlContent := `
stages:
  - name: build
    jobs:
      - name: compile
        image: golang:1.21
        script:
          - go build ./...
      - name: test
        image: golang:1.21
        script:
          - go test ./...
  - name: deploy
    jobs:
      - name: deploy-staging
        image: alpine
        script:
          - echo "Deploying to staging"
        approval_required: true
`

	config, err := engine.ParsePipelineYAML(yamlContent)

	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Len(t, config.Stages, 2)
	assert.Equal(t, "build", config.Stages[0].Name)
	assert.Len(t, config.Stages[0].Jobs, 2)
	assert.Equal(t, "compile", config.Stages[0].Jobs[0].Name)
	assert.True(t, config.Stages[1].Jobs[0].ApprovalRequired)
}
