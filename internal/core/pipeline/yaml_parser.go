package pipeline

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"gopkg.in/yaml.v3"
)

// EnhancedPipelineConfig represents an enhanced pipeline configuration with advanced features
type EnhancedPipelineConfig struct {
	Name        string                 `yaml:"name" json:"name"`
	Description string                 `yaml:"description,omitempty" json:"description,omitempty"`
	Version     string                 `yaml:"version,omitempty" json:"version,omitempty"`
	
	// Include and extend functionality
	Include []string                   `yaml:"include,omitempty" json:"include,omitempty"`
	Extends string                     `yaml:"extends,omitempty" json:"extends,omitempty"`
	
	// Variables and secrets
	Variables map[string]interface{}   `yaml:"variables,omitempty" json:"variables,omitempty"`
	Secrets   []string                 `yaml:"secrets,omitempty" json:"secrets,omitempty"`
	
	// Triggers
	Triggers []TriggerConfig           `yaml:"triggers,omitempty" json:"triggers,omitempty"`
	
	// Environment and services
	Environment map[string]string      `yaml:"environment,omitempty" json:"environment,omitempty"`
	Services    map[string]ServiceConfig `yaml:"services,omitempty" json:"services,omitempty"`
	
	// Matrix builds
	Matrix MatrixConfig               `yaml:"matrix,omitempty" json:"matrix,omitempty"`
	
	// Stages and jobs
	Stages []StageConfig              `yaml:"stages" json:"stages"`
	
	// Workflow configuration
	Workflow WorkflowConfig           `yaml:"workflow,omitempty" json:"workflow,omitempty"`
	
	// Notifications
	Notifications NotificationConfig   `yaml:"notifications,omitempty" json:"notifications,omitempty"`
	
	// Cache configuration
	Cache CacheConfig                 `yaml:"cache,omitempty" json:"cache,omitempty"`
	
	// Artifacts
	Artifacts ArtifactConfig          `yaml:"artifacts,omitempty" json:"artifacts,omitempty"`
}

// TriggerConfig represents trigger configuration
type TriggerConfig struct {
	On       []string          `yaml:"on" json:"on"`                               // push, pull_request, schedule, manual
	Branches []string          `yaml:"branches,omitempty" json:"branches,omitempty"`
	Tags     []string          `yaml:"tags,omitempty" json:"tags,omitempty"`
	Paths    []string          `yaml:"paths,omitempty" json:"paths,omitempty"`
	Schedule string            `yaml:"schedule,omitempty" json:"schedule,omitempty"` // cron expression
	Webhook  WebhookConfig     `yaml:"webhook,omitempty" json:"webhook,omitempty"`
	Manual   ManualTriggerConfig `yaml:"manual,omitempty" json:"manual,omitempty"`
}

// WebhookConfig represents webhook trigger configuration
type WebhookConfig struct {
	URL     string            `yaml:"url" json:"url"`
	Headers map[string]string `yaml:"headers,omitempty" json:"headers,omitempty"`
	Secret  string            `yaml:"secret,omitempty" json:"secret,omitempty"`
}

// ManualTriggerConfig represents manual trigger configuration
type ManualTriggerConfig struct {
	Inputs []InputConfig `yaml:"inputs,omitempty" json:"inputs,omitempty"`
}

// InputConfig represents input configuration for manual triggers
type InputConfig struct {
	Name        string      `yaml:"name" json:"name"`
	Description string      `yaml:"description,omitempty" json:"description,omitempty"`
	Type        string      `yaml:"type" json:"type"` // string, number, boolean, choice
	Default     interface{} `yaml:"default,omitempty" json:"default,omitempty"`
	Required    bool        `yaml:"required,omitempty" json:"required,omitempty"`
	Options     []string    `yaml:"options,omitempty" json:"options,omitempty"` // for choice type
}

// ServiceConfig represents service configuration
type ServiceConfig struct {
	Image       string            `yaml:"image" json:"image"`
	Environment map[string]string `yaml:"environment,omitempty" json:"environment,omitempty"`
	Ports       []string          `yaml:"ports,omitempty" json:"ports,omitempty"`
	Volumes     []string          `yaml:"volumes,omitempty" json:"volumes,omitempty"`
	Command     []string          `yaml:"command,omitempty" json:"command,omitempty"`
	HealthCheck HealthCheckConfig `yaml:"health_check,omitempty" json:"health_check,omitempty"`
}

// HealthCheckConfig represents health check configuration
type HealthCheckConfig struct {
	Test     []string `yaml:"test" json:"test"`
	Interval string   `yaml:"interval,omitempty" json:"interval,omitempty"`
	Timeout  string   `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	Retries  int      `yaml:"retries,omitempty" json:"retries,omitempty"`
}

// MatrixConfig represents matrix build configuration
type MatrixConfig struct {
	Include []map[string]interface{} `yaml:"include,omitempty" json:"include,omitempty"`
	Exclude []map[string]interface{} `yaml:"exclude,omitempty" json:"exclude,omitempty"`
	Strategy MatrixStrategy          `yaml:"strategy,omitempty" json:"strategy,omitempty"`
}

// MatrixStrategy represents matrix build strategy
type MatrixStrategy struct {
	FailFast    bool `yaml:"fail_fast,omitempty" json:"fail_fast,omitempty"`
	MaxParallel int  `yaml:"max_parallel,omitempty" json:"max_parallel,omitempty"`
}

// StageConfig represents stage configuration with enhanced features
type StageConfig struct {
	Name        string                 `yaml:"name" json:"name"`
	DisplayName string                 `yaml:"display_name,omitempty" json:"display_name,omitempty"`
	Description string                 `yaml:"description,omitempty" json:"description,omitempty"`
	Condition   string                 `yaml:"condition,omitempty" json:"condition,omitempty"`
	DependsOn   []string               `yaml:"depends_on,omitempty" json:"depends_on,omitempty"`
	Environment map[string]string      `yaml:"environment,omitempty" json:"environment,omitempty"`
	Variables   map[string]interface{} `yaml:"variables,omitempty" json:"variables,omitempty"`
	Matrix      MatrixConfig           `yaml:"matrix,omitempty" json:"matrix,omitempty"`
	Steps       []StepConfig           `yaml:"steps" json:"steps"`
	OnFailure   []StepConfig           `yaml:"on_failure,omitempty" json:"on_failure,omitempty"`
	OnSuccess   []StepConfig           `yaml:"on_success,omitempty" json:"on_success,omitempty"`
	Finally     []StepConfig           `yaml:"finally,omitempty" json:"finally,omitempty"`
	Timeout     string                 `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	Retry       RetryConfig            `yaml:"retry,omitempty" json:"retry,omitempty"`
}

// StepConfig represents step configuration with enhanced features
type StepConfig struct {
	Name        string                 `yaml:"name" json:"name"`
	DisplayName string                 `yaml:"display_name,omitempty" json:"display_name,omitempty"`
	Description string                 `yaml:"description,omitempty" json:"description,omitempty"`
	Uses        string                 `yaml:"uses,omitempty" json:"uses,omitempty"`         // plugin or action
	Run         string                 `yaml:"run,omitempty" json:"run,omitempty"`           // shell command
	Script      []string               `yaml:"script,omitempty" json:"script,omitempty"`     // multi-line script
	With        map[string]interface{} `yaml:"with,omitempty" json:"with,omitempty"`         // plugin inputs
	Environment map[string]string      `yaml:"environment,omitempty" json:"environment,omitempty"`
	WorkingDir  string                 `yaml:"working_dir,omitempty" json:"working_dir,omitempty"`
	Shell       string                 `yaml:"shell,omitempty" json:"shell,omitempty"`       // bash, sh, powershell, cmd
	Condition   string                 `yaml:"condition,omitempty" json:"condition,omitempty"`
	ContinueOnError bool               `yaml:"continue_on_error,omitempty" json:"continue_on_error,omitempty"`
	Timeout     string                 `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	Retry       RetryConfig            `yaml:"retry,omitempty" json:"retry,omitempty"`
	
	// Approval configuration
	Approval ApprovalConfig           `yaml:"approval,omitempty" json:"approval,omitempty"`
	
	// Deployment configuration
	Deploy DeployConfig              `yaml:"deploy,omitempty" json:"deploy,omitempty"`
	
	// Artifacts
	Artifacts StepArtifactConfig      `yaml:"artifacts,omitempty" json:"artifacts,omitempty"`
}

// RetryConfig represents retry configuration
type RetryConfig struct {
	Count    int      `yaml:"count,omitempty" json:"count,omitempty"`
	Delay    string   `yaml:"delay,omitempty" json:"delay,omitempty"`
	Backoff  string   `yaml:"backoff,omitempty" json:"backoff,omitempty"` // linear, exponential
	OnErrors []string `yaml:"on_errors,omitempty" json:"on_errors,omitempty"`
}

// ApprovalConfig represents approval configuration
type ApprovalConfig struct {
	Required    bool     `yaml:"required" json:"required"`
	Approvers   []string `yaml:"approvers,omitempty" json:"approvers,omitempty"`
	MinApprovals int     `yaml:"min_approvals,omitempty" json:"min_approvals,omitempty"`
	Timeout     string   `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	Message     string   `yaml:"message,omitempty" json:"message,omitempty"`
}

// DeployConfig represents deployment configuration
type DeployConfig struct {
	Environment string            `yaml:"environment" json:"environment"`
	Strategy    string            `yaml:"strategy,omitempty" json:"strategy,omitempty"` // rolling, blue_green, canary
	Target      DeployTarget      `yaml:"target" json:"target"`
	Options     map[string]interface{} `yaml:"options,omitempty" json:"options,omitempty"`
}

// DeployTarget represents deployment target
type DeployTarget struct {
	Type       string            `yaml:"type" json:"type"` // kubernetes, docker, ssh, cloud
	Config     map[string]interface{} `yaml:"config" json:"config"`
	Namespace  string            `yaml:"namespace,omitempty" json:"namespace,omitempty"`
	Cluster    string            `yaml:"cluster,omitempty" json:"cluster,omitempty"`
}

// WorkflowConfig represents workflow configuration
type WorkflowConfig struct {
	Concurrency ConcurrencyConfig `yaml:"concurrency,omitempty" json:"concurrency,omitempty"`
	Permissions PermissionConfig  `yaml:"permissions,omitempty" json:"permissions,omitempty"`
	Defaults    DefaultsConfig    `yaml:"defaults,omitempty" json:"defaults,omitempty"`
}

// ConcurrencyConfig represents concurrency configuration
type ConcurrencyConfig struct {
	Group            string `yaml:"group,omitempty" json:"group,omitempty"`
	CancelInProgress bool   `yaml:"cancel_in_progress,omitempty" json:"cancel_in_progress,omitempty"`
}

// PermissionConfig represents permission configuration
type PermissionConfig struct {
	Contents string `yaml:"contents,omitempty" json:"contents,omitempty"` // read, write, none
	Issues   string `yaml:"issues,omitempty" json:"issues,omitempty"`
	PRs      string `yaml:"pull_requests,omitempty" json:"pull_requests,omitempty"`
}

// DefaultsConfig represents default configuration
type DefaultsConfig struct {
	Run RunDefaults `yaml:"run,omitempty" json:"run,omitempty"`
}

// RunDefaults represents default run configuration
type RunDefaults struct {
	Shell        string `yaml:"shell,omitempty" json:"shell,omitempty"`
	WorkingDir   string `yaml:"working_directory,omitempty" json:"working_directory,omitempty"`
}

// NotificationConfig represents notification configuration
type NotificationConfig struct {
	Slack   SlackConfig   `yaml:"slack,omitempty" json:"slack,omitempty"`
	Email   EmailConfig   `yaml:"email,omitempty" json:"email,omitempty"`
	Webhook WebhookNotificationConfig `yaml:"webhook,omitempty" json:"webhook,omitempty"`
	OnSuccess bool        `yaml:"on_success,omitempty" json:"on_success,omitempty"`
	OnFailure bool        `yaml:"on_failure,omitempty" json:"on_failure,omitempty"`
}

// SlackConfig represents Slack notification configuration
type SlackConfig struct {
	Channel   string `yaml:"channel" json:"channel"`
	Username  string `yaml:"username,omitempty" json:"username,omitempty"`
	IconEmoji string `yaml:"icon_emoji,omitempty" json:"icon_emoji,omitempty"`
	Template  string `yaml:"template,omitempty" json:"template,omitempty"`
}

// EmailConfig represents email notification configuration
type EmailConfig struct {
	To       []string `yaml:"to" json:"to"`
	Subject  string   `yaml:"subject,omitempty" json:"subject,omitempty"`
	Template string   `yaml:"template,omitempty" json:"template,omitempty"`
}

// WebhookNotificationConfig represents webhook notification configuration
type WebhookNotificationConfig struct {
	URL     string            `yaml:"url" json:"url"`
	Headers map[string]string `yaml:"headers,omitempty" json:"headers,omitempty"`
	Payload string            `yaml:"payload,omitempty" json:"payload,omitempty"`
}

// CacheConfig represents cache configuration
type CacheConfig struct {
	Paths []string          `yaml:"paths" json:"paths"`
	Key   string            `yaml:"key" json:"key"`
	RestoreKeys []string    `yaml:"restore_keys,omitempty" json:"restore_keys,omitempty"`
}

// ArtifactConfig represents artifact configuration
type ArtifactConfig struct {
	Paths      []string `yaml:"paths" json:"paths"`
	Name       string   `yaml:"name,omitempty" json:"name,omitempty"`
	Retention  string   `yaml:"retention,omitempty" json:"retention,omitempty"`
	Compression string  `yaml:"compression,omitempty" json:"compression,omitempty"`
}

// StepArtifactConfig represents step-level artifact configuration
type StepArtifactConfig struct {
	Upload   ArtifactConfig `yaml:"upload,omitempty" json:"upload,omitempty"`
	Download ArtifactConfig `yaml:"download,omitempty" json:"download,omitempty"`
}

// EnhancedYAMLParser provides enhanced YAML parsing capabilities
type EnhancedYAMLParser struct {
	variableResolver *VariableResolver
}

// NewEnhancedYAMLParser creates a new enhanced YAML parser
func NewEnhancedYAMLParser() *EnhancedYAMLParser {
	return &EnhancedYAMLParser{
		variableResolver: NewVariableResolver(),
	}
}

// ParseYAML parses enhanced pipeline YAML
func (p *EnhancedYAMLParser) ParseYAML(yamlContent string) (*EnhancedPipelineConfig, error) {
	var config EnhancedPipelineConfig
	
	if err := yaml.Unmarshal([]byte(yamlContent), &config); err != nil {
		return nil, fmt.Errorf("failed to parse YAML: %w", err)
	}
	
	// Validate the configuration
	if err := p.validateConfig(&config); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	
	// Resolve variables and expressions
	if err := p.resolveVariables(&config); err != nil {
		return nil, fmt.Errorf("variable resolution failed: %w", err)
	}
	
	return &config, nil
}

// validateConfig validates the pipeline configuration
func (p *EnhancedYAMLParser) validateConfig(config *EnhancedPipelineConfig) error {
	if config.Name == "" {
		return fmt.Errorf("pipeline name is required")
	}
	
	if len(config.Stages) == 0 {
		return fmt.Errorf("at least one stage is required")
	}
	
	// Validate stages
	for i, stage := range config.Stages {
		if stage.Name == "" {
			return fmt.Errorf("stage %d: name is required", i)
		}
		
		if len(stage.Steps) == 0 {
			return fmt.Errorf("stage %s: at least one step is required", stage.Name)
		}
		
		// Validate steps
		for j, step := range stage.Steps {
			if step.Name == "" {
				return fmt.Errorf("stage %s, step %d: name is required", stage.Name, j)
			}
			
			if step.Uses == "" && step.Run == "" && len(step.Script) == 0 {
				return fmt.Errorf("stage %s, step %s: must specify 'uses', 'run', or 'script'", stage.Name, step.Name)
			}
		}
	}
	
	return nil
}

// resolveVariables resolves variables and expressions in the configuration
func (p *EnhancedYAMLParser) resolveVariables(config *EnhancedPipelineConfig) error {
	// Set up variable context
	context := make(map[string]interface{})
	
	// Add pipeline variables
	for key, value := range config.Variables {
		context[key] = value
	}
	
	// Add environment variables
	for key, value := range config.Environment {
		context["env."+key] = value
	}
	
	// Resolve variables in stages and steps
	for i := range config.Stages {
		if err := p.resolveStageVariables(&config.Stages[i], context); err != nil {
			return fmt.Errorf("stage %s: %w", config.Stages[i].Name, err)
		}
	}
	
	return nil
}

// resolveStageVariables resolves variables in a stage
func (p *EnhancedYAMLParser) resolveStageVariables(stage *StageConfig, context map[string]interface{}) error {
	// Create stage context
	stageContext := make(map[string]interface{})
	for k, v := range context {
		stageContext[k] = v
	}
	
	// Add stage variables
	for key, value := range stage.Variables {
		stageContext[key] = value
	}
	
	// Resolve variables in steps
	for i := range stage.Steps {
		if err := p.resolveStepVariables(&stage.Steps[i], stageContext); err != nil {
			return fmt.Errorf("step %s: %w", stage.Steps[i].Name, err)
		}
	}
	
	return nil
}

// resolveStepVariables resolves variables in a step
func (p *EnhancedYAMLParser) resolveStepVariables(step *StepConfig, context map[string]interface{}) error {
	// Resolve run command
	if step.Run != "" {
		resolved, err := p.variableResolver.Resolve(step.Run, context)
		if err != nil {
			return fmt.Errorf("failed to resolve run command: %w", err)
		}
		step.Run = resolved
	}
	
	// Resolve script commands
	for i, script := range step.Script {
		resolved, err := p.variableResolver.Resolve(script, context)
		if err != nil {
			return fmt.Errorf("failed to resolve script command %d: %w", i, err)
		}
		step.Script[i] = resolved
	}
	
	// Resolve environment variables
	for key, value := range step.Environment {
		resolved, err := p.variableResolver.Resolve(value, context)
		if err != nil {
			return fmt.Errorf("failed to resolve environment variable %s: %w", key, err)
		}
		step.Environment[key] = resolved
	}
	
	return nil
}

// VariableResolver resolves variables and expressions
type VariableResolver struct {
	variablePattern *regexp.Regexp
}

// NewVariableResolver creates a new variable resolver
func NewVariableResolver() *VariableResolver {
	return &VariableResolver{
		variablePattern: regexp.MustCompile(`\$\{\{\s*([^}]+)\s*\}\}`),
	}
}

// Resolve resolves variables in a string
func (r *VariableResolver) Resolve(input string, context map[string]interface{}) (string, error) {
	return r.variablePattern.ReplaceAllStringFunc(input, func(match string) string {
		// Extract variable expression
		expr := r.variablePattern.FindStringSubmatch(match)[1]
		expr = strings.TrimSpace(expr)
		
		// Resolve the expression
		value, err := r.resolveExpression(expr, context)
		if err != nil {
			return match // Return original if resolution fails
		}
		
		return fmt.Sprintf("%v", value)
	}), nil
}

// resolveExpression resolves a variable expression
func (r *VariableResolver) resolveExpression(expr string, context map[string]interface{}) (interface{}, error) {
	// Handle simple variable references
	if value, exists := context[expr]; exists {
		return value, nil
	}
	
	// Handle nested property access (e.g., "env.VAR_NAME")
	if strings.Contains(expr, ".") {
		parts := strings.Split(expr, ".")
		current := context
		
		for i, part := range parts {
			if i == len(parts)-1 {
				if value, exists := current[part]; exists {
					return value, nil
				}
			} else {
				if next, exists := current[part]; exists {
					if nextMap, ok := next.(map[string]interface{}); ok {
						current = nextMap
					} else {
						break
					}
				} else {
					break
				}
			}
		}
	}
	
	// Handle function calls (basic implementation)
	if strings.Contains(expr, "(") && strings.Contains(expr, ")") {
		return r.resolveFunctionCall(expr, context)
	}
	
	return nil, fmt.Errorf("variable not found: %s", expr)
}

// resolveFunctionCall resolves function calls in expressions
func (r *VariableResolver) resolveFunctionCall(expr string, context map[string]interface{}) (interface{}, error) {
	// Basic function call parsing
	if strings.HasPrefix(expr, "length(") && strings.HasSuffix(expr, ")") {
		arg := strings.TrimSpace(expr[7 : len(expr)-1])
		value, err := r.resolveExpression(arg, context)
		if err != nil {
			return nil, err
		}
		
		switch v := value.(type) {
		case string:
			return len(v), nil
		case []interface{}:
			return len(v), nil
		case map[string]interface{}:
			return len(v), nil
		default:
			return 0, nil
		}
	}
	
	if strings.HasPrefix(expr, "upper(") && strings.HasSuffix(expr, ")") {
		arg := strings.TrimSpace(expr[6 : len(expr)-1])
		value, err := r.resolveExpression(arg, context)
		if err != nil {
			return nil, err
		}
		
		if str, ok := value.(string); ok {
			return strings.ToUpper(str), nil
		}
		return value, nil
	}
	
	if strings.HasPrefix(expr, "lower(") && strings.HasSuffix(expr, ")") {
		arg := strings.TrimSpace(expr[6 : len(expr)-1])
		value, err := r.resolveExpression(arg, context)
		if err != nil {
			return nil, err
		}
		
		if str, ok := value.(string); ok {
			return strings.ToLower(str), nil
		}
		return value, nil
	}
	
	return nil, fmt.Errorf("unknown function call: %s", expr)
}
