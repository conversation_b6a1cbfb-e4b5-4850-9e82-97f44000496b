package approval

import (
	"context"
	"fmt"
	"time"

	"github.com/chainops/chainops/internal/storage"
	"github.com/chainops/chainops/pkg/types"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// Manager handles manual approvals for pipeline jobs
type Manager struct {
	logger       *logrus.Logger
	approvalRepo *storage.ApprovalRepository
	jobRepo      *storage.JobRepository
	userRepo     *storage.UserRepository
	notifier     NotificationService
}

// NotificationService interface for sending approval notifications
type NotificationService interface {
	SendApprovalRequest(ctx context.Context, approval *types.Approval, approvers []types.User) error
	SendApprovalDecision(ctx context.Context, approval *types.Approval, decision string) error
}

// NewManager creates a new approval manager
func NewManager(
	logger *logrus.Logger,
	approvalRepo *storage.ApprovalRepository,
	jobRepo *storage.JobRepository,
	userRepo *storage.UserRepository,
	notifier NotificationService,
) *Manager {
	return &Manager{
		logger:       logger,
		approvalRepo: approvalRepo,
		jobRepo:      jobRepo,
		userRepo:     userRepo,
		notifier:     notifier,
	}
}

// CreateApprovalRequest creates a new approval request for a job
func (m *Manager) CreateApprovalRequest(ctx context.Context, req CreateApprovalRequest) (*types.Approval, error) {
	// Validate the job exists and requires approval
	job, err := m.jobRepo.GetByID(req.JobID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get job: %w", err)
	}

	if !job.ManualApproval {
		return nil, fmt.Errorf("job does not require manual approval")
	}

	// Create approval record
	approval := &types.Approval{
		JobID:      req.JobID,
		Status:     types.ApprovalStatusPending,
		RequiredBy: req.RequiredBy,
		Message:    req.Message,
		ExpiresAt:  req.ExpiresAt,
	}

	if err := m.approvalRepo.Create(approval); err != nil {
		return nil, fmt.Errorf("failed to create approval: %w", err)
	}

	// Update job status to waiting for approval
	job.Status = types.StatusWaitingApproval
	if err := m.jobRepo.Update(job); err != nil {
		m.logger.WithError(err).Error("Failed to update job status to waiting approval")
	}

	// Send notifications to approvers
	if len(req.RequiredBy) > 0 {
		// Convert UUIDs to strings for the user repository
		userIDs := make([]string, len(req.RequiredBy))
		for i, id := range req.RequiredBy {
			userIDs[i] = id.String()
		}

		approverPtrs, err := m.userRepo.GetByIDs(userIDs)
		if err != nil {
			m.logger.WithError(err).Error("Failed to get approvers for notification")
		} else {
			// Convert []*types.User to []types.User
			approvers := make([]types.User, len(approverPtrs))
			for i, approver := range approverPtrs {
				approvers[i] = *approver
			}

			if err := m.notifier.SendApprovalRequest(ctx, approval, approvers); err != nil {
				m.logger.WithError(err).Error("Failed to send approval request notifications")
			}
		}
	}

	m.logger.WithFields(logrus.Fields{
		"approval_id": approval.ID,
		"job_id":      approval.JobID,
		"approvers":   len(req.RequiredBy),
	}).Info("Approval request created")

	return approval, nil
}

// ApproveJob approves a job
func (m *Manager) ApproveJob(ctx context.Context, req ApprovalDecisionRequest) error {
	approval, err := m.approvalRepo.GetByJobID(req.JobID)
	if err != nil {
		return fmt.Errorf("failed to get approval: %w", err)
	}

	if approval.Status != types.ApprovalStatusPending {
		return fmt.Errorf("approval is not in pending status")
	}

	// Check if the user is authorized to approve
	if !m.isAuthorizedApprover(req.UserID, approval.RequiredBy) {
		return fmt.Errorf("user is not authorized to approve this job")
	}

	// Check if approval has expired
	if approval.ExpiresAt != nil && time.Now().After(*approval.ExpiresAt) {
		approval.Status = types.ApprovalStatusExpired
		m.approvalRepo.Update(approval)
		return fmt.Errorf("approval has expired")
	}

	// Add user to approved list
	approval.ApprovedBy = append(approval.ApprovedBy, req.UserID)
	approval.Reason = req.Reason
	approval.Status = types.ApprovalStatusApproved

	if err := m.approvalRepo.Update(approval); err != nil {
		return fmt.Errorf("failed to update approval: %w", err)
	}

	// Update job status and approver
	job, err := m.jobRepo.GetByID(req.JobID.String())
	if err != nil {
		return fmt.Errorf("failed to get job: %w", err)
	}

	job.Status = types.StatusQueued // Ready to run
	job.ApprovedBy = &req.UserID
	job.ApprovedAt = &time.Time{}
	*job.ApprovedAt = time.Now()

	if err := m.jobRepo.Update(job); err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	// Send notification about approval decision
	if err := m.notifier.SendApprovalDecision(ctx, approval, "approved"); err != nil {
		m.logger.WithError(err).Error("Failed to send approval decision notification")
	}

	m.logger.WithFields(logrus.Fields{
		"approval_id": approval.ID,
		"job_id":      approval.JobID,
		"approved_by": req.UserID,
	}).Info("Job approved")

	return nil
}

// RejectJob rejects a job
func (m *Manager) RejectJob(ctx context.Context, req ApprovalDecisionRequest) error {
	approval, err := m.approvalRepo.GetByJobID(req.JobID)
	if err != nil {
		return fmt.Errorf("failed to get approval: %w", err)
	}

	if approval.Status != types.ApprovalStatusPending {
		return fmt.Errorf("approval is not in pending status")
	}

	// Check if the user is authorized to reject
	if !m.isAuthorizedApprover(req.UserID, approval.RequiredBy) {
		return fmt.Errorf("user is not authorized to reject this job")
	}

	// Update approval record
	approval.RejectedBy = &req.UserID
	approval.Reason = req.Reason
	approval.Status = types.ApprovalStatusRejected

	if err := m.approvalRepo.Update(approval); err != nil {
		return fmt.Errorf("failed to update approval: %w", err)
	}

	// Update job status
	job, err := m.jobRepo.GetByID(req.JobID.String())
	if err != nil {
		return fmt.Errorf("failed to get job: %w", err)
	}

	job.Status = types.StatusCancelled

	if err := m.jobRepo.Update(job); err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	// Send notification about rejection
	if err := m.notifier.SendApprovalDecision(ctx, approval, "rejected"); err != nil {
		m.logger.WithError(err).Error("Failed to send rejection notification")
	}

	m.logger.WithFields(logrus.Fields{
		"approval_id": approval.ID,
		"job_id":      approval.JobID,
		"rejected_by": req.UserID,
	}).Info("Job rejected")

	return nil
}

// GetPendingApprovals gets all pending approvals for a user
func (m *Manager) GetPendingApprovals(ctx context.Context, userID uuid.UUID) ([]*types.Approval, error) {
	return m.approvalRepo.GetPendingByUser(userID)
}

// GetApprovalByJobID gets approval by job ID
func (m *Manager) GetApprovalByJobID(ctx context.Context, jobID uuid.UUID) (*types.Approval, error) {
	return m.approvalRepo.GetByJobID(jobID)
}

// ExpireApprovals expires approvals that have passed their expiration time
func (m *Manager) ExpireApprovals(ctx context.Context) error {
	expired, err := m.approvalRepo.GetExpired()
	if err != nil {
		return fmt.Errorf("failed to get expired approvals: %w", err)
	}

	for _, approval := range expired {
		approval.Status = types.ApprovalStatusExpired
		if err := m.approvalRepo.Update(approval); err != nil {
			m.logger.WithError(err).WithField("approval_id", approval.ID).Error("Failed to expire approval")
			continue
		}

		// Update associated job
		job, err := m.jobRepo.GetByID(approval.JobID.String())
		if err != nil {
			m.logger.WithError(err).WithField("job_id", approval.JobID).Error("Failed to get job for expired approval")
			continue
		}

		job.Status = types.StatusCancelled
		if err := m.jobRepo.Update(job); err != nil {
			m.logger.WithError(err).WithField("job_id", job.ID).Error("Failed to update job status for expired approval")
		}

		m.logger.WithFields(logrus.Fields{
			"approval_id": approval.ID,
			"job_id":      approval.JobID,
		}).Info("Approval expired")
	}

	return nil
}

// isAuthorizedApprover checks if a user is authorized to approve
func (m *Manager) isAuthorizedApprover(userID uuid.UUID, requiredBy []uuid.UUID) bool {
	for _, approverID := range requiredBy {
		if approverID == userID {
			return true
		}
	}
	return false
}

// Request and response types
type CreateApprovalRequest struct {
	JobID      uuid.UUID   `json:"job_id"`
	RequiredBy []uuid.UUID `json:"required_by"`
	Message    string      `json:"message"`
	ExpiresAt  *time.Time  `json:"expires_at,omitempty"`
}

type ApprovalDecisionRequest struct {
	JobID  uuid.UUID `json:"job_id"`
	UserID uuid.UUID `json:"user_id"`
	Reason string    `json:"reason,omitempty"`
}
