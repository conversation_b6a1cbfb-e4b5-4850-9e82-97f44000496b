package logging

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// LogLevel represents the log level
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug"
	LogLevelInfo  LogLevel = "info"
	LogLevelWarn  LogLevel = "warn"
	LogLevelError LogLevel = "error"
)

// LogEntry represents a single log entry
type LogEntry struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Level       LogLevel               `json:"level"`
	Message     string                 `json:"message"`
	Source      string                 `json:"source"`      // pipeline, job, step
	SourceID    string                 `json:"source_id"`   // pipeline_id, job_id, step_id
	ExecutionID string                 `json:"execution_id"`
	JobID       string                 `json:"job_id,omitempty"`
	StepID      string                 `json:"step_id,omitempty"`
	Fields      map[string]interface{} `json:"fields,omitempty"`
	Raw         string                 `json:"raw,omitempty"` // Raw log output
}

// LogStream represents a log stream
type LogStream struct {
	ID          string    `json:"id"`
	ExecutionID string    `json:"execution_id"`
	JobID       string    `json:"job_id,omitempty"`
	StepID      string    `json:"step_id,omitempty"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time,omitempty"`
	Status      string    `json:"status"` // active, completed, failed
	EntryCount  int       `json:"entry_count"`
}

// StreamManager manages log streams and WebSocket connections
type StreamManager struct {
	streams     map[string]*LogStream
	connections map[string]map[*websocket.Conn]bool // stream_id -> connections
	entries     map[string][]*LogEntry              // stream_id -> entries
	mu          sync.RWMutex
	logger      *logrus.Logger
}

// NewStreamManager creates a new stream manager
func NewStreamManager(logger *logrus.Logger) *StreamManager {
	return &StreamManager{
		streams:     make(map[string]*LogStream),
		connections: make(map[string]map[*websocket.Conn]bool),
		entries:     make(map[string][]*LogEntry),
		logger:      logger,
	}
}

// CreateStream creates a new log stream
func (sm *StreamManager) CreateStream(executionID, jobID, stepID string) *LogStream {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	streamID := fmt.Sprintf("%s", executionID)
	if jobID != "" {
		streamID = fmt.Sprintf("%s-%s", executionID, jobID)
	}
	if stepID != "" {
		streamID = fmt.Sprintf("%s-%s-%s", executionID, jobID, stepID)
	}
	
	stream := &LogStream{
		ID:          streamID,
		ExecutionID: executionID,
		JobID:       jobID,
		StepID:      stepID,
		StartTime:   time.Now(),
		Status:      "active",
	}
	
	sm.streams[streamID] = stream
	sm.connections[streamID] = make(map[*websocket.Conn]bool)
	sm.entries[streamID] = make([]*LogEntry, 0)
	
	return stream
}

// GetStream gets a log stream by ID
func (sm *StreamManager) GetStream(streamID string) (*LogStream, bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	stream, exists := sm.streams[streamID]
	return stream, exists
}

// CloseStream closes a log stream
func (sm *StreamManager) CloseStream(streamID string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	if stream, exists := sm.streams[streamID]; exists {
		stream.EndTime = time.Now()
		stream.Status = "completed"
		
		// Close all WebSocket connections for this stream
		if connections, exists := sm.connections[streamID]; exists {
			for conn := range connections {
				conn.Close()
			}
			delete(sm.connections, streamID)
		}
	}
}

// AddConnection adds a WebSocket connection to a stream
func (sm *StreamManager) AddConnection(streamID string, conn *websocket.Conn) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	if _, exists := sm.streams[streamID]; !exists {
		return fmt.Errorf("stream %s not found", streamID)
	}
	
	if sm.connections[streamID] == nil {
		sm.connections[streamID] = make(map[*websocket.Conn]bool)
	}
	
	sm.connections[streamID][conn] = true
	
	// Send existing log entries to the new connection
	if entries, exists := sm.entries[streamID]; exists {
		for _, entry := range entries {
			if err := conn.WriteJSON(entry); err != nil {
				sm.logger.WithError(err).Error("Failed to send log entry to WebSocket connection")
				conn.Close()
				delete(sm.connections[streamID], conn)
				return err
			}
		}
	}
	
	return nil
}

// RemoveConnection removes a WebSocket connection from a stream
func (sm *StreamManager) RemoveConnection(streamID string, conn *websocket.Conn) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	if connections, exists := sm.connections[streamID]; exists {
		delete(connections, conn)
	}
}

// WriteLog writes a log entry to a stream
func (sm *StreamManager) WriteLog(streamID string, entry *LogEntry) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	// Store the log entry
	if sm.entries[streamID] == nil {
		sm.entries[streamID] = make([]*LogEntry, 0)
	}
	sm.entries[streamID] = append(sm.entries[streamID], entry)
	
	// Update stream entry count
	if stream, exists := sm.streams[streamID]; exists {
		stream.EntryCount++
	}
	
	// Broadcast to all connected WebSocket clients
	if connections, exists := sm.connections[streamID]; exists {
		for conn := range connections {
			if err := conn.WriteJSON(entry); err != nil {
				sm.logger.WithError(err).Error("Failed to send log entry to WebSocket connection")
				conn.Close()
				delete(connections, conn)
			}
		}
	}
}

// GetLogs gets log entries for a stream
func (sm *StreamManager) GetLogs(streamID string, limit, offset int) ([]*LogEntry, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	entries, exists := sm.entries[streamID]
	if !exists {
		return nil, fmt.Errorf("stream %s not found", streamID)
	}
	
	// Apply pagination
	start := offset
	if start > len(entries) {
		start = len(entries)
	}
	
	end := start + limit
	if end > len(entries) {
		end = len(entries)
	}
	
	return entries[start:end], nil
}

// ListStreams lists all streams
func (sm *StreamManager) ListStreams() []*LogStream {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	streams := make([]*LogStream, 0, len(sm.streams))
	for _, stream := range sm.streams {
		streams = append(streams, stream)
	}
	
	return streams
}

// Logger provides a structured logger for pipeline components
type Logger struct {
	streamManager *StreamManager
	streamID      string
	source        string
	sourceID      string
	executionID   string
	jobID         string
	stepID        string
	fields        map[string]interface{}
}

// NewLogger creates a new logger for a specific context
func NewLogger(streamManager *StreamManager, streamID, source, sourceID string) *Logger {
	return &Logger{
		streamManager: streamManager,
		streamID:      streamID,
		source:        source,
		sourceID:      sourceID,
		fields:        make(map[string]interface{}),
	}
}

// WithField adds a field to the logger context
func (l *Logger) WithField(key string, value interface{}) *Logger {
	newLogger := *l
	newLogger.fields = make(map[string]interface{})
	for k, v := range l.fields {
		newLogger.fields[k] = v
	}
	newLogger.fields[key] = value
	return &newLogger
}

// WithFields adds multiple fields to the logger context
func (l *Logger) WithFields(fields map[string]interface{}) *Logger {
	newLogger := *l
	newLogger.fields = make(map[string]interface{})
	for k, v := range l.fields {
		newLogger.fields[k] = v
	}
	for k, v := range fields {
		newLogger.fields[k] = v
	}
	return &newLogger
}

// WithExecution sets the execution context
func (l *Logger) WithExecution(executionID string) *Logger {
	newLogger := *l
	newLogger.executionID = executionID
	return &newLogger
}

// WithJob sets the job context
func (l *Logger) WithJob(jobID string) *Logger {
	newLogger := *l
	newLogger.jobID = jobID
	return &newLogger
}

// WithStep sets the step context
func (l *Logger) WithStep(stepID string) *Logger {
	newLogger := *l
	newLogger.stepID = stepID
	return &newLogger
}

// log writes a log entry
func (l *Logger) log(level LogLevel, message string) {
	entry := &LogEntry{
		ID:          fmt.Sprintf("%d", time.Now().UnixNano()),
		Timestamp:   time.Now(),
		Level:       level,
		Message:     message,
		Source:      l.source,
		SourceID:    l.sourceID,
		ExecutionID: l.executionID,
		JobID:       l.jobID,
		StepID:      l.stepID,
		Fields:      l.fields,
	}
	
	l.streamManager.WriteLog(l.streamID, entry)
}

// Debug logs a debug message
func (l *Logger) Debug(message string) {
	l.log(LogLevelDebug, message)
}

// Info logs an info message
func (l *Logger) Info(message string) {
	l.log(LogLevelInfo, message)
}

// Warn logs a warning message
func (l *Logger) Warn(message string) {
	l.log(LogLevelWarn, message)
}

// Error logs an error message
func (l *Logger) Error(message string) {
	l.log(LogLevelError, message)
}

// Debugf logs a formatted debug message
func (l *Logger) Debugf(format string, args ...interface{}) {
	l.log(LogLevelDebug, fmt.Sprintf(format, args...))
}

// Infof logs a formatted info message
func (l *Logger) Infof(format string, args ...interface{}) {
	l.log(LogLevelInfo, fmt.Sprintf(format, args...))
}

// Warnf logs a formatted warning message
func (l *Logger) Warnf(format string, args ...interface{}) {
	l.log(LogLevelWarn, fmt.Sprintf(format, args...))
}

// Errorf logs a formatted error message
func (l *Logger) Errorf(format string, args ...interface{}) {
	l.log(LogLevelError, fmt.Sprintf(format, args...))
}

// Raw logs raw output (e.g., from command execution)
func (l *Logger) Raw(output string) {
	entry := &LogEntry{
		ID:          fmt.Sprintf("%d", time.Now().UnixNano()),
		Timestamp:   time.Now(),
		Level:       LogLevelInfo,
		Message:     "",
		Source:      l.source,
		SourceID:    l.sourceID,
		ExecutionID: l.executionID,
		JobID:       l.jobID,
		StepID:      l.stepID,
		Fields:      l.fields,
		Raw:         output,
	}
	
	l.streamManager.WriteLog(l.streamID, entry)
}
