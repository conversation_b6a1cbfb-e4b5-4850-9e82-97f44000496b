package git

import (
	"context"
	"fmt"
	"time"
)

// Provider represents a Git provider (GitHub, GitLab, Bitbucket, etc.)
type Provider interface {
	// Authentication
	Authenticate(ctx context.Context, token string) error
	GetUser(ctx context.Context) (*User, error)
	
	// Repository operations
	GetRepository(ctx context.Context, owner, repo string) (*Repository, error)
	ListRepositories(ctx context.Context, opts *ListOptions) ([]*Repository, error)
	
	// Webhook operations
	CreateWebhook(ctx context.Context, owner, repo string, webhook *WebhookConfig) (*Webhook, error)
	DeleteWebhook(ctx context.Context, owner, repo string, webhookID string) error
	ListWebhooks(ctx context.Context, owner, repo string) ([]*Webhook, error)
	
	// Commit and branch operations
	GetCommit(ctx context.Context, owner, repo, sha string) (*Commit, error)
	ListBranches(ctx context.Context, owner, repo string) ([]*Branch, error)
	GetBranch(ctx context.Context, owner, repo, branch string) (*Branch, error)
	
	// Pull/Merge request operations
	GetPullRequest(ctx context.Context, owner, repo string, number int) (*PullRequest, error)
	ListPullRequests(ctx context.Context, owner, repo string, opts *ListOptions) ([]*PullRequest, error)
	UpdatePullRequestStatus(ctx context.Context, owner, repo string, number int, status *Status) error
	
	// File operations
	GetFile(ctx context.Context, owner, repo, path, ref string) (*File, error)
	GetFileContent(ctx context.Context, owner, repo, path, ref string) ([]byte, error)
	
	// Release operations
	CreateRelease(ctx context.Context, owner, repo string, release *Release) (*Release, error)
	GetRelease(ctx context.Context, owner, repo, tag string) (*Release, error)
	ListReleases(ctx context.Context, owner, repo string) ([]*Release, error)
}

// User represents a Git provider user
type User struct {
	ID       int64  `json:"id"`
	Login    string `json:"login"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	AvatarURL string `json:"avatar_url"`
}

// Repository represents a Git repository
type Repository struct {
	ID            int64     `json:"id"`
	Name          string    `json:"name"`
	FullName      string    `json:"full_name"`
	Owner         *User     `json:"owner"`
	Private       bool      `json:"private"`
	HTMLURL       string    `json:"html_url"`
	CloneURL      string    `json:"clone_url"`
	SSHUrl        string    `json:"ssh_url"`
	DefaultBranch string    `json:"default_branch"`
	Description   string    `json:"description"`
	Language      string    `json:"language"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// WebhookConfig represents webhook configuration
type WebhookConfig struct {
	URL         string            `json:"url"`
	ContentType string            `json:"content_type"`
	Secret      string            `json:"secret"`
	Events      []string          `json:"events"`
	Active      bool              `json:"active"`
	Config      map[string]string `json:"config"`
}

// Webhook represents a webhook
type Webhook struct {
	ID     int64          `json:"id"`
	URL    string         `json:"url"`
	Events []string       `json:"events"`
	Active bool           `json:"active"`
	Config *WebhookConfig `json:"config"`
}

// Commit represents a Git commit
type Commit struct {
	SHA       string    `json:"sha"`
	Message   string    `json:"message"`
	Author    *User     `json:"author"`
	Committer *User     `json:"committer"`
	Timestamp time.Time `json:"timestamp"`
	URL       string    `json:"url"`
	Added     []string  `json:"added"`
	Modified  []string  `json:"modified"`
	Removed   []string  `json:"removed"`
}

// Branch represents a Git branch
type Branch struct {
	Name      string  `json:"name"`
	SHA       string  `json:"sha"`
	Protected bool    `json:"protected"`
	Commit    *Commit `json:"commit"`
}

// PullRequest represents a pull/merge request
type PullRequest struct {
	ID          int64     `json:"id"`
	Number      int       `json:"number"`
	Title       string    `json:"title"`
	Body        string    `json:"body"`
	State       string    `json:"state"`
	Author      *User     `json:"author"`
	HeadBranch  string    `json:"head_branch"`
	BaseBranch  string    `json:"base_branch"`
	HeadSHA     string    `json:"head_sha"`
	BaseSHA     string    `json:"base_sha"`
	Mergeable   bool      `json:"mergeable"`
	Merged      bool      `json:"merged"`
	MergedAt    time.Time `json:"merged_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	HTMLURL     string    `json:"html_url"`
	DiffURL     string    `json:"diff_url"`
	PatchURL    string    `json:"patch_url"`
}

// Status represents a commit status
type Status struct {
	State       string `json:"state"`       // pending, success, error, failure
	TargetURL   string `json:"target_url"`
	Description string `json:"description"`
	Context     string `json:"context"`
}

// File represents a file in the repository
type File struct {
	Name        string `json:"name"`
	Path        string `json:"path"`
	SHA         string `json:"sha"`
	Size        int64  `json:"size"`
	URL         string `json:"url"`
	HTMLURL     string `json:"html_url"`
	GitURL      string `json:"git_url"`
	DownloadURL string `json:"download_url"`
	Type        string `json:"type"`
	Content     string `json:"content"`
	Encoding    string `json:"encoding"`
}

// Release represents a release
type Release struct {
	ID          int64     `json:"id"`
	TagName     string    `json:"tag_name"`
	Name        string    `json:"name"`
	Body        string    `json:"body"`
	Draft       bool      `json:"draft"`
	Prerelease  bool      `json:"prerelease"`
	CreatedAt   time.Time `json:"created_at"`
	PublishedAt time.Time `json:"published_at"`
	Author      *User     `json:"author"`
	Assets      []*Asset  `json:"assets"`
	TarballURL  string    `json:"tarball_url"`
	ZipballURL  string    `json:"zipball_url"`
}

// Asset represents a release asset
type Asset struct {
	ID                 int64     `json:"id"`
	Name               string    `json:"name"`
	Label              string    `json:"label"`
	ContentType        string    `json:"content_type"`
	Size               int64     `json:"size"`
	DownloadCount      int       `json:"download_count"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	BrowserDownloadURL string    `json:"browser_download_url"`
}

// ListOptions represents options for listing resources
type ListOptions struct {
	Page    int    `json:"page"`
	PerPage int    `json:"per_page"`
	Sort    string `json:"sort"`
	Order   string `json:"order"`
	State   string `json:"state"`
}

// WebhookEvent represents a webhook event
type WebhookEvent struct {
	Type        string                 `json:"type"`
	Action      string                 `json:"action"`
	Repository  *Repository            `json:"repository"`
	Sender      *User                  `json:"sender"`
	PullRequest *PullRequest           `json:"pull_request,omitempty"`
	Commit      *Commit                `json:"commit,omitempty"`
	Branch      *Branch                `json:"branch,omitempty"`
	Release     *Release               `json:"release,omitempty"`
	Ref         string                 `json:"ref,omitempty"`
	Before      string                 `json:"before,omitempty"`
	After       string                 `json:"after,omitempty"`
	Forced      bool                   `json:"forced,omitempty"`
	Compare     string                 `json:"compare,omitempty"`
	Commits     []*Commit              `json:"commits,omitempty"`
	HeadCommit  *Commit                `json:"head_commit,omitempty"`
	Payload     map[string]interface{} `json:"payload,omitempty"`
}

// ProviderType represents the type of Git provider
type ProviderType string

const (
	ProviderTypeGitHub    ProviderType = "github"
	ProviderTypeGitLab    ProviderType = "gitlab"
	ProviderTypeBitbucket ProviderType = "bitbucket"
	ProviderTypeGitea     ProviderType = "gitea"
)

// ProviderConfig represents provider configuration
type ProviderConfig struct {
	Type         ProviderType `json:"type"`
	BaseURL      string       `json:"base_url"`
	ClientID     string       `json:"client_id"`
	ClientSecret string       `json:"client_secret"`
	WebhookSecret string      `json:"webhook_secret"`
}

// Error types
var (
	ErrProviderNotFound    = fmt.Errorf("git provider not found")
	ErrInvalidCredentials  = fmt.Errorf("invalid credentials")
	ErrRepositoryNotFound  = fmt.Errorf("repository not found")
	ErrWebhookNotFound     = fmt.Errorf("webhook not found")
	ErrRateLimitExceeded   = fmt.Errorf("rate limit exceeded")
	ErrInsufficientPermissions = fmt.Errorf("insufficient permissions")
)
