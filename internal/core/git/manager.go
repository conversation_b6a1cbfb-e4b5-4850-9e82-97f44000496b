package git

import (
	"context"
	"fmt"
	"sync"
)

// Manager manages multiple Git providers
type Manager struct {
	providers map[ProviderType]Provider
	configs   map[ProviderType]*ProviderConfig
	mu        sync.RWMutex
}

// NewManager creates a new Git provider manager
func NewManager() *Manager {
	return &Manager{
		providers: make(map[ProviderType]Provider),
		configs:   make(map[ProviderType]*ProviderConfig),
	}
}

// RegisterProvider registers a Git provider
func (m *Manager) RegisterProvider(providerType ProviderType, config *ProviderConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	var provider Provider
	
	switch providerType {
	case ProviderTypeGitHub:
		provider = NewGitHubProvider(config)
	case ProviderTypeGitLab:
		// TODO: Implement GitLab provider
		return fmt.Errorf("GitLab provider not implemented yet")
	case ProviderTypeBitbucket:
		// TODO: Implement Bitbucket provider
		return fmt.Errorf("Bitbucket provider not implemented yet")
	case ProviderTypeGitea:
		// TODO: Implement Gitea provider
		return fmt.Errorf("Gitea provider not implemented yet")
	default:
		return fmt.Errorf("unsupported provider type: %s", providerType)
	}
	
	m.providers[providerType] = provider
	m.configs[providerType] = config
	
	return nil
}

// GetProvider gets a Git provider by type
func (m *Manager) GetProvider(providerType ProviderType) (Provider, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	provider, exists := m.providers[providerType]
	if !exists {
		return nil, fmt.Errorf("provider %s not registered", providerType)
	}
	
	return provider, nil
}

// GetProviderForRepository gets the appropriate provider for a repository URL
func (m *Manager) GetProviderForRepository(repoURL string) (Provider, ProviderType, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	// Determine provider type from repository URL
	providerType := m.detectProviderType(repoURL)
	if providerType == "" {
		return nil, "", fmt.Errorf("unable to detect provider type from URL: %s", repoURL)
	}
	
	provider, exists := m.providers[providerType]
	if !exists {
		return nil, "", fmt.Errorf("provider %s not registered", providerType)
	}
	
	return provider, providerType, nil
}

// AuthenticateProvider authenticates a provider with a token
func (m *Manager) AuthenticateProvider(ctx context.Context, providerType ProviderType, token string) error {
	provider, err := m.GetProvider(providerType)
	if err != nil {
		return err
	}
	
	return provider.Authenticate(ctx, token)
}

// ListProviders lists all registered providers
func (m *Manager) ListProviders() []ProviderType {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var providers []ProviderType
	for providerType := range m.providers {
		providers = append(providers, providerType)
	}
	
	return providers
}

// GetProviderConfig gets the configuration for a provider
func (m *Manager) GetProviderConfig(providerType ProviderType) (*ProviderConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	config, exists := m.configs[providerType]
	if !exists {
		return nil, fmt.Errorf("provider %s not registered", providerType)
	}
	
	return config, nil
}

// detectProviderType detects the provider type from a repository URL
func (m *Manager) detectProviderType(repoURL string) ProviderType {
	switch {
	case contains(repoURL, "github.com"):
		return ProviderTypeGitHub
	case contains(repoURL, "gitlab.com"):
		return ProviderTypeGitLab
	case contains(repoURL, "bitbucket.org"):
		return ProviderTypeBitbucket
	case contains(repoURL, "gitea"):
		return ProviderTypeGitea
	default:
		// Check if it's a GitHub Enterprise instance
		for providerType, config := range m.configs {
			if providerType == ProviderTypeGitHub && config.BaseURL != "" && contains(repoURL, config.BaseURL) {
				return ProviderTypeGitHub
			}
		}
		return ""
	}
}

// contains checks if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || (len(s) > len(substr) && 
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || 
		 indexOf(s, substr) >= 0)))
}

// indexOf returns the index of substr in s, or -1 if not found
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// Repository operations that work across providers

// CloneRepository clones a repository using the appropriate provider
func (m *Manager) CloneRepository(ctx context.Context, repoURL, token, destination string) error {
	provider, providerType, err := m.GetProviderForRepository(repoURL)
	if err != nil {
		return err
	}
	
	// Authenticate if token is provided
	if token != "" {
		if err := provider.Authenticate(ctx, token); err != nil {
			return fmt.Errorf("authentication failed for %s: %w", providerType, err)
		}
	}
	
	// TODO: Implement actual git clone operation
	// This would typically use git commands or a git library
	return fmt.Errorf("clone operation not implemented yet")
}

// GetRepositoryInfo gets repository information using the appropriate provider
func (m *Manager) GetRepositoryInfo(ctx context.Context, repoURL, token string) (*Repository, error) {
	provider, providerType, err := m.GetProviderForRepository(repoURL)
	if err != nil {
		return nil, err
	}
	
	// Authenticate if token is provided
	if token != "" {
		if err := provider.Authenticate(ctx, token); err != nil {
			return nil, fmt.Errorf("authentication failed for %s: %w", providerType, err)
		}
	}
	
	// Parse owner and repo from URL
	owner, repo, err := m.parseRepositoryURL(repoURL)
	if err != nil {
		return nil, err
	}
	
	return provider.GetRepository(ctx, owner, repo)
}

// CreateWebhookForRepository creates a webhook for a repository
func (m *Manager) CreateWebhookForRepository(ctx context.Context, repoURL, token string, webhook *WebhookConfig) (*Webhook, error) {
	provider, providerType, err := m.GetProviderForRepository(repoURL)
	if err != nil {
		return nil, err
	}
	
	// Authenticate
	if err := provider.Authenticate(ctx, token); err != nil {
		return nil, fmt.Errorf("authentication failed for %s: %w", providerType, err)
	}
	
	// Parse owner and repo from URL
	owner, repo, err := m.parseRepositoryURL(repoURL)
	if err != nil {
		return nil, err
	}
	
	return provider.CreateWebhook(ctx, owner, repo, webhook)
}

// parseRepositoryURL parses a repository URL to extract owner and repository name
func (m *Manager) parseRepositoryURL(repoURL string) (owner, repo string, err error) {
	// This is a simplified parser - in production, you'd want more robust URL parsing
	// Examples:
	// https://github.com/owner/repo
	// https://github.com/owner/repo.git
	// **************:owner/repo.git
	
	// Remove common prefixes and suffixes
	url := repoURL
	if indexOf(url, "://") >= 0 {
		// HTTP(S) URL
		parts := splitString(url, "/")
		if len(parts) >= 2 {
			owner = parts[len(parts)-2]
			repo = parts[len(parts)-1]
		}
	} else if indexOf(url, "@") >= 0 {
		// SSH URL
		parts := splitString(url, ":")
		if len(parts) >= 2 {
			pathParts := splitString(parts[1], "/")
			if len(pathParts) >= 2 {
				owner = pathParts[0]
				repo = pathParts[1]
			}
		}
	}
	
	// Remove .git suffix if present
	if len(repo) > 4 && repo[len(repo)-4:] == ".git" {
		repo = repo[:len(repo)-4]
	}
	
	if owner == "" || repo == "" {
		return "", "", fmt.Errorf("unable to parse repository URL: %s", repoURL)
	}
	
	return owner, repo, nil
}

// splitString splits a string by a delimiter
func splitString(s, delimiter string) []string {
	if s == "" {
		return []string{}
	}
	
	var parts []string
	start := 0
	
	for i := 0; i <= len(s)-len(delimiter); i++ {
		if s[i:i+len(delimiter)] == delimiter {
			if i > start {
				parts = append(parts, s[start:i])
			}
			start = i + len(delimiter)
			i += len(delimiter) - 1
		}
	}
	
	if start < len(s) {
		parts = append(parts, s[start:])
	}
	
	return parts
}

// WebhookHandler handles incoming webhooks
type WebhookHandler struct {
	manager *Manager
}

// NewWebhookHandler creates a new webhook handler
func NewWebhookHandler(manager *Manager) *WebhookHandler {
	return &WebhookHandler{
		manager: manager,
	}
}

// HandleWebhook handles an incoming webhook
func (h *WebhookHandler) HandleWebhook(ctx context.Context, providerType ProviderType, payload []byte, signature string) (*WebhookEvent, error) {
	provider, err := h.manager.GetProvider(providerType)
	if err != nil {
		return nil, err
	}
	
	// For now, we'll assume GitHub provider has a ParseWebhookEvent method
	// In a real implementation, we'd need to add this to the Provider interface
	if githubProvider, ok := provider.(*GitHubProvider); ok {
		return githubProvider.ParseWebhookEvent(payload, signature)
	}
	
	return nil, fmt.Errorf("webhook parsing not implemented for provider: %s", providerType)
}
