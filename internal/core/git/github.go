package git

import (
	"context"
	"fmt"
	"strconv"

	"github.com/google/go-github/v57/github"
	"golang.org/x/oauth2"
)

// GitHubProvider implements the Provider interface for GitHub
type GitHubProvider struct {
	client *github.Client
	config *ProviderConfig
}

// NewGitHubProvider creates a new GitHub provider
func NewGitHubProvider(config *ProviderConfig) *GitHubProvider {
	var client *github.Client

	if config.BaseURL != "" && config.BaseURL != "https://api.github.com" {
		// GitHub Enterprise
		client, _ = github.NewEnterpriseClient(config.BaseURL, config.BaseURL, nil)
	} else {
		// GitHub.com
		client = github.NewClient(nil)
	}

	return &GitHubProvider{
		client: client,
		config: config,
	}
}

// Authenticate authenticates with GitHub using a token
func (g *GitHubProvider) Authenticate(ctx context.Context, token string) error {
	ts := oauth2.StaticTokenSource(&oauth2.Token{AccessToken: token})
	tc := oauth2.NewClient(ctx, ts)
	g.client = github.NewClient(tc)

	// Test authentication
	_, _, err := g.client.Users.Get(ctx, "")
	if err != nil {
		return fmt.Errorf("authentication failed: %w", err)
	}

	return nil
}

// GetUser gets the authenticated user
func (g *GitHubProvider) GetUser(ctx context.Context) (*User, error) {
	user, _, err := g.client.Users.Get(ctx, "")
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &User{
		ID:        user.GetID(),
		Login:     user.GetLogin(),
		Name:      user.GetName(),
		Email:     user.GetEmail(),
		AvatarURL: user.GetAvatarURL(),
	}, nil
}

// GetRepository gets a repository
func (g *GitHubProvider) GetRepository(ctx context.Context, owner, repo string) (*Repository, error) {
	repository, _, err := g.client.Repositories.Get(ctx, owner, repo)
	if err != nil {
		return nil, fmt.Errorf("failed to get repository: %w", err)
	}

	return g.convertRepository(repository), nil
}

// ListRepositories lists repositories
func (g *GitHubProvider) ListRepositories(ctx context.Context, opts *ListOptions) ([]*Repository, error) {
	listOpts := &github.RepositoryListOptions{
		Sort:      opts.Sort,
		Direction: opts.Order,
		ListOptions: github.ListOptions{
			Page:    opts.Page,
			PerPage: opts.PerPage,
		},
	}

	repos, _, err := g.client.Repositories.List(ctx, "", listOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to list repositories: %w", err)
	}

	var repositories []*Repository
	for _, repo := range repos {
		repositories = append(repositories, g.convertRepository(repo))
	}

	return repositories, nil
}

// CreateWebhook creates a webhook
func (g *GitHubProvider) CreateWebhook(ctx context.Context, owner, repo string, webhook *WebhookConfig) (*Webhook, error) {
	hook := &github.Hook{
		Name:   github.String("web"),
		Active: github.Bool(webhook.Active),
		Events: webhook.Events,
		Config: map[string]interface{}{
			"url":          webhook.URL,
			"content_type": webhook.ContentType,
			"secret":       webhook.Secret,
		},
	}

	createdHook, _, err := g.client.Repositories.CreateHook(ctx, owner, repo, hook)
	if err != nil {
		return nil, fmt.Errorf("failed to create webhook: %w", err)
	}

	return g.convertWebhook(createdHook), nil
}

// DeleteWebhook deletes a webhook
func (g *GitHubProvider) DeleteWebhook(ctx context.Context, owner, repo string, webhookID string) error {
	id, err := strconv.ParseInt(webhookID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid webhook ID: %w", err)
	}

	_, err = g.client.Repositories.DeleteHook(ctx, owner, repo, id)
	if err != nil {
		return fmt.Errorf("failed to delete webhook: %w", err)
	}

	return nil
}

// ListWebhooks lists webhooks
func (g *GitHubProvider) ListWebhooks(ctx context.Context, owner, repo string) ([]*Webhook, error) {
	hooks, _, err := g.client.Repositories.ListHooks(ctx, owner, repo, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to list webhooks: %w", err)
	}

	var webhooks []*Webhook
	for _, hook := range hooks {
		webhooks = append(webhooks, g.convertWebhook(hook))
	}

	return webhooks, nil
}

// GetCommit gets a commit
func (g *GitHubProvider) GetCommit(ctx context.Context, owner, repo, sha string) (*Commit, error) {
	commit, _, err := g.client.Repositories.GetCommit(ctx, owner, repo, sha, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get commit: %w", err)
	}

	return g.convertCommit(commit), nil
}

// ListBranches lists branches
func (g *GitHubProvider) ListBranches(ctx context.Context, owner, repo string) ([]*Branch, error) {
	branches, _, err := g.client.Repositories.ListBranches(ctx, owner, repo, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to list branches: %w", err)
	}

	var result []*Branch
	for _, branch := range branches {
		result = append(result, g.convertBranch(branch))
	}

	return result, nil
}

// GetBranch gets a branch
func (g *GitHubProvider) GetBranch(ctx context.Context, owner, repo, branch string) (*Branch, error) {
	b, _, err := g.client.Repositories.GetBranch(ctx, owner, repo, branch, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}

	return g.convertBranch(b), nil
}

// GetPullRequest gets a pull request
func (g *GitHubProvider) GetPullRequest(ctx context.Context, owner, repo string, number int) (*PullRequest, error) {
	pr, _, err := g.client.PullRequests.Get(ctx, owner, repo, number)
	if err != nil {
		return nil, fmt.Errorf("failed to get pull request: %w", err)
	}

	return g.convertPullRequest(pr), nil
}

// ListPullRequests lists pull requests
func (g *GitHubProvider) ListPullRequests(ctx context.Context, owner, repo string, opts *ListOptions) ([]*PullRequest, error) {
	listOpts := &github.PullRequestListOptions{
		State:     opts.State,
		Sort:      opts.Sort,
		Direction: opts.Order,
		ListOptions: github.ListOptions{
			Page:    opts.Page,
			PerPage: opts.PerPage,
		},
	}

	prs, _, err := g.client.PullRequests.List(ctx, owner, repo, listOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to list pull requests: %w", err)
	}

	var pullRequests []*PullRequest
	for _, pr := range prs {
		pullRequests = append(pullRequests, g.convertPullRequest(pr))
	}

	return pullRequests, nil
}

// UpdatePullRequestStatus updates pull request status
func (g *GitHubProvider) UpdatePullRequestStatus(ctx context.Context, owner, repo string, number int, status *Status) error {
	pr, _, err := g.client.PullRequests.Get(ctx, owner, repo, number)
	if err != nil {
		return fmt.Errorf("failed to get pull request: %w", err)
	}

	repoStatus := &github.RepoStatus{
		State:       github.String(status.State),
		TargetURL:   github.String(status.TargetURL),
		Description: github.String(status.Description),
		Context:     github.String(status.Context),
	}

	_, _, err = g.client.Repositories.CreateStatus(ctx, owner, repo, pr.Head.GetSHA(), repoStatus)
	if err != nil {
		return fmt.Errorf("failed to update pull request status: %w", err)
	}

	return nil
}

// GetFile gets a file
func (g *GitHubProvider) GetFile(ctx context.Context, owner, repo, path, ref string) (*File, error) {
	opts := &github.RepositoryContentGetOptions{Ref: ref}
	file, _, _, err := g.client.Repositories.GetContents(ctx, owner, repo, path, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	return g.convertFile(file), nil
}

// GetFileContent gets file content
func (g *GitHubProvider) GetFileContent(ctx context.Context, owner, repo, path, ref string) ([]byte, error) {
	opts := &github.RepositoryContentGetOptions{Ref: ref}
	file, _, _, err := g.client.Repositories.GetContents(ctx, owner, repo, path, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	content, err := file.GetContent()
	if err != nil {
		return nil, fmt.Errorf("failed to decode file content: %w", err)
	}

	return []byte(content), nil
}

// CreateRelease creates a release
func (g *GitHubProvider) CreateRelease(ctx context.Context, owner, repo string, release *Release) (*Release, error) {
	githubRelease := &github.RepositoryRelease{
		TagName:    github.String(release.TagName),
		Name:       github.String(release.Name),
		Body:       github.String(release.Body),
		Draft:      github.Bool(release.Draft),
		Prerelease: github.Bool(release.Prerelease),
	}

	createdRelease, _, err := g.client.Repositories.CreateRelease(ctx, owner, repo, githubRelease)
	if err != nil {
		return nil, fmt.Errorf("failed to create release: %w", err)
	}

	return g.convertRelease(createdRelease), nil
}

// GetRelease gets a release
func (g *GitHubProvider) GetRelease(ctx context.Context, owner, repo, tag string) (*Release, error) {
	release, _, err := g.client.Repositories.GetReleaseByTag(ctx, owner, repo, tag)
	if err != nil {
		return nil, fmt.Errorf("failed to get release: %w", err)
	}

	return g.convertRelease(release), nil
}

// ListReleases lists releases
func (g *GitHubProvider) ListReleases(ctx context.Context, owner, repo string) ([]*Release, error) {
	releases, _, err := g.client.Repositories.ListReleases(ctx, owner, repo, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to list releases: %w", err)
	}

	var result []*Release
	for _, release := range releases {
		result = append(result, g.convertRelease(release))
	}

	return result, nil
}

// Helper methods for converting GitHub types to our types

func (g *GitHubProvider) convertRepository(repo *github.Repository) *Repository {
	var owner *User
	if repo.Owner != nil {
		owner = &User{
			ID:        repo.Owner.GetID(),
			Login:     repo.Owner.GetLogin(),
			Name:      repo.Owner.GetName(),
			Email:     repo.Owner.GetEmail(),
			AvatarURL: repo.Owner.GetAvatarURL(),
		}
	}

	return &Repository{
		ID:            repo.GetID(),
		Name:          repo.GetName(),
		FullName:      repo.GetFullName(),
		Owner:         owner,
		Private:       repo.GetPrivate(),
		HTMLURL:       repo.GetHTMLURL(),
		CloneURL:      repo.GetCloneURL(),
		SSHUrl:        repo.GetSSHURL(),
		DefaultBranch: repo.GetDefaultBranch(),
		Description:   repo.GetDescription(),
		Language:      repo.GetLanguage(),
		CreatedAt:     repo.GetCreatedAt().Time,
		UpdatedAt:     repo.GetUpdatedAt().Time,
	}
}

func (g *GitHubProvider) convertWebhook(hook *github.Hook) *Webhook {
	config := &WebhookConfig{
		Active: hook.GetActive(),
		Events: hook.Events,
	}

	if hook.Config != nil {
		if url, ok := hook.Config["url"].(string); ok {
			config.URL = url
		}
		if contentType, ok := hook.Config["content_type"].(string); ok {
			config.ContentType = contentType
		}
		if secret, ok := hook.Config["secret"].(string); ok {
			config.Secret = secret
		}
	}

	return &Webhook{
		ID:     hook.GetID(),
		URL:    config.URL,
		Events: hook.Events,
		Active: hook.GetActive(),
		Config: config,
	}
}

func (g *GitHubProvider) convertCommit(commit *github.RepositoryCommit) *Commit {
	var author, committer *User

	if commit.Author != nil {
		author = &User{
			ID:        commit.Author.GetID(),
			Login:     commit.Author.GetLogin(),
			Name:      commit.Author.GetName(),
			Email:     commit.Author.GetEmail(),
			AvatarURL: commit.Author.GetAvatarURL(),
		}
	}

	if commit.Committer != nil {
		committer = &User{
			ID:        commit.Committer.GetID(),
			Login:     commit.Committer.GetLogin(),
			Name:      commit.Committer.GetName(),
			Email:     commit.Committer.GetEmail(),
			AvatarURL: commit.Committer.GetAvatarURL(),
		}
	}

	var added, modified, removed []string
	if commit.Files != nil {
		for _, file := range commit.Files {
			switch file.GetStatus() {
			case "added":
				added = append(added, file.GetFilename())
			case "modified":
				modified = append(modified, file.GetFilename())
			case "removed":
				removed = append(removed, file.GetFilename())
			}
		}
	}

	return &Commit{
		SHA:       commit.GetSHA(),
		Message:   commit.Commit.GetMessage(),
		Author:    author,
		Committer: committer,
		Timestamp: commit.Commit.Author.GetDate().Time,
		URL:       commit.GetHTMLURL(),
		Added:     added,
		Modified:  modified,
		Removed:   removed,
	}
}

func (g *GitHubProvider) convertBranch(branch *github.Branch) *Branch {
	var commit *Commit
	if branch.Commit != nil {
		commit = &Commit{
			SHA:     branch.Commit.GetSHA(),
			Message: branch.Commit.Commit.GetMessage(),
			URL:     branch.Commit.GetHTMLURL(),
		}

		if branch.Commit.Commit.Author != nil {
			commit.Timestamp = branch.Commit.Commit.Author.GetDate().Time
		}
	}

	return &Branch{
		Name:      branch.GetName(),
		SHA:       branch.Commit.GetSHA(),
		Protected: branch.GetProtected(),
		Commit:    commit,
	}
}

func (g *GitHubProvider) convertPullRequest(pr *github.PullRequest) *PullRequest {
	var author *User
	if pr.User != nil {
		author = &User{
			ID:        pr.User.GetID(),
			Login:     pr.User.GetLogin(),
			Name:      pr.User.GetName(),
			Email:     pr.User.GetEmail(),
			AvatarURL: pr.User.GetAvatarURL(),
		}
	}

	return &PullRequest{
		ID:         pr.GetID(),
		Number:     pr.GetNumber(),
		Title:      pr.GetTitle(),
		Body:       pr.GetBody(),
		State:      pr.GetState(),
		Author:     author,
		HeadBranch: pr.Head.GetRef(),
		BaseBranch: pr.Base.GetRef(),
		HeadSHA:    pr.Head.GetSHA(),
		BaseSHA:    pr.Base.GetSHA(),
		Mergeable:  pr.GetMergeable(),
		Merged:     pr.GetMerged(),
		MergedAt:   pr.GetMergedAt().Time,
		CreatedAt:  pr.GetCreatedAt().Time,
		UpdatedAt:  pr.GetUpdatedAt().Time,
		HTMLURL:    pr.GetHTMLURL(),
		DiffURL:    pr.GetDiffURL(),
		PatchURL:   pr.GetPatchURL(),
	}
}

func (g *GitHubProvider) convertFile(file *github.RepositoryContent) *File {
	return &File{
		Name:        file.GetName(),
		Path:        file.GetPath(),
		SHA:         file.GetSHA(),
		Size:        int64(file.GetSize()),
		URL:         file.GetURL(),
		HTMLURL:     file.GetHTMLURL(),
		GitURL:      file.GetGitURL(),
		DownloadURL: file.GetDownloadURL(),
		Type:        file.GetType(),
		Content:     file.GetContent(),
		Encoding:    file.GetEncoding(),
	}
}

func (g *GitHubProvider) convertRelease(release *github.RepositoryRelease) *Release {
	var author *User
	if release.Author != nil {
		author = &User{
			ID:        release.Author.GetID(),
			Login:     release.Author.GetLogin(),
			Name:      release.Author.GetName(),
			Email:     release.Author.GetEmail(),
			AvatarURL: release.Author.GetAvatarURL(),
		}
	}

	var assets []*Asset
	for _, asset := range release.Assets {
		assets = append(assets, &Asset{
			ID:                 asset.GetID(),
			Name:               asset.GetName(),
			Label:              asset.GetLabel(),
			ContentType:        asset.GetContentType(),
			Size:               int64(asset.GetSize()),
			DownloadCount:      asset.GetDownloadCount(),
			CreatedAt:          asset.GetCreatedAt().Time,
			UpdatedAt:          asset.GetUpdatedAt().Time,
			BrowserDownloadURL: asset.GetBrowserDownloadURL(),
		})
	}

	return &Release{
		ID:          release.GetID(),
		TagName:     release.GetTagName(),
		Name:        release.GetName(),
		Body:        release.GetBody(),
		Draft:       release.GetDraft(),
		Prerelease:  release.GetPrerelease(),
		CreatedAt:   release.GetCreatedAt().Time,
		PublishedAt: release.GetPublishedAt().Time,
		Author:      author,
		Assets:      assets,
		TarballURL:  release.GetTarballURL(),
		ZipballURL:  release.GetZipballURL(),
	}
}
