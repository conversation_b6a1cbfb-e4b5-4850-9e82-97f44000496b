package security

import (
	"context"
	"fmt"
	"time"

	"github.com/chainops/chainops/pkg/errors"
	"github.com/chainops/chainops/pkg/logger"
	"github.com/google/uuid"
)

// ScanType represents different types of security scans
type ScanType string

const (
	ScanTypeSAST       ScanType = "sast"       // Static Application Security Testing
	ScanTypeDAT        ScanType = "dast"       // Dynamic Application Security Testing
	ScanTypeContainer  ScanType = "container"  // Container Security Scanning
	ScanTypeDependency ScanType = "dependency" // Dependency Vulnerability Scanning
	ScanTypeSecrets    ScanType = "secrets"    // Secret Scanning
	ScanTypeLicense    ScanType = "license"    // License Compliance Scanning
)

// ScanStatus represents the status of a security scan
type ScanStatus string

const (
	ScanStatusPending   ScanStatus = "pending"
	ScanStatusRunning   ScanStatus = "running"
	ScanStatusCompleted ScanStatus = "completed"
	ScanStatusFailed    ScanStatus = "failed"
	ScanStatusCancelled ScanStatus = "cancelled"
)

// VulnerabilitySeverity represents the severity of a vulnerability
type VulnerabilitySeverity string

const (
	SeverityCritical VulnerabilitySeverity = "critical"
	SeverityHigh     VulnerabilitySeverity = "high"
	SeverityMedium   VulnerabilitySeverity = "medium"
	SeverityLow      VulnerabilitySeverity = "low"
	SeverityInfo     VulnerabilitySeverity = "info"
)

// SecurityScan represents a security scan
type SecurityScan struct {
	ID          uuid.UUID     `json:"id"`
	PipelineID  uuid.UUID     `json:"pipeline_id"`
	ExecutionID uuid.UUID     `json:"execution_id"`
	Type        ScanType      `json:"type"`
	Status      ScanStatus    `json:"status"`
	Config      ScanConfig    `json:"config"`
	Results     *ScanResults  `json:"results,omitempty"`
	StartedAt   time.Time     `json:"started_at"`
	CompletedAt *time.Time    `json:"completed_at,omitempty"`
	Duration    time.Duration `json:"duration"`
	Error       string        `json:"error,omitempty"`
}

// ScanConfig holds configuration for security scans
type ScanConfig struct {
	Target         string                `json:"target"`
	Rules          []string              `json:"rules,omitempty"`
	Exclusions     []string              `json:"exclusions,omitempty"`
	Severity       []string              `json:"severity,omitempty"`
	FailOnSeverity VulnerabilitySeverity `json:"fail_on_severity,omitempty"`
	Timeout        time.Duration         `json:"timeout"`
	Options        map[string]string     `json:"options,omitempty"`
}

// ScanResults holds the results of a security scan
type ScanResults struct {
	Summary         ScanSummary     `json:"summary"`
	Vulnerabilities []Vulnerability `json:"vulnerabilities"`
	Licenses        []License       `json:"licenses,omitempty"`
	Secrets         []Secret        `json:"secrets,omitempty"`
	Report          string          `json:"report,omitempty"`
}

// ScanSummary provides a summary of scan results
type ScanSummary struct {
	TotalIssues   int                           `json:"total_issues"`
	BySeverity    map[VulnerabilitySeverity]int `json:"by_severity"`
	ByType        map[string]int                `json:"by_type"`
	Passed        bool                          `json:"passed"`
	FailureReason string                        `json:"failure_reason,omitempty"`
}

// Vulnerability represents a security vulnerability
type Vulnerability struct {
	ID          string                `json:"id"`
	Title       string                `json:"title"`
	Description string                `json:"description"`
	Severity    VulnerabilitySeverity `json:"severity"`
	CVSS        float64               `json:"cvss,omitempty"`
	CVE         string                `json:"cve,omitempty"`
	CWE         string                `json:"cwe,omitempty"`
	Location    VulnerabilityLocation `json:"location"`
	References  []string              `json:"references,omitempty"`
	Solution    string                `json:"solution,omitempty"`
}

// VulnerabilityLocation represents the location of a vulnerability
type VulnerabilityLocation struct {
	File     string `json:"file"`
	Line     int    `json:"line,omitempty"`
	Column   int    `json:"column,omitempty"`
	Function string `json:"function,omitempty"`
	Package  string `json:"package,omitempty"`
	Version  string `json:"version,omitempty"`
}

// License represents a software license
type License struct {
	Name     string   `json:"name"`
	SPDXID   string   `json:"spdx_id,omitempty"`
	URL      string   `json:"url,omitempty"`
	Packages []string `json:"packages"`
	Approved bool     `json:"approved"`
	Risk     string   `json:"risk,omitempty"`
}

// Secret represents a detected secret
type Secret struct {
	Type        string `json:"type"`
	Description string `json:"description"`
	File        string `json:"file"`
	Line        int    `json:"line"`
	Value       string `json:"value,omitempty"` // Redacted
	Confidence  string `json:"confidence"`
}

// SecurityScanner interface for security scanning implementations
type SecurityScanner interface {
	Scan(ctx context.Context, config ScanConfig) (*ScanResults, error)
	GetScanTypes() []ScanType
	ValidateConfig(config ScanConfig) error
}

// SecurityManager manages security scans
type SecurityManager struct {
	scanners map[ScanType]SecurityScanner
	scanRepo SecurityScanRepository
	logger   *logger.Logger
}

// SecurityScanRepository interface for scan persistence
type SecurityScanRepository interface {
	CreateScan(scan *SecurityScan) error
	GetScan(id uuid.UUID) (*SecurityScan, error)
	UpdateScan(scan *SecurityScan) error
	ListScans(pipelineID uuid.UUID) ([]*SecurityScan, error)
	GetScansByExecution(executionID uuid.UUID) ([]*SecurityScan, error)
}

// NewSecurityManager creates a new security manager
func NewSecurityManager(scanRepo SecurityScanRepository, logger *logger.Logger) *SecurityManager {
	return &SecurityManager{
		scanners: make(map[ScanType]SecurityScanner),
		scanRepo: scanRepo,
		logger:   logger,
	}
}

// RegisterScanner registers a security scanner
func (m *SecurityManager) RegisterScanner(scanType ScanType, scanner SecurityScanner) {
	m.scanners[scanType] = scanner
}

// StartScan starts a security scan
func (m *SecurityManager) StartScan(ctx context.Context, req StartScanRequest) (*SecurityScan, error) {
	scanner, exists := m.scanners[req.Type]
	if !exists {
		return nil, errors.Newf(errors.ErrCodeValidation, "unsupported scan type: %s", req.Type)
	}

	// Validate configuration
	if err := scanner.ValidateConfig(req.Config); err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeValidation, "invalid scan configuration")
	}

	// Create scan record
	scan := &SecurityScan{
		ID:          uuid.New(),
		PipelineID:  req.PipelineID,
		ExecutionID: req.ExecutionID,
		Type:        req.Type,
		Status:      ScanStatusPending,
		Config:      req.Config,
		StartedAt:   time.Now(),
	}

	// Save scan
	if err := m.scanRepo.CreateScan(scan); err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInternal, "failed to create scan")
	}

	// Start scan asynchronously
	go m.executeScan(context.Background(), scan, scanner)

	return scan, nil
}

// executeScan executes a security scan
func (m *SecurityManager) executeScan(ctx context.Context, scan *SecurityScan, scanner SecurityScanner) {
	logger := m.logger.WithContext(ctx).WithFields(map[string]interface{}{
		"scan_id":      scan.ID,
		"scan_type":    scan.Type,
		"pipeline_id":  scan.PipelineID,
		"execution_id": scan.ExecutionID,
	})

	logger.Info("Starting security scan")

	// Update status to running
	scan.Status = ScanStatusRunning
	scan.StartedAt = time.Now()
	m.scanRepo.UpdateScan(scan)

	// Execute scan with timeout
	scanCtx, cancel := context.WithTimeout(ctx, scan.Config.Timeout)
	defer cancel()

	results, err := scanner.Scan(scanCtx, scan.Config)
	now := time.Now()
	scan.CompletedAt = &now
	scan.Duration = now.Sub(scan.StartedAt)

	if err != nil {
		logger.WithError(err).Error("Security scan failed")
		scan.Status = ScanStatusFailed
		scan.Error = err.Error()
	} else {
		logger.Info("Security scan completed")
		scan.Status = ScanStatusCompleted
		scan.Results = results
	}

	// Update scan record
	m.scanRepo.UpdateScan(scan)

	// Log scan results
	if results != nil {
		logger.WithFields(map[string]interface{}{
			"total_issues":    results.Summary.TotalIssues,
			"critical_issues": results.Summary.BySeverity[SeverityCritical],
			"high_issues":     results.Summary.BySeverity[SeverityHigh],
			"scan_passed":     results.Summary.Passed,
		}).Info("Security scan results")
	}
}

// GetScan retrieves a security scan
func (m *SecurityManager) GetScan(ctx context.Context, id uuid.UUID) (*SecurityScan, error) {
	scan, err := m.scanRepo.GetScan(id)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeNotFound, "scan not found")
	}
	return scan, nil
}

// ListScans lists security scans for a pipeline
func (m *SecurityManager) ListScans(ctx context.Context, pipelineID uuid.UUID) ([]*SecurityScan, error) {
	scans, err := m.scanRepo.ListScans(pipelineID)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInternal, "failed to list scans")
	}
	return scans, nil
}

// GetExecutionScans gets all scans for an execution
func (m *SecurityManager) GetExecutionScans(ctx context.Context, executionID uuid.UUID) ([]*SecurityScan, error) {
	scans, err := m.scanRepo.GetScansByExecution(executionID)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInternal, "failed to get execution scans")
	}
	return scans, nil
}

// StartScanRequest represents a request to start a security scan
type StartScanRequest struct {
	PipelineID  uuid.UUID  `json:"pipeline_id"`
	ExecutionID uuid.UUID  `json:"execution_id"`
	Type        ScanType   `json:"type"`
	Config      ScanConfig `json:"config"`
}

// TrivyScanner implements container security scanning using Trivy
type TrivyScanner struct {
	logger *logger.Logger
}

// NewTrivyScanner creates a new Trivy scanner
func NewTrivyScanner(logger *logger.Logger) *TrivyScanner {
	return &TrivyScanner{logger: logger}
}

// Scan performs a container security scan using Trivy
func (s *TrivyScanner) Scan(ctx context.Context, config ScanConfig) (*ScanResults, error) {
	// Implementation would call Trivy CLI or API
	// For now, return mock results
	results := &ScanResults{
		Summary: ScanSummary{
			TotalIssues: 5,
			BySeverity: map[VulnerabilitySeverity]int{
				SeverityCritical: 1,
				SeverityHigh:     2,
				SeverityMedium:   2,
				SeverityLow:      0,
			},
			ByType: map[string]int{
				"vulnerability": 5,
			},
			Passed:        false,
			FailureReason: "Critical vulnerabilities found",
		},
		Vulnerabilities: []Vulnerability{
			{
				ID:          "CVE-2023-1234",
				Title:       "Critical vulnerability in base image",
				Description: "A critical vulnerability was found in the base image",
				Severity:    SeverityCritical,
				CVSS:        9.8,
				CVE:         "CVE-2023-1234",
				Location: VulnerabilityLocation{
					Package: "openssl",
					Version: "1.1.1",
				},
				Solution: "Update to openssl 1.1.1n or later",
			},
		},
	}

	// Simulate scan time
	time.Sleep(2 * time.Second)

	return results, nil
}

// GetScanTypes returns supported scan types
func (s *TrivyScanner) GetScanTypes() []ScanType {
	return []ScanType{ScanTypeContainer}
}

// ValidateConfig validates scan configuration
func (s *TrivyScanner) ValidateConfig(config ScanConfig) error {
	if config.Target == "" {
		return fmt.Errorf("target is required")
	}
	return nil
}
