package storage

import (
	"fmt"
	"time"

	"github.com/chainops/chainops/internal/config"
	"github.com/chainops/chainops/pkg/types"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database represents the database connection and operations
type Database struct {
	db *gorm.DB
}

// NewDatabase creates a new database connection
func NewDatabase(cfg config.DatabaseConfig) (*Database, error) {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.Database, cfg.SSLMode)

	// Configure GORM logger
	logLevel := logger.Silent // Default to silent

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return &Database{db: db}, nil
}

// NewDatabaseFromGORM creates a Database wrapper from an existing GORM DB
func NewDatabaseFromGORM(db *gorm.DB) *Database {
	return &Database{db: db}
}

// Migrate runs database migrations
func (d *Database) Migrate() error {
	return d.db.AutoMigrate(
		&types.User{},
		&types.Organization{},
		&types.Pipeline{},
		&types.PipelineExecution{},
		&types.Job{},
		&types.Secret{},
		&types.Artifact{},
	)
}

// Close closes the database connection
func (d *Database) Close() error {
	sqlDB, err := d.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// GetDB returns the underlying GORM database instance
func (d *Database) GetDB() *gorm.DB {
	return d.db
}

// Health checks database connectivity
func (d *Database) Health() error {
	sqlDB, err := d.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// PipelineRepository handles pipeline database operations
type PipelineRepository struct {
	db *gorm.DB
}

// NewPipelineRepository creates a new pipeline repository
func NewPipelineRepository(db *Database) *PipelineRepository {
	return &PipelineRepository{db: db.GetDB()}
}

// Create creates a new pipeline
func (r *PipelineRepository) Create(pipeline *types.Pipeline) error {
	return r.db.Create(pipeline).Error
}

// GetByID retrieves a pipeline by ID
func (r *PipelineRepository) GetByID(id string) (*types.Pipeline, error) {
	var pipeline types.Pipeline
	err := r.db.Preload("Organization").Preload("CreatedBy").First(&pipeline, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &pipeline, nil
}

// GetByRepository retrieves pipelines by repository
func (r *PipelineRepository) GetByRepository(repository string) ([]types.Pipeline, error) {
	var pipelines []types.Pipeline
	err := r.db.Preload("Organization").Preload("CreatedBy").
		Where("repository = ? AND is_active = ?", repository, true).
		Find(&pipelines).Error
	return pipelines, err
}

// List retrieves all pipelines with pagination
func (r *PipelineRepository) List(page, perPage int, orgID *string) ([]types.Pipeline, int64, error) {
	var pipelines []types.Pipeline
	var total int64

	query := r.db.Model(&types.Pipeline{}).Where("is_active = ?", true)
	if orgID != nil {
		query = query.Where("organization_id = ?", *orgID)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := (page - 1) * perPage
	err := query.Preload("Organization").Preload("CreatedBy").
		Offset(offset).Limit(perPage).
		Order("created_at DESC").
		Find(&pipelines).Error

	return pipelines, total, err
}

// Update updates a pipeline
func (r *PipelineRepository) Update(pipeline *types.Pipeline) error {
	return r.db.Save(pipeline).Error
}

// Delete soft deletes a pipeline
func (r *PipelineRepository) Delete(id string) error {
	return r.db.Model(&types.Pipeline{}).Where("id = ?", id).Update("is_active", false).Error
}

// ExecutionRepository handles pipeline execution database operations
type ExecutionRepository struct {
	db *gorm.DB
}

// NewExecutionRepository creates a new execution repository
func NewExecutionRepository(db *Database) *ExecutionRepository {
	return &ExecutionRepository{db: db.GetDB()}
}

// Create creates a new pipeline execution
func (r *ExecutionRepository) Create(execution *types.PipelineExecution) error {
	return r.db.Create(execution).Error
}

// GetByID retrieves an execution by ID
func (r *ExecutionRepository) GetByID(id string) (*types.PipelineExecution, error) {
	var execution types.PipelineExecution
	err := r.db.Preload("Pipeline").Preload("Jobs").First(&execution, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &execution, nil
}

// GetByPipelineID retrieves executions by pipeline ID
func (r *ExecutionRepository) GetByPipelineID(pipelineID string, page, perPage int) ([]types.PipelineExecution, int64, error) {
	var executions []types.PipelineExecution
	var total int64

	query := r.db.Model(&types.PipelineExecution{}).Where("pipeline_id = ?", pipelineID)

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := (page - 1) * perPage
	err := query.Preload("Pipeline").Preload("Jobs").
		Offset(offset).Limit(perPage).
		Order("created_at DESC").
		Find(&executions).Error

	return executions, total, err
}

// Update updates an execution
func (r *ExecutionRepository) Update(execution *types.PipelineExecution) error {
	return r.db.Save(execution).Error
}

// GetRunningExecutions retrieves all running executions
func (r *ExecutionRepository) GetRunningExecutions() ([]types.PipelineExecution, error) {
	var executions []types.PipelineExecution
	err := r.db.Preload("Pipeline").Preload("Jobs").
		Where("status IN ?", []types.ExecutionStatus{types.StatusPending, types.StatusQueued, types.StatusRunning}).
		Find(&executions).Error
	return executions, err
}

// JobRepository handles job database operations
type JobRepository struct {
	db *gorm.DB
}

// NewJobRepository creates a new job repository
func NewJobRepository(db *Database) *JobRepository {
	return &JobRepository{db: db.GetDB()}
}

// Create creates a new job
func (r *JobRepository) Create(job *types.Job) error {
	return r.db.Create(job).Error
}

// GetByID retrieves a job by ID
func (r *JobRepository) GetByID(id string) (*types.Job, error) {
	var job types.Job
	err := r.db.Preload("Execution").First(&job, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &job, nil
}

// GetByExecutionID retrieves jobs by execution ID
func (r *JobRepository) GetByExecutionID(executionID string) ([]types.Job, error) {
	var jobs []types.Job
	err := r.db.Where("execution_id = ?", executionID).
		Order("created_at ASC").
		Find(&jobs).Error
	return jobs, err
}

// Update updates a job
func (r *JobRepository) Update(job *types.Job) error {
	return r.db.Save(job).Error
}

// GetPendingJobs retrieves all pending jobs
func (r *JobRepository) GetPendingJobs() ([]types.Job, error) {
	var jobs []types.Job
	err := r.db.Preload("Execution").Preload("Execution.Pipeline").
		Where("status = ?", types.StatusPending).
		Order("created_at ASC").
		Find(&jobs).Error
	return jobs, err
}

// UserRepository handles user database operations
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *Database) *UserRepository {
	return &UserRepository{db: db.GetDB()}
}

// Create creates a new user
func (r *UserRepository) Create(user *types.User) error {
	return r.db.Create(user).Error
}

// GetByID retrieves a user by ID
func (r *UserRepository) GetByID(id string) (*types.User, error) {
	var user types.User
	err := r.db.First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByEmail retrieves a user by email
func (r *UserRepository) GetByEmail(email string) (*types.User, error) {
	var user types.User
	err := r.db.First(&user, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByUsername retrieves a user by username
func (r *UserRepository) GetByUsername(username string) (*types.User, error) {
	var user types.User
	err := r.db.First(&user, "username = ?", username).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// Update updates a user
func (r *UserRepository) Update(user *types.User) error {
	return r.db.Save(user).Error
}

// SecretRepository handles secret database operations
type SecretRepository struct {
	db *gorm.DB
}

// NewSecretRepository creates a new secret repository
func NewSecretRepository(db *Database) *SecretRepository {
	return &SecretRepository{db: db.GetDB()}
}

// Create creates a new secret
func (r *SecretRepository) Create(secret *types.Secret) error {
	return r.db.Create(secret).Error
}

// GetByOrganizationID retrieves secrets by organization ID
func (r *SecretRepository) GetByOrganizationID(orgID string) ([]types.Secret, error) {
	var secrets []types.Secret
	err := r.db.Select("id", "organization_id", "name", "description", "created_at", "updated_at").
		Where("organization_id = ?", orgID).
		Find(&secrets).Error
	return secrets, err
}

// GetByName retrieves a secret by organization ID and name
func (r *SecretRepository) GetByName(orgID, name string) (*types.Secret, error) {
	var secret types.Secret
	err := r.db.First(&secret, "organization_id = ? AND name = ?", orgID, name).Error
	if err != nil {
		return nil, err
	}
	return &secret, nil
}

// Update updates a secret
func (r *SecretRepository) Update(secret *types.Secret) error {
	return r.db.Save(secret).Error
}

// Delete deletes a secret
func (r *SecretRepository) Delete(orgID, name string) error {
	return r.db.Where("organization_id = ? AND name = ?", orgID, name).Delete(&types.Secret{}).Error
}

// GetByProviderID retrieves users by provider and provider ID
func (r *UserRepository) GetByProviderID(provider, providerID string) ([]types.User, error) {
	var users []types.User
	err := r.db.Where("provider = ? AND provider_id = ?", provider, providerID).Find(&users).Error
	return users, err
}

// GetByIDs retrieves users by multiple IDs
func (r *UserRepository) GetByIDs(ids []string) ([]*types.User, error) {
	var users []*types.User
	err := r.db.Where("id IN ?", ids).Find(&users).Error
	return users, err
}
