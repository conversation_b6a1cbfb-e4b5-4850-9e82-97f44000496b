package templates

import (
	"context"
	"fmt"
	"strings"
	"text/template"

	"github.com/chainops/chainops/internal/storage"
	"github.com/chainops/chainops/pkg/types"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v3"
)

// TemplateManager manages pipeline templates
type TemplateManager struct {
	templateRepo *storage.TemplateRepository
	logger       *logrus.Logger
}

// Template is an alias for types.PipelineTemplate for backward compatibility
type Template = types.PipelineTemplate

// TemplateVar represents a template variable
type TemplateVar struct {
	Name         string      `json:"name"`
	Description  string      `json:"description"`
	Type         string      `json:"type"` // string, number, boolean, select
	DefaultValue interface{} `json:"default_value,omitempty"`
	Required     bool        `json:"required"`
	Options      []string    `json:"options,omitempty"` // for select type
}

// TemplateRequest represents a template creation/update request
type TemplateRequest struct {
	Name        string        `json:"name" binding:"required"`
	Description string        `json:"description"`
	Category    string        `json:"category"`
	Tags        []string      `json:"tags"`
	Content     string        `json:"content" binding:"required"`
	Variables   []TemplateVar `json:"variables"`
	IsPublic    bool          `json:"is_public"`
}

// TemplateInstance represents an instance of a template with values
type TemplateInstance struct {
	TemplateID uuid.UUID              `json:"template_id"`
	Values     map[string]interface{} `json:"values"`
}

// NewTemplateManager creates a new template manager
func NewTemplateManager(templateRepo *storage.TemplateRepository, logger *logrus.Logger) *TemplateManager {
	return &TemplateManager{
		templateRepo: templateRepo,
		logger:       logger,
	}
}

// CreateTemplate creates a new template
func (tm *TemplateManager) CreateTemplate(ctx context.Context, req TemplateRequest, userID string) (*Template, error) {
	// Validate template content
	if err := tm.validateTemplateContent(req.Content); err != nil {
		return nil, fmt.Errorf("invalid template content: %w", err)
	}

	// Parse user ID
	var createdByID *uuid.UUID
	if userID != "" {
		id, err := uuid.Parse(userID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}
		createdByID = &id
	}

	// Convert TemplateVar slice to map[string]string
	variables := make(map[string]string)
	for _, v := range req.Variables {
		variables[v.Name] = fmt.Sprintf("%v", v.DefaultValue)
	}

	template := &Template{
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		Tags:        req.Tags,
		YAMLContent: req.Content,
		Variables:   variables,
		IsPublic:    req.IsPublic,
		CreatedByID: createdByID,
	}

	if err := tm.templateRepo.Create(template); err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	tm.logger.WithFields(logrus.Fields{
		"template_id":   template.ID,
		"template_name": template.Name,
		"user_id":       userID,
	}).Info("Template created")

	return template, nil
}

// GetTemplate retrieves a template by ID
func (tm *TemplateManager) GetTemplate(ctx context.Context, templateID string) (*Template, error) {
	id, err := uuid.Parse(templateID)
	if err != nil {
		return nil, fmt.Errorf("invalid template ID: %w", err)
	}
	return tm.templateRepo.GetByID(id)
}

// ListTemplates lists templates with optional filters
func (tm *TemplateManager) ListTemplates(ctx context.Context, category string, tags []string, isPublic *bool) ([]*Template, error) {
	filter := storage.TemplateFilter{
		Category: category,
		Tags:     tags,
		IsPublic: isPublic,
	}
	return tm.templateRepo.List(filter)
}

// UpdateTemplate updates an existing template
func (tm *TemplateManager) UpdateTemplate(ctx context.Context, templateID string, req TemplateRequest, userID string) (*Template, error) {
	id, err := uuid.Parse(templateID)
	if err != nil {
		return nil, fmt.Errorf("invalid template ID: %w", err)
	}

	template, err := tm.templateRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}

	// Check permissions (only creator or admin can update)
	if template.CreatedByID != nil && userID != template.CreatedByID.String() {
		// TODO: Check if user is admin
		return nil, fmt.Errorf("permission denied")
	}

	// Validate template content
	if err := tm.validateTemplateContent(req.Content); err != nil {
		return nil, fmt.Errorf("invalid template content: %w", err)
	}

	// Convert TemplateVar slice to map[string]string
	variables := make(map[string]string)
	for _, v := range req.Variables {
		variables[v.Name] = fmt.Sprintf("%v", v.DefaultValue)
	}

	// Update fields
	template.Name = req.Name
	template.Description = req.Description
	template.Category = req.Category
	template.Tags = req.Tags
	template.YAMLContent = req.Content
	template.Variables = variables
	template.IsPublic = req.IsPublic

	if err := tm.templateRepo.Update(template); err != nil {
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	tm.logger.WithFields(logrus.Fields{
		"template_id":   template.ID,
		"template_name": template.Name,
		"user_id":       userID,
	}).Info("Template updated")

	return template, nil
}

// DeleteTemplate deletes a template
func (tm *TemplateManager) DeleteTemplate(ctx context.Context, templateID string, userID string) error {
	id, err := uuid.Parse(templateID)
	if err != nil {
		return fmt.Errorf("invalid template ID: %w", err)
	}

	template, err := tm.templateRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("template not found: %w", err)
	}

	// Check permissions
	if template.CreatedByID != nil && userID != template.CreatedByID.String() {
		// TODO: Check if user is admin
		return fmt.Errorf("permission denied")
	}

	if err := tm.templateRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}

	tm.logger.WithFields(logrus.Fields{
		"template_id":   template.ID,
		"template_name": template.Name,
		"user_id":       userID,
	}).Info("Template deleted")

	return nil
}

// InstantiateTemplate creates a pipeline from a template
func (tm *TemplateManager) InstantiateTemplate(
	ctx context.Context,
	templateID string,
	values map[string]interface{},
	pipelineName string,
	repository string,
	userID string,
) (*types.Pipeline, error) {
	// Get template
	id, err := uuid.Parse(templateID)
	if err != nil {
		return nil, fmt.Errorf("invalid template ID: %w", err)
	}

	template, err := tm.templateRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}

	// Validate required variables
	if err := tm.validateTemplateValues(template, values); err != nil {
		return nil, fmt.Errorf("invalid template values: %w", err)
	}

	// Render template
	renderedContent, err := tm.renderTemplate(template.YAMLContent, values)
	if err != nil {
		return nil, fmt.Errorf("failed to render template: %w", err)
	}

	// Parse user ID
	var createdByID *uuid.UUID
	if userID != "" {
		id, err := uuid.Parse(userID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}
		createdByID = &id
	}

	// Create pipeline
	pipeline := &types.Pipeline{
		Name:        pipelineName,
		Repository:  repository,
		YAMLContent: renderedContent,
		CreatedByID: createdByID,
		IsActive:    true,
	}

	tm.logger.WithFields(logrus.Fields{
		"template_id":   template.ID,
		"template_name": template.Name,
		"pipeline_name": pipelineName,
		"user_id":       userID,
	}).Info("Template instantiated")

	return pipeline, nil
}

// GetBuiltinTemplates returns built-in templates
func (tm *TemplateManager) GetBuiltinTemplates() []*Template {
	return []*Template{
		{
			ID:          uuid.New(),
			Name:        "Node.js Application",
			Description: "Template for Node.js applications with testing and deployment",
			Category:    "Web Applications",
			Tags:        []string{"nodejs", "javascript", "web"},
			YAMLContent: tm.getNodeJSTemplate(),
			Variables: map[string]string{
				"node_version":    "18",
				"package_manager": "npm",
				"test_command":    "npm test",
				"build_command":   "npm run build",
			},
			IsPublic: true,
		},
		{
			ID:          uuid.New(),
			Name:        "Docker Application",
			Description: "Template for containerized applications",
			Category:    "Containers",
			Tags:        []string{"docker", "container"},
			YAMLContent: tm.getDockerTemplate(),
			Variables: map[string]string{
				"dockerfile_path": "Dockerfile",
				"image_name":      "",
				"registry":        "docker.io",
				"context_path":    ".",
			},
			IsPublic: true,
		},
		{
			ID:          uuid.New(),
			Name:        "Go Application",
			Description: "Template for Go applications",
			Category:    "Backend",
			Tags:        []string{"go", "golang", "backend"},
			YAMLContent: tm.getGoTemplate(),
			Variables: map[string]string{
				"go_version":  "1.21",
				"module_name": "",
				"binary_name": "",
			},
			IsPublic: true,
		},
	}
}

// validateTemplateContent validates template YAML content
func (tm *TemplateManager) validateTemplateContent(content string) error {
	// Parse as YAML to check syntax
	var data interface{}
	if err := yaml.Unmarshal([]byte(content), &data); err != nil {
		return fmt.Errorf("invalid YAML: %w", err)
	}

	// Check for required pipeline structure
	if !strings.Contains(content, "pipeline:") {
		return fmt.Errorf("template must contain a 'pipeline' section")
	}

	return nil
}

// validateTemplateValues validates template variable values
func (tm *TemplateManager) validateTemplateValues(template *Template, values map[string]interface{}) error {
	// For now, just check that all template variables have corresponding values
	// In a more sophisticated implementation, we could store variable metadata
	// separately and validate against that
	for varName := range template.Variables {
		if _, exists := values[varName]; !exists {
			// Set default value if not provided
			values[varName] = template.Variables[varName]
		}
	}

	return nil
}

// renderTemplate renders a template with given values
func (tm *TemplateManager) renderTemplate(content string, values map[string]interface{}) (string, error) {
	tmpl, err := template.New("pipeline").Parse(content)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}

	var buf strings.Builder
	if err := tmpl.Execute(&buf, values); err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}

	return buf.String(), nil
}

// Built-in template content
func (tm *TemplateManager) getNodeJSTemplate() string {
	return `pipeline:
  name: {{.pipeline_name}}
  
  variables:
    NODE_VERSION: "{{.node_version}}"
    PACKAGE_MANAGER: "{{.package_manager}}"
  
  triggers:
    - on: [push, pull_request]
      branches: [main, develop]
  
  stages:
    - name: setup
      steps:
        - name: checkout
          uses: checkout@v1
        - name: setup-node
          uses: setup-node@v1
          with:
            node-version: $NODE_VERSION
    
    - name: dependencies
      steps:
        - name: install
          run: {{.package_manager}} ci
    
    - name: test
      steps:
        - name: lint
          run: {{.package_manager}} run lint
        - name: test
          run: {{.test_command}}
    
    - name: build
      steps:
        - name: build
          run: {{.build_command}}
    
    - name: deploy
      condition: branch == "main"
      steps:
        - name: deploy
          run: echo "Deploying application..."`
}

func (tm *TemplateManager) getDockerTemplate() string {
	return `pipeline:
  name: {{.pipeline_name}}
  
  variables:
    IMAGE_NAME: "{{.image_name}}"
    REGISTRY: "{{.registry}}"
    DOCKERFILE: "{{.dockerfile_path}}"
    CONTEXT: "{{.context_path}}"
  
  triggers:
    - on: [push]
      branches: [main]
  
  stages:
    - name: build
      steps:
        - name: checkout
          uses: checkout@v1
        - name: build-image
          uses: docker@v1
          with:
            action: build
            dockerfile: $DOCKERFILE
            context: $CONTEXT
            tag: $REGISTRY/$IMAGE_NAME:$GITHUB_SHA
    
    - name: test
      steps:
        - name: test-image
          uses: docker@v1
          with:
            action: run
            image: $REGISTRY/$IMAGE_NAME:$GITHUB_SHA
            command: echo "Running tests..."
    
    - name: push
      steps:
        - name: push-image
          uses: docker@v1
          with:
            action: push
            tag: $REGISTRY/$IMAGE_NAME:$GITHUB_SHA`
}

func (tm *TemplateManager) getGoTemplate() string {
	return `pipeline:
  name: {{.pipeline_name}}
  
  variables:
    GO_VERSION: "{{.go_version}}"
    MODULE_NAME: "{{.module_name}}"
    BINARY_NAME: "{{.binary_name}}"
  
  triggers:
    - on: [push, pull_request]
      branches: [main, develop]
  
  stages:
    - name: setup
      steps:
        - name: checkout
          uses: checkout@v1
        - name: setup-go
          uses: setup-go@v1
          with:
            go-version: $GO_VERSION
    
    - name: dependencies
      steps:
        - name: download
          run: go mod download
        - name: verify
          run: go mod verify
    
    - name: test
      steps:
        - name: vet
          run: go vet ./...
        - name: test
          run: go test -v ./...
        - name: race
          run: go test -race ./...
    
    - name: build
      steps:
        - name: build
          run: go build -o $BINARY_NAME ./cmd/...
    
    - name: deploy
      condition: branch == "main"
      steps:
        - name: deploy
          run: echo "Deploying $BINARY_NAME..."`
}
