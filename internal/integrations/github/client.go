package github

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/chainops/chainops/pkg/types"
	"github.com/sirupsen/logrus"
)

// Client represents a GitHub API client
type Client struct {
	baseURL    string
	token      string
	httpClient *http.Client
	logger     *logrus.Logger
}

// Config represents GitHub integration configuration
type Config struct {
	Token      string `yaml:"token"`
	BaseURL    string `yaml:"base_url"`
	WebhookURL string `yaml:"webhook_url"`
	Secret     string `yaml:"secret"`
}

// Repository represents a GitHub repository
type Repository struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	FullName    string `json:"full_name"`
	CloneURL    string `json:"clone_url"`
	SSHURL      string `json:"ssh_url"`
	HTMLURL     string `json:"html_url"`
	DefaultBranch string `json:"default_branch"`
	Private     bool   `json:"private"`
	Owner       Owner  `json:"owner"`
}

// Owner represents a repository owner
type Owner struct {
	Login string `json:"login"`
	Type  string `json:"type"`
}

// PullRequest represents a GitHub pull request
type PullRequest struct {
	ID     int64  `json:"id"`
	Number int    `json:"number"`
	Title  string `json:"title"`
	State  string `json:"state"`
	Head   Branch `json:"head"`
	Base   Branch `json:"base"`
	User   User   `json:"user"`
}

// Branch represents a git branch
type Branch struct {
	Ref  string     `json:"ref"`
	SHA  string     `json:"sha"`
	Repo Repository `json:"repo"`
}

// User represents a GitHub user
type User struct {
	Login string `json:"login"`
	Email string `json:"email"`
}

// Commit represents a git commit
type Commit struct {
	SHA     string    `json:"sha"`
	Message string    `json:"message"`
	Author  CommitUser `json:"author"`
	URL     string    `json:"url"`
}

// CommitUser represents commit author/committer
type CommitUser struct {
	Name  string    `json:"name"`
	Email string    `json:"email"`
	Date  time.Time `json:"date"`
}

// WebhookEvent represents a GitHub webhook event
type WebhookEvent struct {
	Action     string      `json:"action"`
	Repository Repository  `json:"repository"`
	PullRequest *PullRequest `json:"pull_request,omitempty"`
	Commits    []Commit    `json:"commits,omitempty"`
	HeadCommit *Commit     `json:"head_commit,omitempty"`
	Ref        string      `json:"ref"`
	Before     string      `json:"before"`
	After      string      `json:"after"`
	Sender     User        `json:"sender"`
}

// CheckRun represents a GitHub check run
type CheckRun struct {
	ID         int64     `json:"id"`
	Name       string    `json:"name"`
	Status     string    `json:"status"`
	Conclusion string    `json:"conclusion"`
	StartedAt  time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Output     CheckOutput `json:"output"`
}

// CheckOutput represents check run output
type CheckOutput struct {
	Title   string `json:"title"`
	Summary string `json:"summary"`
	Text    string `json:"text"`
}

// NewClient creates a new GitHub client
func NewClient(config Config, logger *logrus.Logger) *Client {
	baseURL := config.BaseURL
	if baseURL == "" {
		baseURL = "https://api.github.com"
	}

	return &Client{
		baseURL: baseURL,
		token:   config.Token,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// GetRepository retrieves repository information
func (c *Client) GetRepository(ctx context.Context, owner, repo string) (*Repository, error) {
	url := fmt.Sprintf("%s/repos/%s/%s", c.baseURL, owner, repo)
	
	var repository Repository
	if err := c.makeRequest(ctx, "GET", url, nil, &repository); err != nil {
		return nil, fmt.Errorf("failed to get repository: %w", err)
	}

	return &repository, nil
}

// GetPullRequest retrieves pull request information
func (c *Client) GetPullRequest(ctx context.Context, owner, repo string, number int) (*PullRequest, error) {
	url := fmt.Sprintf("%s/repos/%s/%s/pulls/%d", c.baseURL, owner, repo, number)
	
	var pr PullRequest
	if err := c.makeRequest(ctx, "GET", url, nil, &pr); err != nil {
		return nil, fmt.Errorf("failed to get pull request: %w", err)
	}

	return &pr, nil
}

// CreateCheckRun creates a check run for a commit
func (c *Client) CreateCheckRun(ctx context.Context, owner, repo, sha string, checkRun CheckRun) (*CheckRun, error) {
	url := fmt.Sprintf("%s/repos/%s/%s/check-runs", c.baseURL, owner, repo)
	
	payload := map[string]interface{}{
		"name":       checkRun.Name,
		"head_sha":   sha,
		"status":     checkRun.Status,
		"started_at": checkRun.StartedAt.Format(time.RFC3339),
	}

	if checkRun.Conclusion != "" {
		payload["conclusion"] = checkRun.Conclusion
	}

	if checkRun.CompletedAt != nil {
		payload["completed_at"] = checkRun.CompletedAt.Format(time.RFC3339)
	}

	if checkRun.Output.Title != "" || checkRun.Output.Summary != "" {
		payload["output"] = map[string]interface{}{
			"title":   checkRun.Output.Title,
			"summary": checkRun.Output.Summary,
			"text":    checkRun.Output.Text,
		}
	}

	var result CheckRun
	if err := c.makeRequest(ctx, "POST", url, payload, &result); err != nil {
		return nil, fmt.Errorf("failed to create check run: %w", err)
	}

	return &result, nil
}

// UpdateCheckRun updates an existing check run
func (c *Client) UpdateCheckRun(ctx context.Context, owner, repo string, checkRunID int64, checkRun CheckRun) (*CheckRun, error) {
	url := fmt.Sprintf("%s/repos/%s/%s/check-runs/%d", c.baseURL, owner, repo, checkRunID)
	
	payload := map[string]interface{}{
		"status": checkRun.Status,
	}

	if checkRun.Conclusion != "" {
		payload["conclusion"] = checkRun.Conclusion
	}

	if checkRun.CompletedAt != nil {
		payload["completed_at"] = checkRun.CompletedAt.Format(time.RFC3339)
	}

	if checkRun.Output.Title != "" || checkRun.Output.Summary != "" {
		payload["output"] = map[string]interface{}{
			"title":   checkRun.Output.Title,
			"summary": checkRun.Output.Summary,
			"text":    checkRun.Output.Text,
		}
	}

	var result CheckRun
	if err := c.makeRequest(ctx, "PATCH", url, payload, &result); err != nil {
		return nil, fmt.Errorf("failed to update check run: %w", err)
	}

	return &result, nil
}

// CreateCommitStatus creates a commit status
func (c *Client) CreateCommitStatus(ctx context.Context, owner, repo, sha string, status types.PipelineStatus, description, targetURL string) error {
	url := fmt.Sprintf("%s/repos/%s/%s/statuses/%s", c.baseURL, owner, repo, sha)
	
	state := c.mapPipelineStatusToGitHubState(status)
	
	payload := map[string]interface{}{
		"state":       state,
		"description": description,
		"context":     "chainops/pipeline",
	}

	if targetURL != "" {
		payload["target_url"] = targetURL
	}

	if err := c.makeRequest(ctx, "POST", url, payload, nil); err != nil {
		return fmt.Errorf("failed to create commit status: %w", err)
	}

	return nil
}

// ListBranches lists repository branches
func (c *Client) ListBranches(ctx context.Context, owner, repo string) ([]Branch, error) {
	url := fmt.Sprintf("%s/repos/%s/%s/branches", c.baseURL, owner, repo)
	
	var branches []Branch
	if err := c.makeRequest(ctx, "GET", url, nil, &branches); err != nil {
		return nil, fmt.Errorf("failed to list branches: %w", err)
	}

	return branches, nil
}

// GetCommit retrieves commit information
func (c *Client) GetCommit(ctx context.Context, owner, repo, sha string) (*Commit, error) {
	url := fmt.Sprintf("%s/repos/%s/%s/commits/%s", c.baseURL, owner, repo, sha)
	
	var commit Commit
	if err := c.makeRequest(ctx, "GET", url, nil, &commit); err != nil {
		return nil, fmt.Errorf("failed to get commit: %w", err)
	}

	return &commit, nil
}

// makeRequest makes an HTTP request to the GitHub API
func (c *Client) makeRequest(ctx context.Context, method, url string, payload interface{}, result interface{}) error {
	var body []byte
	var err error

	if payload != nil {
		body, err = json.Marshal(payload)
		if err != nil {
			return fmt.Errorf("failed to marshal payload: %w", err)
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, url, strings.NewReader(string(body)))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "token "+c.token)
	req.Header.Set("Accept", "application/vnd.github.v3+json")
	req.Header.Set("User-Agent", "ChainOps/1.0")

	if payload != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("GitHub API returned status %d", resp.StatusCode)
	}

	if result != nil {
		if err := json.NewDecoder(resp.Body).Decode(result); err != nil {
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}

// mapPipelineStatusToGitHubState maps pipeline status to GitHub commit state
func (c *Client) mapPipelineStatusToGitHubState(status types.PipelineStatus) string {
	switch status {
	case types.PipelineStatusPending, types.PipelineStatusRunning:
		return "pending"
	case types.PipelineStatusSuccess:
		return "success"
	case types.PipelineStatusFailed:
		return "failure"
	case types.PipelineStatusCancelled:
		return "error"
	default:
		return "pending"
	}
}
