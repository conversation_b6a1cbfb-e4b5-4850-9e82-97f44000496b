package github

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/chainops/chainops/pkg/types"
	"github.com/sirupsen/logrus"
)

// WebhookHandler handles GitHub webhook events
type WebhookHandler struct {
	secret         string
	pipelineEngine PipelineEngine
	logger         *logrus.Logger
}

// PipelineEngine interface for triggering pipelines
type PipelineEngine interface {
	TriggerPipeline(ctx context.Context, req *types.TriggerPipelineRequest) (*types.PipelineExecution, error)
	GetPipelineByRepository(ctx context.Context, repository string) (*types.Pipeline, error)
}

// NewWebhookHandler creates a new webhook handler
func NewWebhookHandler(secret string, pipelineEngine PipelineEngine, logger *logrus.Logger) *WebhookHandler {
	return &WebhookHandler{
		secret:         secret,
		pipelineEngine: pipelineEngine,
		logger:         logger,
	}
}

// HandleWebhook handles incoming GitHub webhook requests
func (wh *WebhookHandler) HandleWebhook(w http.ResponseWriter, r *http.Request) {
	// Read the request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to read webhook body")
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	// Verify the webhook signature
	if wh.secret != "" {
		signature := r.Header.Get("X-Hub-Signature-256")
		if !wh.verifySignature(body, signature) {
			wh.logger.Error("Invalid webhook signature")
			http.Error(w, "Invalid signature", http.StatusUnauthorized)
			return
		}
	}

	// Get the event type
	eventType := r.Header.Get("X-GitHub-Event")
	if eventType == "" {
		wh.logger.Error("Missing X-GitHub-Event header")
		http.Error(w, "Missing event type", http.StatusBadRequest)
		return
	}

	// Parse the webhook event
	var event WebhookEvent
	if err := json.Unmarshal(body, &event); err != nil {
		wh.logger.WithError(err).Error("Failed to parse webhook event")
		http.Error(w, "Failed to parse event", http.StatusBadRequest)
		return
	}

	// Handle the event
	ctx := r.Context()
	if err := wh.handleEvent(ctx, eventType, &event); err != nil {
		wh.logger.WithError(err).WithField("event_type", eventType).Error("Failed to handle webhook event")
		http.Error(w, "Failed to handle event", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

// handleEvent handles different types of GitHub events
func (wh *WebhookHandler) handleEvent(ctx context.Context, eventType string, event *WebhookEvent) error {
	wh.logger.WithFields(logrus.Fields{
		"event_type": eventType,
		"action":     event.Action,
		"repository": event.Repository.FullName,
	}).Info("Handling GitHub webhook event")

	switch eventType {
	case "push":
		return wh.handlePushEvent(ctx, event)
	case "pull_request":
		return wh.handlePullRequestEvent(ctx, event)
	case "create":
		return wh.handleCreateEvent(ctx, event)
	case "delete":
		return wh.handleDeleteEvent(ctx, event)
	default:
		wh.logger.WithField("event_type", eventType).Debug("Unhandled event type")
		return nil
	}
}

// handlePushEvent handles push events
func (wh *WebhookHandler) handlePushEvent(ctx context.Context, event *WebhookEvent) error {
	// Skip if this is a delete event (after field is all zeros)
	if event.After == "0000000000000000000000000000000000000000" {
		return nil
	}

	// Extract branch name from ref
	branch := strings.TrimPrefix(event.Ref, "refs/heads/")
	
	// Get pipeline for this repository
	pipeline, err := wh.pipelineEngine.GetPipelineByRepository(ctx, event.Repository.CloneURL)
	if err != nil {
		wh.logger.WithError(err).WithField("repository", event.Repository.FullName).Debug("No pipeline found for repository")
		return nil // Not an error - repository might not have a pipeline
	}

	// Create trigger request
	triggerReq := &types.TriggerPipelineRequest{
		PipelineID:  pipeline.ID,
		TriggerType: types.TriggerWebhook,
		Branch:      branch,
		CommitSHA:   event.After,
		Variables: map[string]string{
			"GITHUB_EVENT_TYPE":   "push",
			"GITHUB_REPOSITORY":   event.Repository.FullName,
			"GITHUB_REF":          event.Ref,
			"GITHUB_SHA":          event.After,
			"GITHUB_ACTOR":        event.Sender.Login,
			"GITHUB_WORKFLOW":     pipeline.Name,
		},
		Metadata: map[string]interface{}{
			"github_event":      event,
			"repository":        event.Repository,
			"commits":           event.Commits,
			"head_commit":       event.HeadCommit,
			"trigger_source":    "github_webhook",
		},
	}

	// Add commit information if available
	if event.HeadCommit != nil {
		triggerReq.Variables["GITHUB_COMMIT_MESSAGE"] = event.HeadCommit.Message
		triggerReq.Variables["GITHUB_COMMIT_AUTHOR"] = event.HeadCommit.Author.Name
		triggerReq.Variables["GITHUB_COMMIT_EMAIL"] = event.HeadCommit.Author.Email
	}

	// Trigger the pipeline
	execution, err := wh.pipelineEngine.TriggerPipeline(ctx, triggerReq)
	if err != nil {
		return fmt.Errorf("failed to trigger pipeline: %w", err)
	}

	wh.logger.WithFields(logrus.Fields{
		"pipeline_id":   pipeline.ID,
		"execution_id":  execution.ID,
		"repository":    event.Repository.FullName,
		"branch":        branch,
		"commit":        event.After,
	}).Info("Pipeline triggered from GitHub push event")

	return nil
}

// handlePullRequestEvent handles pull request events
func (wh *WebhookHandler) handlePullRequestEvent(ctx context.Context, event *WebhookEvent) error {
	// Only handle opened, synchronize, and reopened actions
	if event.Action != "opened" && event.Action != "synchronize" && event.Action != "reopened" {
		return nil
	}

	if event.PullRequest == nil {
		return fmt.Errorf("pull request data missing from event")
	}

	// Get pipeline for this repository
	pipeline, err := wh.pipelineEngine.GetPipelineByRepository(ctx, event.Repository.CloneURL)
	if err != nil {
		wh.logger.WithError(err).WithField("repository", event.Repository.FullName).Debug("No pipeline found for repository")
		return nil
	}

	// Create trigger request
	triggerReq := &types.TriggerPipelineRequest{
		PipelineID:  pipeline.ID,
		TriggerType: types.TriggerWebhook,
		Branch:      event.PullRequest.Head.Ref,
		CommitSHA:   event.PullRequest.Head.SHA,
		Variables: map[string]string{
			"GITHUB_EVENT_TYPE":     "pull_request",
			"GITHUB_REPOSITORY":     event.Repository.FullName,
			"GITHUB_REF":            fmt.Sprintf("refs/pull/%d/merge", event.PullRequest.Number),
			"GITHUB_SHA":            event.PullRequest.Head.SHA,
			"GITHUB_ACTOR":          event.Sender.Login,
			"GITHUB_WORKFLOW":       pipeline.Name,
			"GITHUB_PR_NUMBER":      fmt.Sprintf("%d", event.PullRequest.Number),
			"GITHUB_PR_TITLE":       event.PullRequest.Title,
			"GITHUB_PR_AUTHOR":      event.PullRequest.User.Login,
			"GITHUB_BASE_REF":       event.PullRequest.Base.Ref,
			"GITHUB_HEAD_REF":       event.PullRequest.Head.Ref,
		},
		Metadata: map[string]interface{}{
			"github_event":      event,
			"repository":        event.Repository,
			"pull_request":      event.PullRequest,
			"trigger_source":    "github_webhook",
			"is_pull_request":   true,
		},
	}

	// Trigger the pipeline
	execution, err := wh.pipelineEngine.TriggerPipeline(ctx, triggerReq)
	if err != nil {
		return fmt.Errorf("failed to trigger pipeline: %w", err)
	}

	wh.logger.WithFields(logrus.Fields{
		"pipeline_id":   pipeline.ID,
		"execution_id":  execution.ID,
		"repository":    event.Repository.FullName,
		"pr_number":     event.PullRequest.Number,
		"branch":        event.PullRequest.Head.Ref,
		"commit":        event.PullRequest.Head.SHA,
	}).Info("Pipeline triggered from GitHub pull request event")

	return nil
}

// handleCreateEvent handles branch/tag creation events
func (wh *WebhookHandler) handleCreateEvent(ctx context.Context, event *WebhookEvent) error {
	// Only handle branch creation for now
	if event.Ref == "" {
		return nil
	}

	wh.logger.WithFields(logrus.Fields{
		"repository": event.Repository.FullName,
		"ref":        event.Ref,
		"ref_type":   "branch", // GitHub sends this in the event, but we're not parsing it
	}).Info("Branch created")

	// Could trigger pipeline for new branches based on configuration
	return nil
}

// handleDeleteEvent handles branch/tag deletion events
func (wh *WebhookHandler) handleDeleteEvent(ctx context.Context, event *WebhookEvent) error {
	wh.logger.WithFields(logrus.Fields{
		"repository": event.Repository.FullName,
		"ref":        event.Ref,
	}).Info("Branch deleted")

	// Could clean up related pipeline executions
	return nil
}

// verifySignature verifies the GitHub webhook signature
func (wh *WebhookHandler) verifySignature(body []byte, signature string) bool {
	if signature == "" {
		return false
	}

	// Remove the "sha256=" prefix
	signature = strings.TrimPrefix(signature, "sha256=")

	// Compute the expected signature
	mac := hmac.New(sha256.New, []byte(wh.secret))
	mac.Write(body)
	expectedSignature := hex.EncodeToString(mac.Sum(nil))

	// Compare signatures
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// GetSupportedEvents returns the list of GitHub events this handler supports
func (wh *WebhookHandler) GetSupportedEvents() []string {
	return []string{
		"push",
		"pull_request",
		"create",
		"delete",
	}
}
