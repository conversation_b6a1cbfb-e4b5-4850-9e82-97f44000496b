package app

import (
	"context"
	"fmt"

	"github.com/chainops/chainops/internal/config"
	"github.com/chainops/chainops/internal/core/approval"
	"github.com/chainops/chainops/internal/core/pipeline"
	"github.com/chainops/chainops/internal/core/runner"
	"github.com/chainops/chainops/internal/core/trigger"
	"github.com/chainops/chainops/internal/monitoring"
	"github.com/chainops/chainops/internal/storage"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// Application represents the main application
type Application struct {
	config *config.Config
	logger *logrus.Logger
	db     *gorm.DB

	// Core services
	pipelineEngine *pipeline.Engine
	runnerExecutor *runner.Executor
	triggerSystem  *trigger.System

	// Advanced services
	approvalManager *approval.Manager
	// TODO: Add these when managers are created
	// templateManager *templates.Manager
	// pluginManager   *plugins.Manager
	// rbacManager     *auth.RBACManager

	// Infrastructure
	messageQueue     storage.MessageQueue
	cache            storage.Cache
	metricsCollector *monitoring.MetricsCollector

	// Repositories
	pipelineRepo  *storage.PipelineRepository
	executionRepo *storage.ExecutionRepository
	jobRepo       *storage.JobRepository
	userRepo      *storage.UserRepository
	approvalRepo  *storage.ApprovalRepository
	// TODO: Add these when needed
	// templateRepo   *storage.TemplateRepository
	// pluginRepo     *storage.PluginRepository
	// roleRepo       *storage.RoleRepository
	// permissionRepo *storage.PermissionRepository
	// userRoleRepo   *storage.UserRoleRepository
}

// New creates a new application instance
func New(cfg *config.Config, logger *logrus.Logger, db *gorm.DB) *Application {
	return &Application{
		config: cfg,
		logger: logger,
		db:     db,
	}
}

// Initialize initializes all application components
func (a *Application) Initialize() error {
	a.logger.Info("Initializing ChainOps application")

	// Initialize infrastructure
	if err := a.initializeInfrastructure(); err != nil {
		return fmt.Errorf("failed to initialize infrastructure: %w", err)
	}

	// Initialize repositories
	if err := a.initializeRepositories(); err != nil {
		return fmt.Errorf("failed to initialize repositories: %w", err)
	}

	// Initialize core services
	if err := a.initializeCoreServices(); err != nil {
		return fmt.Errorf("failed to initialize core services: %w", err)
	}

	// Initialize monitoring
	if err := a.initializeMonitoring(); err != nil {
		return fmt.Errorf("failed to initialize monitoring: %w", err)
	}

	// Wire up dependencies
	if err := a.wireUpDependencies(); err != nil {
		return fmt.Errorf("failed to wire up dependencies: %w", err)
	}

	a.logger.Info("ChainOps application initialized successfully")
	return nil
}

// initializeInfrastructure initializes infrastructure components
func (a *Application) initializeInfrastructure() error {
	// Initialize message queue (stub for now)
	a.messageQueue = storage.NewInMemoryMessageQueue(a.logger)

	// Initialize cache (stub for now)
	a.cache = storage.NewInMemoryCache(a.logger)

	return nil
}

// initializeRepositories initializes data repositories
func (a *Application) initializeRepositories() error {
	// Create storage database wrapper
	database := storage.NewDatabaseFromGORM(a.db)

	a.pipelineRepo = storage.NewPipelineRepository(database)
	a.executionRepo = storage.NewExecutionRepository(database)
	a.jobRepo = storage.NewJobRepository(database)
	a.userRepo = storage.NewUserRepository(database)
	a.approvalRepo = storage.NewApprovalRepository(a.db)

	return nil
}

// initializeCoreServices initializes core business services
func (a *Application) initializeCoreServices() error {
	// Create storage database wrapper
	database := storage.NewDatabaseFromGORM(a.db)

	// Initialize pipeline engine
	a.pipelineEngine = pipeline.NewEngine(database, a.logger)

	// Initialize runner executor
	a.runnerExecutor = runner.NewExecutor(a.config.Runner, a.logger)

	// Initialize trigger system
	a.triggerSystem = trigger.NewSystem(a.messageQueue, a.pipelineEngine, a.logger)

	// Initialize approval manager (with nil notifier for now)
	a.approvalManager = approval.NewManager(a.logger, a.approvalRepo, a.jobRepo, a.userRepo, nil)

	return nil
}

// initializeMonitoring initializes monitoring components
func (a *Application) initializeMonitoring() error {
	// Initialize metrics collector
	metricsConfig := monitoring.MetricsConfig{
		Enabled:   a.config.Metrics.Enabled,
		Endpoint:  a.config.Metrics.Endpoint,
		Namespace: a.config.Metrics.Namespace,
	}
	a.metricsCollector = monitoring.NewMetricsCollector(metricsConfig, a.logger)

	return nil
}

// wireUpDependencies wires up dependencies between components
func (a *Application) wireUpDependencies() error {
	// Initialize runner executor dependencies
	if err := a.runnerExecutor.Initialize(a.jobRepo, a.cache, nil); err != nil {
		return fmt.Errorf("failed to initialize runner executor: %w", err)
	}

	// Initialize trigger system dependencies
	a.triggerSystem.Initialize(a.pipelineRepo)

	return nil
}

// Start starts all application services
func (a *Application) Start(ctx context.Context) error {
	a.logger.Info("Starting ChainOps application services")

	// Start metrics collector
	if err := a.metricsCollector.Start(); err != nil {
		return fmt.Errorf("failed to start metrics collector: %w", err)
	}

	a.logger.Info("ChainOps application services started successfully")
	return nil
}

// Stop stops all application services
func (a *Application) Stop(ctx context.Context) error {
	a.logger.Info("Stopping ChainOps application services")

	// Stop metrics collector
	if err := a.metricsCollector.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop metrics collector")
	}

	a.logger.Info("ChainOps application services stopped")
	return nil
}

// GetServices returns all application services for API handlers
func (a *Application) GetServices() *Services {
	return &Services{
		PipelineEngine:   a.pipelineEngine,
		RunnerExecutor:   a.runnerExecutor,
		TriggerSystem:    a.triggerSystem,
		ApprovalManager:  a.approvalManager,
		MetricsCollector: a.metricsCollector,
		Repositories: &Repositories{
			Pipeline:  a.pipelineRepo,
			Execution: a.executionRepo,
			Job:       a.jobRepo,
			User:      a.userRepo,
			Approval:  a.approvalRepo,
		},
	}
}

// Services holds all application services
type Services struct {
	PipelineEngine   *pipeline.Engine
	RunnerExecutor   *runner.Executor
	TriggerSystem    *trigger.System
	ApprovalManager  *approval.Manager
	MetricsCollector *monitoring.MetricsCollector
	Repositories     *Repositories
}

// Repositories holds all data repositories
type Repositories struct {
	Pipeline  *storage.PipelineRepository
	Execution *storage.ExecutionRepository
	Job       *storage.JobRepository
	User      *storage.UserRepository
	Approval  *storage.ApprovalRepository
}
