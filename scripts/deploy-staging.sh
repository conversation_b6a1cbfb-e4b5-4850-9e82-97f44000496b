#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 ChainOps/PipelinerX Staging Deployment${NC}"
echo -e "${BLUE}=========================================${NC}"

# Configuration
ENVIRONMENT=${ENVIRONMENT:-staging}
COMPOSE_FILE=${COMPOSE_FILE:-docker-compose.staging.yml}
ENV_FILE=${ENV_FILE:-.env.staging}

# Check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is not installed${NC}"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose is not installed${NC}"
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        echo -e "${RED}❌ Docker daemon is not running${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Prerequisites check passed${NC}"
}

# Setup environment variables
setup_environment() {
    echo -e "${YELLOW}🔧 Setting up environment variables...${NC}"
    
    # Create .env.staging file if it doesn't exist
    if [ ! -f "$ENV_FILE" ]; then
        echo -e "${YELLOW}Creating $ENV_FILE file...${NC}"
        cat > "$ENV_FILE" << EOF
# Database Configuration
DB_PASSWORD=chainops_staging_password_$(openssl rand -hex 16)

# Redis Configuration
REDIS_PASSWORD=chainops_redis_password_$(openssl rand -hex 16)

# JWT Configuration
JWT_SECRET=$(openssl rand -base64 32)

# CSRF Configuration
CSRF_SECRET=$(openssl rand -base64 32)

# Grafana Configuration
GRAFANA_PASSWORD=admin_$(openssl rand -hex 8)

# Environment
ENVIRONMENT=staging
EOF
        echo -e "${GREEN}✅ Environment file created: $ENV_FILE${NC}"
    else
        echo -e "${GREEN}✅ Environment file exists: $ENV_FILE${NC}"
    fi
    
    # Load environment variables
    if [ -f "$ENV_FILE" ]; then
        export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    fi
}

# Build application
build_application() {
    echo -e "${YELLOW}🔨 Building application...${NC}"
    
    # Run tests first
    echo -e "${YELLOW}Running tests...${NC}"
    if ! ./scripts/run-tests.sh unit; then
        echo -e "${RED}❌ Tests failed, aborting deployment${NC}"
        exit 1
    fi
    
    # Build Docker images
    echo -e "${YELLOW}Building Docker images...${NC}"
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    
    echo -e "${GREEN}✅ Application built successfully${NC}"
}

# Deploy services
deploy_services() {
    echo -e "${YELLOW}🚀 Deploying services...${NC}"
    
    # Stop existing services
    echo -e "${YELLOW}Stopping existing services...${NC}"
    docker-compose -f "$COMPOSE_FILE" down --remove-orphans
    
    # Start database and cache first
    echo -e "${YELLOW}Starting database and cache...${NC}"
    docker-compose -f "$COMPOSE_FILE" up -d postgres redis
    
    # Wait for database to be ready
    echo -e "${YELLOW}Waiting for database to be ready...${NC}"
    timeout 60 bash -c 'until docker-compose -f '"$COMPOSE_FILE"' exec -T postgres pg_isready -U chainops -d chainops_staging; do sleep 2; done'
    
    # Run database migrations
    echo -e "${YELLOW}Running database migrations...${NC}"
    docker-compose -f "$COMPOSE_FILE" run --rm chainops ./migrate up
    
    # Start all services
    echo -e "${YELLOW}Starting all services...${NC}"
    docker-compose -f "$COMPOSE_FILE" up -d
    
    echo -e "${GREEN}✅ Services deployed successfully${NC}"
}

# Health checks
run_health_checks() {
    echo -e "${YELLOW}🏥 Running health checks...${NC}"
    
    # Wait for services to start
    echo -e "${YELLOW}Waiting for services to start...${NC}"
    sleep 30
    
    # Check ChainOps health
    echo -e "${YELLOW}Checking ChainOps health...${NC}"
    if curl -f http://localhost:8080/health; then
        echo -e "${GREEN}✅ ChainOps is healthy${NC}"
    else
        echo -e "${RED}❌ ChainOps health check failed${NC}"
        docker-compose -f "$COMPOSE_FILE" logs chainops
        exit 1
    fi
    
    # Check Prometheus
    echo -e "${YELLOW}Checking Prometheus...${NC}"
    if curl -f http://localhost:9090/-/healthy; then
        echo -e "${GREEN}✅ Prometheus is healthy${NC}"
    else
        echo -e "${YELLOW}⚠️  Prometheus health check failed${NC}"
    fi
    
    # Check Grafana
    echo -e "${YELLOW}Checking Grafana...${NC}"
    if curl -f http://localhost:3000/api/health; then
        echo -e "${GREEN}✅ Grafana is healthy${NC}"
    else
        echo -e "${YELLOW}⚠️  Grafana health check failed${NC}"
    fi
    
    echo -e "${GREEN}✅ Health checks completed${NC}"
}

# Show deployment info
show_deployment_info() {
    echo -e "${BLUE}📋 Deployment Information${NC}"
    echo -e "${BLUE}=========================${NC}"
    echo -e "${YELLOW}Environment:${NC} $ENVIRONMENT"
    echo -e "${YELLOW}ChainOps API:${NC} http://localhost:8080"
    echo -e "${YELLOW}Grafana Dashboard:${NC} http://localhost:3000"
    echo -e "${YELLOW}Prometheus:${NC} http://localhost:9090"
    echo -e "${YELLOW}Database:${NC} postgresql://chainops:***@localhost:5432/chainops_staging"
    echo -e "${YELLOW}Redis:${NC} redis://localhost:6379"
    echo ""
    echo -e "${YELLOW}Grafana Credentials:${NC}"
    echo -e "  Username: admin"
    echo -e "  Password: ${GRAFANA_PASSWORD:-admin}"
    echo ""
    echo -e "${YELLOW}Useful Commands:${NC}"
    echo -e "  View logs: docker-compose -f $COMPOSE_FILE logs -f"
    echo -e "  Stop services: docker-compose -f $COMPOSE_FILE down"
    echo -e "  Restart services: docker-compose -f $COMPOSE_FILE restart"
    echo -e "  Scale ChainOps: docker-compose -f $COMPOSE_FILE up -d --scale chainops=3"
}

# Cleanup function
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    # Add any cleanup logic here
}

# Main deployment function
main() {
    echo -e "${BLUE}Starting staging deployment...${NC}"
    
    # Parse command line arguments
    case "${1:-deploy}" in
        "deploy")
            check_prerequisites
            setup_environment
            build_application
            deploy_services
            run_health_checks
            show_deployment_info
            ;;
        "stop")
            echo -e "${YELLOW}Stopping services...${NC}"
            docker-compose -f "$COMPOSE_FILE" down
            echo -e "${GREEN}✅ Services stopped${NC}"
            ;;
        "restart")
            echo -e "${YELLOW}Restarting services...${NC}"
            docker-compose -f "$COMPOSE_FILE" restart
            echo -e "${GREEN}✅ Services restarted${NC}"
            ;;
        "logs")
            docker-compose -f "$COMPOSE_FILE" logs -f
            ;;
        "status")
            docker-compose -f "$COMPOSE_FILE" ps
            ;;
        "clean")
            echo -e "${YELLOW}Cleaning up deployment...${NC}"
            docker-compose -f "$COMPOSE_FILE" down --volumes --remove-orphans
            docker system prune -f
            echo -e "${GREEN}✅ Cleanup completed${NC}"
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $1${NC}"
            echo -e "${YELLOW}Available commands: deploy, stop, restart, logs, status, clean${NC}"
            exit 1
            ;;
    esac
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
