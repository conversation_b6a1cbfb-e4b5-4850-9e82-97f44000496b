-- ChainOps Database Initialization Script
-- This script sets up the initial database structure and data

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS chainops;
CREATE SCHEMA IF NOT EXISTS audit;

-- Set default schema
SET search_path TO chainops, public;

-- Create enum types
CREATE TYPE pipeline_status AS ENUM (
    'pending',
    'running',
    'success',
    'failed',
    'cancelled',
    'skipped'
);

CREATE TYPE deployment_status AS ENUM (
    'pending',
    'deploying',
    'success',
    'failed',
    'rolled_back'
);

CREATE TYPE trigger_type AS ENUM (
    'manual',
    'webhook',
    'schedule',
    'api'
);

CREATE TYPE user_status AS ENUM (
    'active',
    'inactive',
    'suspended'
);

-- Create audit function
CREATE OR REPLACE FUNCTION audit.audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(NEW),
            current_setting('app.current_user_id', true)::uuid,
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            old_values,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            row_to_json(NEW),
            current_setting('app.current_user_id', true)::uuid,
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            old_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            current_setting('app.current_user_id', true)::uuid,
            NOW()
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit.audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name TEXT NOT NULL,
    operation TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for audit log
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON audit.audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit.audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit.audit_log(user_id);

-- Create default admin user (password: admin123)
-- This will be created by the application migration, but we set up the structure
COMMENT ON SCHEMA chainops IS 'Main ChainOps application schema';
COMMENT ON SCHEMA audit IS 'Audit logging schema for compliance and tracking';

-- Grant permissions
GRANT USAGE ON SCHEMA chainops TO chainops;
GRANT USAGE ON SCHEMA audit TO chainops;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA chainops TO chainops;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA audit TO chainops;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA chainops TO chainops;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA audit TO chainops;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA chainops GRANT ALL ON TABLES TO chainops;
ALTER DEFAULT PRIVILEGES IN SCHEMA audit GRANT ALL ON TABLES TO chainops;
ALTER DEFAULT PRIVILEGES IN SCHEMA chainops GRANT ALL ON SEQUENCES TO chainops;
ALTER DEFAULT PRIVILEGES IN SCHEMA audit GRANT ALL ON SEQUENCES TO chainops;
