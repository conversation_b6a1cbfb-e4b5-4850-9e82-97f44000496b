#!/bin/bash

# ChainOps Production Deployment Script
# This script deploys ChainOps to a Kubernetes cluster

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="chainops"
IMAGE_TAG="${IMAGE_TAG:-latest}"
REGISTRY="${REGISTRY:-chainops}"
KUBECONFIG="${KUBECONFIG:-$HOME/.kube/config}"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command_exists kubectl; then
        missing_deps+=("kubectl")
    fi
    
    if ! command_exists docker; then
        missing_deps+=("docker")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_error "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    # Check kubectl connectivity
    if ! kubectl cluster-info >/dev/null 2>&1; then
        log_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
        exit 1
    fi
    
    log_success "All prerequisites are met"
}

# Build and push Docker image
build_and_push_image() {
    log_info "Building and pushing Docker image..."
    
    local image_name="${REGISTRY}/chainops:${IMAGE_TAG}"
    
    # Build the image
    log_info "Building image: ${image_name}"
    docker build -t "${image_name}" --target production .
    
    # Push the image
    log_info "Pushing image: ${image_name}"
    docker push "${image_name}"
    
    log_success "Image built and pushed: ${image_name}"
}

# Create namespace
create_namespace() {
    log_info "Creating namespace..."
    
    if kubectl get namespace "${NAMESPACE}" >/dev/null 2>&1; then
        log_info "Namespace ${NAMESPACE} already exists"
    else
        kubectl apply -f deployments/k8s/namespace.yaml
        log_success "Namespace ${NAMESPACE} created"
    fi
}

# Deploy PostgreSQL
deploy_postgres() {
    log_info "Deploying PostgreSQL..."
    
    kubectl apply -f deployments/k8s/postgres.yaml
    
    # Wait for PostgreSQL to be ready
    log_info "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgres -n "${NAMESPACE}" --timeout=300s
    
    log_success "PostgreSQL deployed and ready"
}

# Deploy Redis
deploy_redis() {
    log_info "Deploying Redis..."
    
    # Create Redis deployment
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ${NAMESPACE}
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: redis
      app.kubernetes.io/component: cache
  template:
    metadata:
      labels:
        app.kubernetes.io/name: redis
        app.kubernetes.io/component: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: ${NAMESPACE}
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
EOF
    
    log_success "Redis deployed"
}

# Deploy NATS
deploy_nats() {
    log_info "Deploying NATS..."
    
    # Create NATS deployment
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nats
  namespace: ${NAMESPACE}
  labels:
    app.kubernetes.io/name: nats
    app.kubernetes.io/component: messaging
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: nats
      app.kubernetes.io/component: messaging
  template:
    metadata:
      labels:
        app.kubernetes.io/name: nats
        app.kubernetes.io/component: messaging
    spec:
      containers:
      - name: nats
        image: nats:2.10-alpine
        ports:
        - containerPort: 4222
          name: client
        - containerPort: 8222
          name: monitor
        args:
        - "--jetstream"
        - "--http_port=8222"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8222
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8222
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: nats-service
  namespace: ${NAMESPACE}
  labels:
    app.kubernetes.io/name: nats
    app.kubernetes.io/component: messaging
spec:
  type: ClusterIP
  ports:
  - port: 4222
    targetPort: 4222
    protocol: TCP
    name: client
  - port: 8222
    targetPort: 8222
    protocol: TCP
    name: monitor
  selector:
    app.kubernetes.io/name: nats
    app.kubernetes.io/component: messaging
EOF
    
    log_success "NATS deployed"
}

# Deploy ChainOps application
deploy_chainops() {
    log_info "Deploying ChainOps application..."
    
    # Update image tag in deployment
    sed "s|image: chainops:latest|image: ${REGISTRY}/chainops:${IMAGE_TAG}|g" deployments/k8s/chainops.yaml | kubectl apply -f -
    
    # Wait for deployment to be ready
    log_info "Waiting for ChainOps deployment to be ready..."
    kubectl wait --for=condition=available deployment/chainops -n "${NAMESPACE}" --timeout=300s
    
    log_success "ChainOps application deployed and ready"
}

# Create ingress
create_ingress() {
    log_info "Creating ingress..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chainops-ingress
  namespace: ${NAMESPACE}
  labels:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - chainops.yourdomain.com
    secretName: chainops-tls
  rules:
  - host: chainops.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: chainops-service
            port:
              number: 8080
EOF
    
    log_success "Ingress created"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check all pods are running
    kubectl get pods -n "${NAMESPACE}"
    
    # Check services
    kubectl get services -n "${NAMESPACE}"
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    kubectl port-forward -n "${NAMESPACE}" service/chainops-service 8080:8080 &
    local port_forward_pid=$!
    
    sleep 5
    
    if curl -f http://localhost:8080/health >/dev/null 2>&1; then
        log_success "Health check passed"
    else
        log_warning "Health check failed"
    fi
    
    kill $port_forward_pid 2>/dev/null || true
    
    log_success "Deployment verification completed"
}

# Main deployment function
main() {
    log_info "🚀 Starting ChainOps production deployment..."
    
    check_prerequisites
    build_and_push_image
    create_namespace
    deploy_postgres
    deploy_redis
    deploy_nats
    deploy_chainops
    create_ingress
    verify_deployment
    
    log_success "🎉 Production deployment completed successfully!"
    echo ""
    log_info "Next steps:"
    echo "  1. Update DNS to point to your ingress controller"
    echo "  2. Configure SSL certificates"
    echo "  3. Set up monitoring and alerting"
    echo "  4. Configure backup strategies"
    echo ""
    log_info "To check status: kubectl get all -n ${NAMESPACE}"
    log_info "To view logs: kubectl logs -f deployment/chainops -n ${NAMESPACE}"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --image-tag TAG    Docker image tag (default: latest)"
            echo "  --registry REG     Docker registry (default: chainops)"
            echo "  --namespace NS     Kubernetes namespace (default: chainops)"
            echo "  --help            Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
