#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 ChainOps/PipelinerX Test Suite${NC}"
echo -e "${BLUE}=================================${NC}"

# Configuration
TEST_CONFIG=${TEST_CONFIG:-config/test.yaml}
COVERAGE_FILE=${COVERAGE_FILE:-coverage.out}
COVERAGE_HTML=${COVERAGE_HTML:-coverage.html}

# Test environment variables
export CHAINOPS_CONFIG_FILE=$TEST_CONFIG
export CHAINOPS_DATABASE_HOST=localhost
export CHAINOPS_DATABASE_PORT=5432
export CHAINOPS_DATABASE_USER=chainops_test
export CHAINOPS_DATABASE_PASSWORD=test_password
export CHAINOPS_DATABASE_NAME=chainops_test
export CHAINOPS_DATABASE_SSL_MODE=disable
export CHAINOPS_JWT_SECRET=test-jwt-secret-key-for-testing-only
export CHAINOPS_LOG_LEVEL=debug

# Functions
run_unit_tests() {
    echo -e "${YELLOW}📋 Running Unit Tests...${NC}"
    go test -v -race -coverprofile=$COVERAGE_FILE ./internal/... ./pkg/... -timeout=30m
    echo -e "${GREEN}✅ Unit tests completed${NC}"
}

run_integration_tests() {
    echo -e "${YELLOW}🔗 Running Integration Tests...${NC}"
    go test -v -race -tags=integration ./tests/integration/... -timeout=30m
    echo -e "${GREEN}✅ Integration tests completed${NC}"
}

run_api_tests() {
    echo -e "${YELLOW}🌐 Running API Tests...${NC}"
    
    # Start the server in background
    echo -e "${YELLOW}Starting test server...${NC}"
    go run cmd/server/main.go --config=$TEST_CONFIG &
    SERVER_PID=$!
    
    # Wait for server to start
    echo -e "${YELLOW}Waiting for server to start...${NC}"
    sleep 10
    
    # Check if server is running
    if ! curl -s http://localhost:8081/health > /dev/null; then
        echo -e "${RED}❌ Test server failed to start${NC}"
        kill $SERVER_PID 2>/dev/null || true
        exit 1
    fi
    
    echo -e "${GREEN}✅ Test server started${NC}"
    
    # Run API tests
    echo -e "${YELLOW}Running API endpoint tests...${NC}"
    
    # Test health endpoint
    echo -e "${YELLOW}Testing health endpoint...${NC}"
    curl -s http://localhost:8081/health | jq .
    
    # Test metrics endpoint
    echo -e "${YELLOW}Testing metrics endpoint...${NC}"
    curl -s http://localhost:8081/metrics | head -10
    
    # Test pipeline creation (this will fail without auth, but that's expected)
    echo -e "${YELLOW}Testing pipeline creation (should fail without auth)...${NC}"
    curl -s -X POST http://localhost:8081/api/v1/pipelines \
        -H "Content-Type: application/json" \
        -d '{"name":"test","repository":"https://github.com/test/repo"}' | jq .
    
    # Cleanup
    echo -e "${YELLOW}Stopping test server...${NC}"
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
    
    echo -e "${GREEN}✅ API tests completed${NC}"
}

run_performance_tests() {
    echo -e "${YELLOW}⚡ Running Performance Tests...${NC}"
    
    # Start server for performance testing
    go run cmd/server/main.go --config=$TEST_CONFIG &
    SERVER_PID=$!
    sleep 10
    
    # Simple load test using curl
    echo -e "${YELLOW}Running basic load test...${NC}"
    for i in {1..10}; do
        curl -s http://localhost:8081/health > /dev/null &
    done
    wait
    
    # Cleanup
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
    
    echo -e "${GREEN}✅ Performance tests completed${NC}"
}

generate_coverage_report() {
    echo -e "${YELLOW}📊 Generating Coverage Report...${NC}"
    
    if [ -f "$COVERAGE_FILE" ]; then
        # Generate HTML coverage report
        go tool cover -html=$COVERAGE_FILE -o $COVERAGE_HTML
        
        # Show coverage summary
        echo -e "${YELLOW}Coverage Summary:${NC}"
        go tool cover -func=$COVERAGE_FILE | tail -1
        
        echo -e "${GREEN}✅ Coverage report generated: $COVERAGE_HTML${NC}"
    else
        echo -e "${YELLOW}⚠️  No coverage file found${NC}"
    fi
}

run_linting() {
    echo -e "${YELLOW}🔍 Running Code Linting...${NC}"
    
    # Check if golangci-lint is installed
    if command -v golangci-lint &> /dev/null; then
        golangci-lint run ./...
        echo -e "${GREEN}✅ Linting completed${NC}"
    else
        echo -e "${YELLOW}⚠️  golangci-lint not found, skipping linting${NC}"
        echo -e "${YELLOW}Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest${NC}"
    fi
}

run_security_scan() {
    echo -e "${YELLOW}🔒 Running Security Scan...${NC}"
    
    # Check if gosec is installed
    if command -v gosec &> /dev/null; then
        gosec ./...
        echo -e "${GREEN}✅ Security scan completed${NC}"
    else
        echo -e "${YELLOW}⚠️  gosec not found, skipping security scan${NC}"
        echo -e "${YELLOW}Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest${NC}"
    fi
}

cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    
    # Kill any remaining processes
    pkill -f "go run cmd/server/main.go" 2>/dev/null || true
    
    # Clean up test files
    rm -f $COVERAGE_FILE 2>/dev/null || true
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Main execution
main() {
    echo -e "${BLUE}Starting test suite...${NC}"
    
    # Setup test environment
    echo -e "${YELLOW}🔧 Setting up test environment...${NC}"
    ./scripts/setup-test-db.sh
    
    # Parse command line arguments
    case "${1:-all}" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "api")
            run_api_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "lint")
            run_linting
            ;;
        "security")
            run_security_scan
            ;;
        "coverage")
            generate_coverage_report
            ;;
        "all")
            run_linting
            run_security_scan
            run_unit_tests
            run_integration_tests
            run_api_tests
            run_performance_tests
            generate_coverage_report
            ;;
        *)
            echo -e "${RED}❌ Unknown test type: $1${NC}"
            echo -e "${YELLOW}Available options: unit, integration, api, performance, lint, security, coverage, all${NC}"
            exit 1
            ;;
    esac
    
    cleanup
    
    echo -e "${GREEN}🎉 Test suite completed successfully!${NC}"
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
