#!/bin/bash

# ChainOps Demo Script
# This script demonstrates the complete ChainOps CI/CD platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:8080/api/v1"

echo -e "${BLUE}"
cat << "EOF"
   _____ _           _       ____            
  / ____| |         (_)     / __ \           
 | |    | |__   __ _ _ _ __ | |  | |_ __  ___ 
 | |    | '_ \ / _` | | '_ \| |  | | '_ \/ __|
 | |____| | | | (_| | | | | | |__| | |_) \__ \
  \_____|_| |_|\__,_|_|_| |_|\____/| .__/|___/
                                   | |        
                                   |_|        
EOF
echo -e "${NC}"

echo -e "${BLUE}🚀 ChainOps CI/CD Platform Demo${NC}"
echo -e "${BLUE}===============================${NC}"
echo ""
echo -e "${CYAN}Welcome to ChainOps - The Modern CI/CD Platform!${NC}"
echo ""
echo "This demo will show you:"
echo "• ✨ Creating pipelines with YAML configuration"
echo "• 🔄 Executing pipelines with Docker runners"
echo "• 📊 Monitoring execution progress"
echo "• 🎯 Managing pipeline lifecycle"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${CYAN}ℹ${NC} $1"
}

print_step() {
    echo -e "\n${PURPLE}📋 $1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Function to wait for user input
wait_for_user() {
    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
}

# Function to check service health
check_service() {
    local service_name=$1
    local url=$2
    
    if curl -s -f "$url" > /dev/null 2>&1; then
        print_status "$service_name is running"
        return 0
    else
        print_error "$service_name is not running"
        return 1
    fi
}

# Function to create demo pipeline
create_demo_pipeline() {
    local pipeline_name=$1
    local pipeline_yaml=$2
    
    local pipeline_data=$(cat << EOF
{
    "name": "$pipeline_name",
    "repository": "https://github.com/chainops/demo-app.git",
    "branch": "main",
    "config_path": ".chainops.yml",
    "yaml_content": "$pipeline_yaml"
}
EOF
)
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "$pipeline_data" \
        "$API_BASE_URL/pipelines")
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "201" ]; then
        pipeline_id=$(echo "$body" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        echo "$pipeline_id"
        return 0
    else
        echo ""
        return 1
    fi
}

# Function to execute pipeline
execute_pipeline() {
    local pipeline_id=$1
    
    local execution_data='{
        "trigger_type": "manual",
        "variables": {
            "DEMO_MODE": "true",
            "BUILD_NUMBER": "1"
        },
        "branch": "main"
    }'
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "$execution_data" \
        "$API_BASE_URL/pipelines/$pipeline_id/execute")
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "201" ]; then
        execution_id=$(echo "$body" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        echo "$execution_id"
        return 0
    else
        echo ""
        return 1
    fi
}

# Main demo flow
main() {
    print_step "System Health Check"
    
    # Check if services are running
    if ! check_service "ChainOps API" "$API_BASE_URL/health"; then
        print_error "ChainOps API is not running!"
        echo ""
        echo "Please start the system first:"
        echo "1. make dev-up"
        echo "2. make migrate-up"
        echo "3. make dev-server"
        exit 1
    fi
    

    
    wait_for_user
    
    print_step "Creating Demo Pipelines"
    
    # Simple Hello World Pipeline
    print_info "Creating 'Hello World' pipeline..."
    
    hello_world_yaml='pipeline:\n  name: hello-world\n  \n  triggers:\n    - on: [push]\n      branches: [main]\n  \n  stages:\n    - name: greet\n      steps:\n        - name: hello\n          run: echo \"Hello from ChainOps!\"\n        - name: date\n          run: date\n        - name: system-info\n          run: |\n            echo \"Running on: $(uname -a)\"\n            echo \"User: $(whoami)\"\n            echo \"Working directory: $(pwd)\"'
    
    hello_pipeline_id=$(create_demo_pipeline "hello-world-demo" "$hello_world_yaml")
    
    if [ -n "$hello_pipeline_id" ]; then
        print_status "Hello World pipeline created (ID: ${hello_pipeline_id:0:8}...)"
    else
        print_error "Failed to create Hello World pipeline"
    fi
    
    # Node.js Build Pipeline
    print_info "Creating 'Node.js Build' pipeline..."
    
    nodejs_yaml='pipeline:\n  name: nodejs-build\n  \n  variables:\n    NODE_VERSION: \"18\"\n    NPM_CACHE: \"/tmp/npm-cache\"\n  \n  triggers:\n    - on: [push, pull_request]\n      branches: [main, develop]\n  \n  stages:\n    - name: setup\n      steps:\n        - name: node-version\n          run: |\n            echo \"Node.js version: $NODE_VERSION\"\n            echo \"NPM cache: $NPM_CACHE\"\n            \n    - name: build\n      steps:\n        - name: install-deps\n          run: |\n            echo \"Installing dependencies...\"\n            echo \"npm ci --cache $NPM_CACHE\"\n            \n        - name: lint\n          run: |\n            echo \"Running linter...\"\n            echo \"npm run lint\"\n            \n        - name: test\n          run: |\n            echo \"Running tests...\"\n            echo \"npm test\"\n            \n        - name: build\n          run: |\n            echo \"Building application...\"\n            echo \"npm run build\"\n            \n    - name: package\n      condition: branch == \"main\"\n      steps:\n        - name: create-artifact\n          run: |\n            echo \"Creating deployment artifact...\"\n            echo \"tar -czf app.tar.gz dist/\"\n            echo \"Artifact created successfully!\"'
    
    nodejs_pipeline_id=$(create_demo_pipeline "nodejs-build-demo" "$nodejs_yaml")
    
    if [ -n "$nodejs_pipeline_id" ]; then
        print_status "Node.js Build pipeline created (ID: ${nodejs_pipeline_id:0:8}...)"
    else
        print_error "Failed to create Node.js Build pipeline"
    fi
    
    wait_for_user
    
    print_step "Executing Pipelines"
    
    # Execute Hello World pipeline
    if [ -n "$hello_pipeline_id" ]; then
        print_info "Executing Hello World pipeline..."
        hello_execution_id=$(execute_pipeline "$hello_pipeline_id")
        
        if [ -n "$hello_execution_id" ]; then
            print_status "Hello World execution started (ID: ${hello_execution_id:0:8}...)"
        else
            print_error "Failed to execute Hello World pipeline"
        fi
    fi
    
    # Execute Node.js pipeline
    if [ -n "$nodejs_pipeline_id" ]; then
        print_info "Executing Node.js Build pipeline..."
        nodejs_execution_id=$(execute_pipeline "$nodejs_pipeline_id")
        
        if [ -n "$nodejs_execution_id" ]; then
            print_status "Node.js Build execution started (ID: ${nodejs_execution_id:0:8}...)"
        else
            print_error "Failed to execute Node.js Build pipeline"
        fi
    fi
    
    wait_for_user
    
    print_step "Monitoring Executions"
    
    print_info "You can monitor executions in several ways:"
    echo ""
    echo "1. 📡 API Endpoints:"
    echo "   • GET $API_BASE_URL/pipelines"
    echo "   • GET $API_BASE_URL/executions/{id}"
    echo "2. 📊 Metrics: http://localhost:9090 (Prometheus)"
    echo "3. 📈 Dashboards: http://localhost:3001 (Grafana)"
    
    if [ -n "$hello_execution_id" ]; then
        echo ""
        print_info "Checking Hello World execution status..."
        curl -s "$API_BASE_URL/executions/$hello_execution_id" | \
            grep -o '"status":"[^"]*"' | cut -d'"' -f4 | \
            xargs -I {} echo "Status: {}"
    fi
    
    wait_for_user
    
    print_step "Pipeline Management"
    
    print_info "ChainOps provides comprehensive pipeline management:"
    echo ""
    echo "• 📝 YAML-based configuration"
    echo "• 🔄 Multiple trigger types (Git, Manual, Scheduled)"
    echo "• 🐳 Docker and Kubernetes runners"
    echo "• 🔀 Parallel and sequential execution"
    echo "• 📦 Artifact management"
    echo "• 🔐 Secret management"
    echo "• 📊 Real-time monitoring"
    echo "• 🚨 Alerting and notifications"
    
    wait_for_user
    
    print_step "API Examples"
    
    print_info "Here are some useful API commands:"
    echo ""
    echo "# List all pipelines"
    echo "curl $API_BASE_URL/pipelines"
    echo ""
    echo "# Get pipeline details"
    echo "curl $API_BASE_URL/pipelines/{pipeline-id}"
    echo ""
    echo "# Execute pipeline manually"
    echo "curl -X POST $API_BASE_URL/pipelines/{pipeline-id}/execute \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{\"trigger_type\": \"manual\"}'"
    echo ""
    echo "# Get execution status"
    echo "curl $API_BASE_URL/executions/{execution-id}"
    echo ""
    echo "# Get job logs"
    echo "curl $API_BASE_URL/jobs/{job-id}/logs"
    
    wait_for_user
    
    print_step "Cleanup"
    
    print_info "Cleaning up demo pipelines..."
    
    if [ -n "$hello_pipeline_id" ]; then
        curl -s -X DELETE "$API_BASE_URL/pipelines/$hello_pipeline_id" > /dev/null
        print_status "Hello World pipeline deleted"
    fi
    
    if [ -n "$nodejs_pipeline_id" ]; then
        curl -s -X DELETE "$API_BASE_URL/pipelines/$nodejs_pipeline_id" > /dev/null
        print_status "Node.js Build pipeline deleted"
    fi
    
    print_step "Demo Complete!"
    
    echo -e "${GREEN}🎉 Congratulations! You've completed the ChainOps demo!${NC}"
    echo ""
    echo -e "${CYAN}What's next?${NC}"
    echo "• 📖 Read the documentation in ./docs/"
    echo "• 🔧 Create your own pipelines"
    echo "• 🚀 Deploy to production using Docker Compose"
    echo "• ☁️ Deploy to cloud using Kubernetes"
    echo ""
    echo -e "${CYAN}Resources:${NC}"
    echo "• 📚 Deployment Guide: ./DEPLOYMENT_GUIDE.md"
    echo "• 🔧 Technical Blueprint: ./TECHNICAL_BLUEPRINT.md"
    echo "• 🧪 Test System: ./scripts/test-system.sh"
    echo "• 🐳 Docker Deployment: docker-compose up -d"
    echo ""
    echo -e "${BLUE}Thank you for trying ChainOps! 🚀${NC}"
}

# Run the demo
main "$@"
