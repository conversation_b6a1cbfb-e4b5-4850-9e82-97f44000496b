#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up ChainOps Test Database...${NC}"

# Configuration
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD:-postgres}
TEST_DB_NAME=${TEST_DB_NAME:-chainops_test}
TEST_DB_USER=${TEST_DB_USER:-chainops_test}
TEST_DB_PASSWORD=${TEST_DB_PASSWORD:-test_password}

# Check if PostgreSQL is running
echo -e "${YELLOW}Checking PostgreSQL connection...${NC}"
if ! pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER; then
    echo -e "${RED}PostgreSQL is not running or not accessible${NC}"
    echo -e "${YELLOW}Starting PostgreSQL with Docker...${NC}"
    
    docker run -d \
        --name chainops-test-db \
        -e POSTGRES_USER=$DB_USER \
        -e POSTGRES_PASSWORD=$DB_PASSWORD \
        -e POSTGRES_DB=postgres \
        -p $DB_PORT:5432 \
        postgres:15
    
    echo -e "${YELLOW}Waiting for PostgreSQL to start...${NC}"
    sleep 10
fi

# Create test database and user
echo -e "${YELLOW}Creating test database and user...${NC}"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres << EOF
-- Drop existing test database and user if they exist
DROP DATABASE IF EXISTS $TEST_DB_NAME;
DROP USER IF EXISTS $TEST_DB_USER;

-- Create test user
CREATE USER $TEST_DB_USER WITH PASSWORD '$TEST_DB_PASSWORD';

-- Create test database
CREATE DATABASE $TEST_DB_NAME OWNER $TEST_DB_USER;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE $TEST_DB_NAME TO $TEST_DB_USER;
EOF

echo -e "${GREEN}Test database setup completed!${NC}"

# Run migrations
echo -e "${YELLOW}Running database migrations...${NC}"
export CHAINOPS_DATABASE_HOST=$DB_HOST
export CHAINOPS_DATABASE_PORT=$DB_PORT
export CHAINOPS_DATABASE_USER=$TEST_DB_USER
export CHAINOPS_DATABASE_PASSWORD=$TEST_DB_PASSWORD
export CHAINOPS_DATABASE_NAME=$TEST_DB_NAME
export CHAINOPS_DATABASE_SSL_MODE=disable

if [ -f "cmd/migrate/main.go" ]; then
    go run cmd/migrate/main.go up
else
    echo -e "${YELLOW}Migration tool not found, skipping migrations${NC}"
fi

echo -e "${GREEN}Database setup and migrations completed!${NC}"

# Setup Redis for testing
echo -e "${YELLOW}Setting up Redis for testing...${NC}"
if ! redis-cli -h localhost -p 6379 ping > /dev/null 2>&1; then
    echo -e "${YELLOW}Starting Redis with Docker...${NC}"
    docker run -d \
        --name chainops-test-redis \
        -p 6379:6379 \
        redis:7-alpine
    
    echo -e "${YELLOW}Waiting for Redis to start...${NC}"
    sleep 5
fi

echo -e "${GREEN}Redis setup completed!${NC}"

# Create test directories
echo -e "${YELLOW}Creating test directories...${NC}"
mkdir -p /tmp/chainops-test/{workspaces,artifacts,logs,storage}

echo -e "${GREEN}Test environment setup completed successfully!${NC}"
echo -e "${YELLOW}Test Database: $TEST_DB_NAME${NC}"
echo -e "${YELLOW}Test User: $TEST_DB_USER${NC}"
echo -e "${YELLOW}Connection: postgresql://$TEST_DB_USER:$TEST_DB_PASSWORD@$DB_HOST:$DB_PORT/$TEST_DB_NAME?sslmode=disable${NC}"
