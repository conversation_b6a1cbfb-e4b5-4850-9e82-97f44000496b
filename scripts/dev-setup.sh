#!/bin/bash

# ChainOps Development Setup Script
# This script sets up the development environment for ChainOps

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command_exists docker; then
        missing_deps+=("docker")
    fi
    
    if ! command_exists docker-compose; then
        missing_deps+=("docker-compose")
    fi
    
    if ! command_exists go; then
        missing_deps+=("go")
    fi
    
    if ! command_exists git; then
        missing_deps+=("git")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_error "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

# Setup environment files
setup_environment() {
    log_info "Setting up environment files..."
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        cat > .env << EOF
# ChainOps Development Environment
CHAINOPS_ENV=development
CHAINOPS_LOG_LEVEL=debug

# Database
CHAINOPS_DATABASE_HOST=localhost
CHAINOPS_DATABASE_PORT=5432
CHAINOPS_DATABASE_USER=chainops
CHAINOPS_DATABASE_PASSWORD=chainops_dev_password
CHAINOPS_DATABASE_NAME=chainops

# Cache
CHAINOPS_CACHE_HOST=localhost
CHAINOPS_CACHE_PORT=6379

# Message Queue
CHAINOPS_MESSAGE_QUEUE_URL=nats://localhost:4222

# Object Storage
CHAINOPS_OBJECT_STORAGE_ENDPOINT=localhost:9000
CHAINOPS_OBJECT_STORAGE_ACCESS_KEY_ID=chainops
CHAINOPS_OBJECT_STORAGE_SECRET_ACCESS_KEY=chainops_minio_password

# Security
CHAINOPS_JWT_SECRET=dev-jwt-secret-key-change-in-production
CHAINOPS_ENCRYPTION_KEY=dev-encryption-key-32-chars-long

# Runner
CHAINOPS_RUNNER_TYPE=docker
CHAINOPS_RUNNER_DOCKER_HOST=unix:///var/run/docker.sock

# Monitoring
CHAINOPS_METRICS_ENABLED=true
CHAINOPS_METRICS_ENDPOINT=:8081
EOF
        log_success "Created .env file"
    else
        log_info ".env file already exists"
    fi
}

# Start infrastructure services
start_infrastructure() {
    log_info "Starting infrastructure services..."
    
    # Start PostgreSQL, Redis, NATS, and MinIO
    docker-compose up -d postgres redis nats minio
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    
    # Wait for PostgreSQL
    log_info "Waiting for PostgreSQL..."
    until docker-compose exec -T postgres pg_isready -U chainops -d chainops; do
        sleep 2
    done
    log_success "PostgreSQL is ready"
    
    # Wait for Redis
    log_info "Waiting for Redis..."
    until docker-compose exec -T redis redis-cli ping; do
        sleep 2
    done
    log_success "Redis is ready"
    
    # Wait for MinIO
    log_info "Waiting for MinIO..."
    until curl -f http://localhost:9000/minio/health/live >/dev/null 2>&1; do
        sleep 2
    done
    log_success "MinIO is ready"
    
    log_success "All infrastructure services are ready"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Build migration tool
    go build -o bin/migrate cmd/migrate/main.go
    
    # Run migrations
    ./bin/migrate up
    
    log_success "Database migrations completed"
}

# Build application
build_application() {
    log_info "Building ChainOps application..."
    
    # Create bin directory
    mkdir -p bin
    
    # Build main application
    go build -o bin/chainops cmd/server/main.go
    
    log_success "Application built successfully"
}

# Setup development tools
setup_dev_tools() {
    log_info "Setting up development tools..."
    
    # Install air for hot reloading if not present
    if ! command_exists air; then
        log_info "Installing air for hot reloading..."
        go install github.com/cosmtrek/air@latest
        log_success "Air installed"
    else
        log_info "Air already installed"
    fi
    
    # Install golangci-lint if not present
    if ! command_exists golangci-lint; then
        log_info "Installing golangci-lint..."
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2
        log_success "golangci-lint installed"
    else
        log_info "golangci-lint already installed"
    fi
}

# Create development database
create_dev_database() {
    log_info "Creating development database..."
    
    # Create database if it doesn't exist
    docker-compose exec -T postgres psql -U chainops -d postgres -c "CREATE DATABASE chainops_dev;" 2>/dev/null || true
    
    log_success "Development database ready"
}

# Main setup function
main() {
    log_info "🚀 Starting ChainOps development setup..."
    
    check_prerequisites
    setup_environment
    start_infrastructure
    run_migrations
    build_application
    setup_dev_tools
    create_dev_database
    
    log_success "🎉 Development setup completed successfully!"
    echo ""
    log_info "Next steps:"
    echo "  1. Run 'air' to start the application with hot reloading"
    echo "  2. Visit http://localhost:8080/health to check the application"
    echo "  3. Visit http://localhost:3001 for Grafana dashboard (admin/admin)"
    echo "  4. Visit http://localhost:9090 for Prometheus metrics"
    echo "  5. Visit http://localhost:9001 for MinIO console (chainops/chainops_minio_password)"
    echo ""
    log_info "To stop all services: docker-compose down"
    log_info "To view logs: docker-compose logs -f"
}

# Run main function
main "$@"
