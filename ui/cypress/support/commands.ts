/// <reference types="cypress" />

// Custom commands for ChainOps testing

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to navigate to a specific page
       * @example cy.navigateTo('pipelines')
       */
      navigateTo(page: string): Chainable<Element>
      
      /**
       * Custom command to wait for page to load
       * @example cy.waitForPageLoad()
       */
      waitForPageLoad(): Chainable<Element>
      
      /**
       * Custom command to mock API responses
       * @example cy.mockApiResponse('GET', '/api/v1/pipelines', { data: [] })
       */
      mockApiResponse(method: string, url: string, response: any): Chainable<Element>
    }
  }
}

Cypress.Commands.add('navigateTo', (page: string) => {
  cy.get(`[href="/${page}"]`).click();
  cy.url().should('include', `/${page}`);
});

Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('[data-testid="loading"]', { timeout: 1000 }).should('not.exist');
  cy.get('body').should('be.visible');
});

Cypress.Commands.add('mockApiResponse', (method: string, url: string, response: any) => {
  cy.intercept(method, url, response).as('apiCall');
});

// Prevent TypeScript errors
export {};
