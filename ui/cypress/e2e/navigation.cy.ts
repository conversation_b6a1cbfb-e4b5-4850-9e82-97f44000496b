describe('Navigation E2E Tests', () => {
  beforeEach(() => {
    // Mock API responses
    cy.intercept('GET', '/api/v1/**', {
      statusCode: 200,
      body: { success: true, data: [], pagination: { total: 0 } }
    }).as('apiCalls');

    cy.visit('/');
  });

  it('should navigate to all main pages', () => {
    // Test Dashboard
    cy.contains('Dashboard').scrollIntoView().click({ force: true });
    cy.url().should('eq', Cypress.config().baseUrl + '/');
    cy.contains('Welcome to ChainOps').should('be.visible');

    // Test Pipelines
    cy.contains('Pipelines').scrollIntoView().click({ force: true });
    cy.url().should('include', '/pipelines');
    cy.contains('Pipelines').should('be.visible');

    // Test Executions
    cy.contains('Executions').scrollIntoView().click({ force: true });
    cy.url().should('include', '/executions');
    cy.contains('Executions').should('be.visible');

    // Test Deployments
    cy.contains('Deployments').scrollIntoView().click({ force: true });
    cy.url().should('include', '/deployments');
    cy.contains('Deployments').should('be.visible');

    // Test Secrets
    cy.contains('Secrets').scrollIntoView().click({ force: true });
    cy.url().should('include', '/secrets');
    cy.contains('Secrets').should('be.visible');

    // Test Monitoring
    cy.contains('Monitoring').scrollIntoView().click({ force: true });
    cy.url().should('include', '/monitoring');
    cy.contains('Monitoring').should('be.visible');

    // Test Analytics (if available in navigation)
    cy.get('body').then(($body) => {
      if ($body.text().includes('Analytics')) {
        cy.contains('Analytics').scrollIntoView().click({ force: true });
        cy.url().should('include', '/analytics');
      }
    });
  });

  it('should navigate to placeholder pages', () => {
    // Test Users/User Management
    cy.visit('/users');
    cy.contains('User Management').should('be.visible');
    cy.contains('Coming soon...').should('be.visible');

    // Test Notifications
    cy.visit('/notifications');
    cy.contains('Notifications').should('be.visible');
    cy.contains('Coming soon...').should('be.visible');

    // Test Integrations
    cy.visit('/integrations');
    cy.contains('Integrations').should('be.visible');
    cy.contains('Coming soon...').should('be.visible');

    // Test Settings
    cy.visit('/settings');
    cy.contains('Settings').should('be.visible');
    cy.contains('Coming soon...').should('be.visible');
  });

  it('should highlight active navigation item', () => {
    // Check Dashboard is active by default
    cy.get('[href="/"]').should('have.class', 'bg-blue-100');

    // Navigate to Pipelines and check it becomes active
    cy.contains('Pipelines').scrollIntoView().click({ force: true });
    cy.get('[href="/pipelines"]').should('have.class', 'bg-blue-100');

    // Navigate to Executions and check it becomes active
    cy.contains('Executions').scrollIntoView().click({ force: true });
    cy.get('[href="/executions"]').should('have.class', 'bg-blue-100');
  });

  it('should display correct page titles', () => {
    // Dashboard
    cy.visit('/');
    cy.contains('Dashboard').should('be.visible');

    // Pipelines
    cy.visit('/pipelines');
    cy.contains('Pipelines').should('be.visible');

    // Executions
    cy.visit('/executions');
    cy.contains('Executions').should('be.visible');

    // Create Pipeline
    cy.visit('/pipelines/new');
    cy.contains('Create Pipeline').should('be.visible');
  });

  it('should handle browser back/forward navigation', () => {
    // Navigate to pipelines
    cy.contains('Pipelines').scrollIntoView().click({ force: true });
    cy.url().should('include', '/pipelines');

    // Navigate to executions
    cy.contains('Executions').scrollIntoView().click({ force: true });
    cy.url().should('include', '/executions');

    // Use browser back button
    cy.go('back');
    cy.url().should('include', '/pipelines');

    // Use browser forward button
    cy.go('forward');
    cy.url().should('include', '/executions');

    // Go back to dashboard
    cy.go('back');
    cy.go('back');
    cy.url().should('eq', Cypress.config().baseUrl + '/');
  });

  it('should maintain navigation state on page refresh', () => {
    // Navigate to pipelines
    cy.contains('Pipelines').scrollIntoView().click({ force: true });
    cy.url().should('include', '/pipelines');

    // Refresh page
    cy.reload();

    // Should still be on pipelines page
    cy.url().should('include', '/pipelines');
    cy.get('[href="/pipelines"]').should('have.class', 'bg-blue-100');
  });

  it('should handle direct URL navigation', () => {
    // Test direct navigation to various pages
    const pages = [
      '/pipelines',
      '/executions',
      '/deployments',
      '/secrets',
      '/monitoring',
      '/users',
      '/notifications',
      '/integrations',
      '/settings'
    ];

    pages.forEach(page => {
      cy.visit(page);
      cy.url().should('include', page);
      
      // Should show some content (not a 404)
      cy.get('body').should('not.contain', '404');
      cy.get('body').should('not.contain', 'Page not found');
    });
  });

  it('should display user profile in sidebar', () => {
    cy.contains('Admin User').should('be.visible');
    cy.contains('<EMAIL>').should('be.visible');
  });

  it('should have proper sidebar layout', () => {
    // Check sidebar structure
    cy.get('.w-64').should('exist'); // Sidebar width class
    cy.contains('ChainOps').should('be.visible'); // Header
    
    // Check navigation items are in proper order
    const navItems = ['Dashboard', 'Pipelines', 'Executions', 'Deployments', 'Secrets', 'Monitoring'];
    navItems.forEach((item, index) => {
      cy.contains(item).should('be.visible');
    });
  });

  it('should handle nested routes', () => {
    // Test pipeline detail route
    cy.visit('/pipelines/123');
    cy.contains('Pipeline Details').should('be.visible');

    // Test execution detail route
    cy.visit('/executions/456');
    cy.contains('Execution Details').should('be.visible');

    // Test create pipeline route
    cy.visit('/pipelines/new');
    cy.contains('Create Pipeline').should('be.visible');
  });
});
