describe('Dashboard E2E Tests', () => {
  beforeEach(() => {
    // Mock API responses to prevent real API calls
    cy.intercept('GET', '/api/v1/pipelines*', {
      statusCode: 200,
      body: {
        success: true,
        data: [],
        pagination: { total: 5, page: 1, per_page: 20, total_pages: 1 }
      }
    }).as('getPipelines');

    cy.visit('/');
  });

  it('should load the dashboard successfully', () => {
    // Check that the main elements are visible
    cy.contains('ChainOps').should('be.visible');
    cy.contains('Welcome to ChainOps').should('be.visible');
    cy.contains('Manage your CI/CD pipelines with ease').should('be.visible');
  });

  it('should display all navigation items', () => {
    // Check sidebar navigation
    cy.contains('Dashboard').should('be.visible');
    cy.contains('Pipelines').should('be.visible');
    cy.contains('Executions').should('be.visible');
    cy.contains('Deployments').should('be.visible');
    cy.contains('Secrets').should('be.visible');
    cy.contains('Monitoring').should('be.visible');
    cy.contains('Notifications').should('be.visible');
    cy.contains('Infrastructure').should('be.visible');
    cy.contains('Settings').should('be.visible');
  });

  it('should display metrics cards', () => {
    // Wait for dashboard to load
    cy.contains('Total Pipelines').should('be.visible');
    cy.contains('Total Executions').should('be.visible');
    cy.contains('Running').should('be.visible');
    cy.contains('Success Rate').should('be.visible');
    cy.contains('Total Deployments').should('be.visible');
    cy.contains('Active Deployments').should('be.visible');
    cy.contains('Secrets').should('be.visible');
    cy.contains('System Health').should('be.visible');
  });

  it('should display system metrics', () => {
    cy.contains('CPU Usage').should('be.visible');
    cy.contains('Memory Usage').should('be.visible');
    cy.contains('Active Alerts').should('be.visible');
    
    // Check for progress bars
    cy.get('[style*="width: 65%"]').should('exist'); // CPU usage bar
    cy.get('[style*="width: 78%"]').should('exist'); // Memory usage bar
  });

  it('should display recent executions', () => {
    cy.contains('Recent Executions').should('be.visible');
    cy.contains('Frontend Build & Deploy').should('be.visible');
    cy.contains('Backend API Tests').should('be.visible');
    cy.contains('Security Scan').should('be.visible');
    cy.contains('Local Testing Demo').should('be.visible');
  });

  it('should display active alerts', () => {
    cy.contains('Active Alerts').should('be.visible');
    cy.contains('High CPU Usage').should('be.visible');
    cy.contains('Pipeline Failure Rate Increased').should('be.visible');
  });

  it('should display quick actions', () => {
    cy.contains('Quick Actions').should('be.visible');
    cy.contains('Create Pipeline').should('be.visible');
    cy.contains('View Pipelines').should('be.visible');
    cy.contains('View Executions').should('be.visible');
    cy.contains('Deployments').should('be.visible');
    cy.contains('Secrets').should('be.visible');
  });

  it('should have working navigation links', () => {
    // Test navigation to pipelines
    cy.contains('Pipelines').click();
    cy.url().should('include', '/pipelines');
    cy.contains('Pipelines').should('be.visible');

    // Navigate back to dashboard
    cy.contains('Dashboard').click();
    cy.url().should('eq', Cypress.config().baseUrl + '/');
    cy.contains('Welcome to ChainOps').should('be.visible');
  });

  it('should have working quick action links', () => {
    // Test Create Pipeline link
    cy.contains('Create Pipeline').first().click();
    cy.url().should('include', '/pipelines/new');

    // Navigate back
    cy.go('back');
    cy.url().should('eq', Cypress.config().baseUrl + '/');
  });

  it('should display execution status badges correctly', () => {
    // Check for status badges
    cy.contains('success').should('be.visible');
    cy.contains('running').should('be.visible');
    
    // Check for execution durations
    cy.contains('180s').should('be.visible');
    cy.contains('95s').should('be.visible');
    cy.contains('12s').should('be.visible');
  });

  it('should be responsive', () => {
    // Test mobile viewport
    cy.viewport(375, 667);
    cy.contains('ChainOps').should('be.visible');
    cy.contains('Welcome to ChainOps').should('be.visible');

    // Test tablet viewport
    cy.viewport(768, 1024);
    cy.contains('ChainOps').should('be.visible');
    cy.contains('Welcome to ChainOps').should('be.visible');

    // Test desktop viewport
    cy.viewport(1280, 720);
    cy.contains('ChainOps').should('be.visible');
    cy.contains('Welcome to ChainOps').should('be.visible');
  });

  it('should handle loading states gracefully', () => {
    // Intercept with delay to test loading state
    cy.intercept('GET', '/api/v1/pipelines*', {
      delay: 1000,
      statusCode: 200,
      body: {
        success: true,
        data: [],
        pagination: { total: 5, page: 1, per_page: 20, total_pages: 1 }
      }
    }).as('getSlowPipelines');

    cy.visit('/');
    
    // Should show loading state briefly
    cy.get('.animate-spin').should('exist');
    
    // Should eventually show content
    cy.contains('Welcome to ChainOps', { timeout: 2000 }).should('be.visible');
  });
});
