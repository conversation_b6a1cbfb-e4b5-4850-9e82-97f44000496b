describe('New ChainOps Interface', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should load the new dashboard with ChainOps branding', () => {
    cy.contains('Welcome to ChainOps').should('be.visible');
    cy.contains('Your enterprise CI/CD platform dashboard').should('be.visible');
  });

  it('should have the new sidebar navigation', () => {
    cy.get('[data-testid="sidebar"]').should('be.visible');
    cy.contains('Home').should('be.visible');
    cy.contains('Pipelines').should('be.visible');
    cy.contains('Projects').should('be.visible');
    cy.contains('Deploys').should('be.visible');
    cy.contains('Insights').should('be.visible');
    cy.contains('Runners').should('be.visible');
    cy.contains('Settings').should('be.visible');
    cy.contains('Plan').should('be.visible');
  });

  it('should have the top navbar with search and dark mode toggle', () => {
    cy.get('input[placeholder*="Search pipelines"]').should('be.visible');
    cy.get('button[title*="dark mode"]').should('be.visible');
  });

  it('should navigate to pipelines page with "All Pipelines" title', () => {
    cy.contains('Pipelines').click();
    cy.contains('All Pipelines').should('be.visible');
    cy.contains('Monitor and manage your CI/CD pipeline runs').should('be.visible');
  });

  it('should show pipeline filters', () => {
    cy.contains('Pipelines').click();
    cy.contains('User').should('be.visible');
    cy.contains('Project Name').should('be.visible');
    cy.contains('Branch').should('be.visible');
  });

  it('should display pipeline data with status colors', () => {
    cy.contains('Pipelines').click();
    // Check for status indicators
    cy.get('.status-running, .status-success, .status-failed, .status-needs_approval, .status-queued')
      .should('have.length.greaterThan', 0);
  });

  it('should have dark mode toggle functionality', () => {
    cy.get('button[title*="dark mode"]').click();
    cy.get('html').should('have.class', 'dark');
  });

  it('should show stats cards on dashboard', () => {
    cy.contains('Total Pipelines').should('be.visible');
    cy.contains('Success Rate').should('be.visible');
    cy.contains('Active Runners').should('be.visible');
    cy.contains('Avg Duration').should('be.visible');
  });

  it('should have working quick actions', () => {
    cy.contains('Quick Actions').should('be.visible');
    cy.contains('Create Pipeline').should('be.visible');
    cy.contains('Manage Projects').should('be.visible');
    cy.contains('View Runners').should('be.visible');
  });

  it('should show system status', () => {
    cy.contains('System Status').should('be.visible');
    cy.contains('Pipeline Engine').should('be.visible');
    cy.contains('Runner Pool').should('be.visible');
    cy.contains('Deployment Service').should('be.visible');
  });
});
