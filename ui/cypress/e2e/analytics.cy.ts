describe('Analytics Page E2E Tests', () => {
  beforeEach(() => {
    // Mock API responses
    cy.intercept('GET', '/api/v1/**', {
      statusCode: 200,
      body: { success: true, data: [] }
    }).as('apiCalls');

    cy.visit('/analytics');
  });

  it('should load the analytics page successfully', () => {
    cy.contains('Analytics').should('be.visible');
    cy.url().should('include', '/analytics');
  });

  it('should display filter controls', () => {
    // Check for time range filter
    cy.get('select').should('contain.value', '7d');
    
    // Check for pipeline filter
    cy.contains('Select Pipeline').should('be.visible');
    
    // Check for environment filter
    cy.contains('Select Environment').should('be.visible');
    
    // Check for refresh button
    cy.contains('Refresh').should('be.visible');
  });

  it('should display metric cards', () => {
    cy.contains('Total Executions').should('be.visible');
    cy.contains('Success Rate').should('be.visible');
    cy.contains('Avg Duration').should('be.visible');
    cy.contains('Cost Estimate').should('be.visible');
  });

  it('should display charts', () => {
    cy.contains('Execution Trend').should('be.visible');
    cy.contains('Success Rate Trend').should('be.visible');
    cy.contains('Executions by Status').should('be.visible');
    cy.contains('Daily Resource Usage').should('be.visible');
  });

  it('should display failure analysis', () => {
    cy.contains('Top Failure Reasons').should('be.visible');
    cy.contains('Slowest Jobs').should('be.visible');
    
    // Check for mock failure reasons
    cy.contains('Test failures').should('be.visible');
    cy.contains('Build timeout').should('be.visible');
    cy.contains('Dependency issues').should('be.visible');
  });

  it('should display deployment metrics', () => {
    cy.contains('Deployment Metrics').should('be.visible');
    cy.contains('Success Rate').should('be.visible');
    cy.contains('MTTR').should('be.visible');
    cy.contains('Change Failure Rate').should('be.visible');
  });

  it('should allow filter interactions', () => {
    // Test time range filter
    cy.get('select').first().select('30d');
    cy.get('select').first().should('have.value', '30d');

    // Test refresh button
    cy.contains('Refresh').click();
    
    // Should show loading state briefly
    cy.get('.animate-spin').should('exist');
  });

  it('should display formatted metrics correctly', () => {
    // Check for formatted numbers
    cy.contains('1,247').should('be.visible'); // Total executions
    cy.contains('95.2%').should('be.visible'); // Success rate
    cy.contains('$1,247.50').should('be.visible'); // Cost estimate
  });

  it('should display trend indicators', () => {
    cy.contains('Trending up').should('be.visible');
  });

  it('should handle chart interactions', () => {
    // Charts should be rendered (we can't test actual chart interactions easily in E2E)
    // But we can verify the chart containers exist
    cy.get('[data-testid="responsive-container"]').should('have.length.at.least', 1);
  });

  it('should be responsive on different screen sizes', () => {
    // Test mobile viewport
    cy.viewport(375, 667);
    cy.contains('Analytics').should('be.visible');
    cy.contains('Total Executions').should('be.visible');

    // Test tablet viewport
    cy.viewport(768, 1024);
    cy.contains('Analytics').should('be.visible');
    cy.contains('Execution Trend').should('be.visible');

    // Test desktop viewport
    cy.viewport(1280, 720);
    cy.contains('Analytics').should('be.visible');
    cy.contains('Daily Resource Usage').should('be.visible');
  });

  it('should handle loading and error states', () => {
    // Test with delayed response
    cy.intercept('GET', '/api/v1/analytics/**', {
      delay: 1000,
      statusCode: 200,
      body: { success: true, data: {} }
    }).as('slowAnalytics');

    cy.visit('/analytics');
    
    // Should show loading state
    cy.get('.animate-spin').should('exist');
    
    // Should eventually show content
    cy.contains('Total Executions', { timeout: 2000 }).should('be.visible');
  });

  it('should display job performance data', () => {
    cy.contains('Integration Tests').should('be.visible');
    cy.contains('Security Scan').should('be.visible');
    cy.contains('Build Frontend').should('be.visible');
    cy.contains('Deploy Production').should('be.visible');
  });

  it('should show duration formatting', () => {
    // Check for various duration formats
    cy.contains('7m').should('be.visible');
    cy.contains('6m 20s').should('be.visible');
    cy.contains('4m').should('be.visible');
    cy.contains('3m').should('be.visible');
  });

  it('should display resource usage metrics', () => {
    cy.contains('CPU Hours').should('be.visible');
    cy.contains('Memory GB Hours').should('be.visible');
  });

  it('should handle filter combinations', () => {
    // Test multiple filter selections
    cy.get('select').first().select('30d');
    
    // Select environment filter
    cy.get('select').eq(2).select('production');
    
    // Click refresh
    cy.contains('Refresh').click();
    
    // Should maintain filter values
    cy.get('select').first().should('have.value', '30d');
    cy.get('select').eq(2).should('have.value', 'production');
  });
});
