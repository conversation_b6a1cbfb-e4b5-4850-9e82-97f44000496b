{"name": "chainops-ui", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "cypress open"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@monaco-editor/react": "^4.6.0", "axios": "^1.6.2", "clsx": "^2.1.1", "date-fns": "^2.30.0", "monaco-editor": "^0.44.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-flow": "^1.0.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.30.1", "reactflow": "^11.11.4", "recharts": "^2.15.3", "socket.io-client": "^4.7.4", "zustand": "^4.4.7"}, "devDependencies": {"@cypress/react18": "^2.0.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "cypress": "^13.6.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.8.3", "vite": "^5.0.0", "vitest": "^1.0.0"}}