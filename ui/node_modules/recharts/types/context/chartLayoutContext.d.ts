import React, { ReactNode } from 'react';
import { CartesianViewBox, ChartOffset, XAxisMap, YAxisMap } from '../util/types';
import type { CategoricalChartState } from '../chart/types';
import type { Props as XAxisProps } from '../cartesian/XAxis';
import type { Props as YAxisProps } from '../cartesian/YAxis';
export declare const XAxisContext: React.Context<XAxisMap>;
export declare const YAxisContext: React.Context<YAxisMap>;
export declare const ViewBoxContext: React.Context<CartesianViewBox>;
export declare const OffsetContext: React.Context<ChartOffset>;
export declare const ClipPathIdContext: React.Context<string>;
export declare const ChartHeightContext: React.Context<number>;
export declare const ChartWidthContext: React.Context<number>;
/**
 * Will add all the properties required to render all individual Recharts components into a React Context.
 *
 * If you want to read these properties, see the collection of hooks exported from this file.
 *
 * @param {object} props CategoricalChartState, plus children
 * @returns {ReactElement} React Context Provider
 */
export declare const ChartLayoutContextProvider: (props: {
    state: CategoricalChartState;
    children: ReactNode;
    clipPathId: string;
    width: number;
    height: number;
}) => React.JSX.Element;
export declare const useClipPathId: () => string | undefined;
/**
 * This either finds and returns Axis by the specified ID, or throws an exception if an axis with this ID does not exist.
 *
 * @param xAxisId identifier of the axis - it's either autogenerated ('0'), or passed via `id` prop as <XAxis id='foo' />
 * @returns axis configuration object
 * @throws Error if no axis with this ID exists
 */
export declare const useXAxisOrThrow: (xAxisId: string | number) => XAxisProps;
/**
 * This will find an arbitrary first XAxis. If there's exactly one it always returns that one
 * - but if there are multiple then it can return any of those.
 *
 * If you want specific XAxis out of multiple then prefer using useXAxisOrThrow
 *
 * @returns X axisOptions, or undefined - if there are no X axes
 */
export declare const useArbitraryXAxis: () => XAxisProps | undefined;
/**
 * This will find an arbitrary first YAxis. If there's exactly one it always returns that one
 * - but if there are multiple then it can return any of those.
 *
 * If you want specific YAxis out of multiple then prefer using useXAxisOrThrow
 *
 * @returns Y axisOptions, or undefined - if there are no Y axes
 */
export declare const useArbitraryYAxis: () => XAxisProps | undefined;
/**
 * This hooks will:
 * 1st attempt to find an YAxis that has all elements in its domain finite
 * If no such axis exists, it will return an arbitrary YAxis
 * if there are no Y axes then it returns undefined
 *
 * @returns Either Y axisOptions, or undefined if there are no Y axes
 */
export declare const useYAxisWithFiniteDomainOrRandom: () => YAxisProps | undefined;
/**
 * This either finds and returns Axis by the specified ID, or throws an exception if an axis with this ID does not exist.
 *
 * @param yAxisId identifier of the axis - it's either autogenerated ('0'), or passed via `id` prop as <YAxis id='foo' />
 * @returns axis configuration object
 * @throws Error if no axis with this ID exists
 */
export declare const useYAxisOrThrow: (yAxisId: string | number) => YAxisProps;
export declare const useViewBox: () => CartesianViewBox;
export declare const useOffset: () => ChartOffset;
export declare const useChartWidth: () => number;
export declare const useChartHeight: () => number;
