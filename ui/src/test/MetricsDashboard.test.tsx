import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import MetricsDashboard from '../components/MetricsDashboard';

// Mock recharts components
vi.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  AreaChart: ({ children }: any) => <div data-testid="area-chart">{children}</div>,
  Area: () => <div data-testid="area" />,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
}));

// Mock the API module
const mockApi = {
  get: vi.fn(),
};

vi.mock('../services/api', () => ({
  api: mockApi,
}));

describe('MetricsDashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { container } = render(<MetricsDashboard />);
    expect(container).toBeInTheDocument();
  });

  it('renders dashboard content after loading', async () => {
    render(<MetricsDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Total Executions')).toBeInTheDocument();
    }, { timeout: 3000 });

    // Check for metric cards
    expect(screen.getByText('Success Rate')).toBeInTheDocument();
    expect(screen.getByText('Avg Duration')).toBeInTheDocument();
    expect(screen.getByText('Cost Estimate')).toBeInTheDocument();
  });

  it('displays filter controls', async () => {
    render(<MetricsDashboard />);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('7d')).toBeInTheDocument();
    });

    // Check for filter dropdowns
    expect(screen.getByText('Select Pipeline')).toBeInTheDocument();
    expect(screen.getByText('Select Environment')).toBeInTheDocument();
    expect(screen.getByText('Refresh')).toBeInTheDocument();
  });

  it('displays charts', async () => {
    render(<MetricsDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Execution Trend')).toBeInTheDocument();
    });

    // Check for chart sections
    expect(screen.getByText('Success Rate Trend')).toBeInTheDocument();
    expect(screen.getByText('Executions by Status')).toBeInTheDocument();
    expect(screen.getByText('Daily Resource Usage')).toBeInTheDocument();
    
    // Check for chart components
    expect(screen.getAllByTestId('responsive-container')).toHaveLength(4);
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    expect(screen.getByTestId('area-chart')).toBeInTheDocument();
    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
  });

  it('displays failure reasons and slowest jobs', async () => {
    render(<MetricsDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Top Failure Reasons')).toBeInTheDocument();
    });

    expect(screen.getByText('Slowest Jobs')).toBeInTheDocument();
    
    // Check for mock data
    expect(screen.getByText('Test failures')).toBeInTheDocument();
    expect(screen.getByText('Build timeout')).toBeInTheDocument();
    expect(screen.getByText('Integration Tests')).toBeInTheDocument();
    expect(screen.getByText('Security Scan')).toBeInTheDocument();
  });

  it('displays deployment metrics', async () => {
    render(<MetricsDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Deployment Metrics')).toBeInTheDocument();
    });

    expect(screen.getByText('Success Rate')).toBeInTheDocument();
    expect(screen.getByText('MTTR')).toBeInTheDocument();
    expect(screen.getByText('Change Failure Rate')).toBeInTheDocument();
  });

  it('handles time range filter changes', async () => {
    render(<MetricsDashboard />);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('7d')).toBeInTheDocument();
    });

    const timeRangeSelect = screen.getByDisplayValue('7d');
    fireEvent.change(timeRangeSelect, { target: { value: '30d' } });
    
    expect(timeRangeSelect).toHaveValue('30d');
  });

  it('handles refresh button click', async () => {
    render(<MetricsDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Refresh')).toBeInTheDocument();
    });

    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);
    
    // Should trigger a re-render with loading state
    expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument();
  });

  it('displays correct metric values', async () => {
    render(<MetricsDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('1,247')).toBeInTheDocument();
    });

    // Check for formatted numbers
    expect(screen.getByText('95.2%')).toBeInTheDocument();
    expect(screen.getByText('3m')).toBeInTheDocument();
    expect(screen.getByText('$1,247.50')).toBeInTheDocument();
  });

  it('displays trend indicators', async () => {
    render(<MetricsDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Trending up')).toBeInTheDocument();
    });
  });

  it('accepts custom props', async () => {
    render(<MetricsDashboard timeRange="30d" pipelineId="test-pipeline" environment="production" />);

    await waitFor(() => {
      // Check that the component renders with custom props
      const selects = screen.getAllByRole('combobox');
      expect(selects.length).toBeGreaterThan(0);
    }, { timeout: 5000 });
  });

  it('formats durations correctly', async () => {
    render(<MetricsDashboard />);

    await waitFor(() => {
      expect(screen.getAllByText('3m')).toHaveLength.greaterThan(0);
    }, { timeout: 5000 });

    // Check for various duration formats (using getAllByText for duplicates)
    expect(screen.getAllByText('3m')).toHaveLength.greaterThan(0);
    expect(screen.getByText('6m 20s')).toBeInTheDocument();
    expect(screen.getByText('4m')).toBeInTheDocument();
  });
});
