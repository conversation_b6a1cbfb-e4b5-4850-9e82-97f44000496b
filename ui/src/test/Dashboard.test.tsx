import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';

// Mock the API module
const mockApi = {
  get: vi.fn(),
};

vi.mock('../services/api', () => ({
  api: mockApi,
}));

const renderDashboard = () => {
  return render(
    <MemoryRouter>
      <Dashboard />
    </MemoryRouter>
  );
};

describe('Dashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue({
      data: { pagination: { total: 5 } }
    });
  });

  it('renders loading state initially', () => {
    renderDashboard();
    expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument();
  });

  it('renders dashboard content after loading', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Welcome to ChainOps')).toBeInTheDocument();
    });

    expect(screen.getByText('Manage your CI/CD pipelines with ease')).toBeInTheDocument();
    expect(screen.getByText('Create Pipeline')).toBeInTheDocument();
  });

  it('displays correct metrics cards', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Total Pipelines')).toBeInTheDocument();
    });

    // Check for all metric cards
    expect(screen.getByText('Total Executions')).toBeInTheDocument();
    expect(screen.getByText('Running')).toBeInTheDocument();
    expect(screen.getByText('Success Rate')).toBeInTheDocument();
    expect(screen.getByText('Total Deployments')).toBeInTheDocument();
    expect(screen.getByText('Active Deployments')).toBeInTheDocument();
    expect(screen.getByText('Secrets')).toBeInTheDocument();
    expect(screen.getByText('System Health')).toBeInTheDocument();
  });

  it('displays system metrics', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('CPU Usage')).toBeInTheDocument();
    });

    expect(screen.getByText('Memory Usage')).toBeInTheDocument();
    expect(screen.getByText('Active Alerts')).toBeInTheDocument();
  });

  it('displays recent executions section', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Recent Executions')).toBeInTheDocument();
    });

    // Check for mock execution data
    expect(screen.getByText('Frontend Build & Deploy')).toBeInTheDocument();
    expect(screen.getByText('Backend API Tests')).toBeInTheDocument();
    expect(screen.getByText('Security Scan')).toBeInTheDocument();
    expect(screen.getByText('Local Testing Demo')).toBeInTheDocument();
  });

  it('displays active alerts', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Active Alerts')).toBeInTheDocument();
    });

    expect(screen.getByText('High CPU Usage')).toBeInTheDocument();
    expect(screen.getByText('Pipeline Failure Rate Increased')).toBeInTheDocument();
  });

  it('displays quick actions', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    });

    // Check for quick action items
    expect(screen.getByText('Create Pipeline')).toBeInTheDocument();
    expect(screen.getByText('View Pipelines')).toBeInTheDocument();
    expect(screen.getByText('View Executions')).toBeInTheDocument();
    expect(screen.getByText('Deployments')).toBeInTheDocument();
    expect(screen.getByText('Secrets')).toBeInTheDocument();
  });

  it('displays correct execution statuses with icons', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Frontend Build & Deploy')).toBeInTheDocument();
    });

    // Check for status badges
    const successBadges = screen.getAllByText('success');
    const runningBadges = screen.getAllByText('running');
    
    expect(successBadges.length).toBeGreaterThan(0);
    expect(runningBadges.length).toBeGreaterThan(0);
  });

  it('displays execution durations', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('180s')).toBeInTheDocument();
    });

    expect(screen.getByText('95s')).toBeInTheDocument();
    expect(screen.getByText('12s')).toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));
    
    renderDashboard();
    
    // Should still render the dashboard with default/mock data
    await waitFor(() => {
      expect(screen.getByText('Welcome to ChainOps')).toBeInTheDocument();
    });
  });
});
