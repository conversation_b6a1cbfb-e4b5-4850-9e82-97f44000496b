import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';
import { api, pipelineAPI, executionAPI, jobAPI, triggerAPI, healthAPI } from '../services/api';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock window.location
const mockLocation = {
  href: '',
};
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

describe('API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('API Instance Configuration', () => {
    it('should have correct base configuration', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:8080/api/v1',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    });
  });

  describe('Pipeline API', () => {
    const mockPipelineResponse = {
      data: {
        success: true,
        data: {
          id: '1',
          name: 'Test Pipeline',
          repository: 'test/repo',
          branch: 'main',
          config_path: '.chainops.yml',
          yaml_content: 'test: yaml',
          is_active: true,
          variables: {},
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      },
    };

    it('should list pipelines', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: [mockPipelineResponse.data.data],
          pagination: {
            page: 1,
            per_page: 20,
            total: 1,
            total_pages: 1,
          },
        },
      };

      mockedAxios.get = vi.fn().mockResolvedValue(mockResponse);

      const result = await pipelineAPI.list(1, 20);

      expect(mockedAxios.get).toHaveBeenCalledWith('/pipelines?page=1&per_page=20');
      expect(result).toEqual(mockResponse.data);
    });

    it('should get pipeline by ID', async () => {
      mockedAxios.get = vi.fn().mockResolvedValue(mockPipelineResponse);

      const result = await pipelineAPI.get('1');

      expect(mockedAxios.get).toHaveBeenCalledWith('/pipelines/1');
      expect(result).toEqual(mockPipelineResponse.data);
    });

    it('should create pipeline', async () => {
      const createData = {
        name: 'New Pipeline',
        repository: 'test/repo',
        yaml_content: 'test: yaml',
      };

      mockedAxios.post = vi.fn().mockResolvedValue(mockPipelineResponse);

      const result = await pipelineAPI.create(createData);

      expect(mockedAxios.post).toHaveBeenCalledWith('/pipelines', createData);
      expect(result).toEqual(mockPipelineResponse.data);
    });

    it('should update pipeline', async () => {
      const updateData = { name: 'Updated Pipeline' };

      mockedAxios.put = vi.fn().mockResolvedValue(mockPipelineResponse);

      const result = await pipelineAPI.update('1', updateData);

      expect(mockedAxios.put).toHaveBeenCalledWith('/pipelines/1', updateData);
      expect(result).toEqual(mockPipelineResponse.data);
    });

    it('should delete pipeline', async () => {
      const mockResponse = { data: { success: true } };

      mockedAxios.delete = vi.fn().mockResolvedValue(mockResponse);

      const result = await pipelineAPI.delete('1');

      expect(mockedAxios.delete).toHaveBeenCalledWith('/pipelines/1');
      expect(result).toEqual(mockResponse.data);
    });

    it('should execute pipeline', async () => {
      const executeData = {
        trigger_type: 'manual' as const,
        variables: { ENV: 'test' },
      };

      const mockResponse = {
        data: {
          success: true,
          data: {
            id: 'exec-1',
            pipeline_id: '1',
            status: 'pending' as const,
            trigger_type: 'manual' as const,
            trigger_data: {},
            variables: { ENV: 'test' },
            created_at: '2024-01-01T00:00:00Z',
          },
        },
      };

      mockedAxios.post = vi.fn().mockResolvedValue(mockResponse);

      const result = await pipelineAPI.execute('1', executeData);

      expect(mockedAxios.post).toHaveBeenCalledWith('/pipelines/1/execute', executeData);
      expect(result).toEqual(mockResponse.data);
    });

    it('should validate pipeline YAML', async () => {
      const yamlContent = 'test: yaml';
      const mockResponse = {
        data: {
          success: true,
          data: { valid: true, errors: [] },
        },
      };

      mockedAxios.post = vi.fn().mockResolvedValue(mockResponse);

      const result = await pipelineAPI.validate(yamlContent);

      expect(mockedAxios.post).toHaveBeenCalledWith('/pipelines/validate', { yaml_content: yamlContent });
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('Execution API', () => {
    it('should get execution by ID', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            id: 'exec-1',
            pipeline_id: '1',
            status: 'success' as const,
            trigger_type: 'manual' as const,
            trigger_data: {},
            variables: {},
            created_at: '2024-01-01T00:00:00Z',
          },
        },
      };

      mockedAxios.get = vi.fn().mockResolvedValue(mockResponse);

      const result = await executionAPI.get('exec-1');

      expect(mockedAxios.get).toHaveBeenCalledWith('/executions/exec-1');
      expect(result).toEqual(mockResponse.data);
    });

    it('should cancel execution', async () => {
      const mockResponse = { data: { success: true } };

      mockedAxios.post = vi.fn().mockResolvedValue(mockResponse);

      const result = await executionAPI.cancel('exec-1');

      expect(mockedAxios.post).toHaveBeenCalledWith('/executions/exec-1/cancel');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('Health API', () => {
    it('should check health', async () => {
      const mockResponse = { data: { success: true } };

      mockedAxios.get = vi.fn().mockResolvedValue(mockResponse);

      const result = await healthAPI.check();

      expect(mockedAxios.get).toHaveBeenCalledWith('/health');
      expect(result).toEqual(mockResponse.data);
    });
  });
});
