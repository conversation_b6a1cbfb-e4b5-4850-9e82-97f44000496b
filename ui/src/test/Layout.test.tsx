import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import Layout from '../components/Layout';

const renderLayout = (initialEntries = ['/'], children = <div>Test Content</div>) => {
  return render(
    <MemoryRouter initialEntries={initialEntries}>
      <Layout>{children}</Layout>
    </MemoryRouter>
  );
};

describe('Layout Component', () => {
  it('renders the layout structure', () => {
    renderLayout();
    
    // Check for main layout elements
    expect(screen.getByText('ChainOps')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders all navigation items', () => {
    renderLayout();

    // Check for all navigation items (using getAllByText for items that appear multiple times)
    expect(screen.getAllByText('Dashboard')).toHaveLength(2); // Navigation link + page title
    expect(screen.getByText('Pipelines')).toBeInTheDocument();
    expect(screen.getByText('Executions')).toBeInTheDocument();
    expect(screen.getByText('Deployments')).toBeInTheDocument();
    expect(screen.getByText('Secrets')).toBeInTheDocument();
    expect(screen.getByText('Monitoring')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Infrastructure')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('highlights active navigation item', () => {
    renderLayout(['/pipelines']);
    
    const pipelinesLink = screen.getByRole('link', { name: /pipelines/i });
    expect(pipelinesLink).toHaveClass('bg-blue-100', 'text-blue-700');
  });

  it('renders user profile section', () => {
    renderLayout();
    
    expect(screen.getByText('Admin User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('displays correct page title in header', () => {
    renderLayout(['/']);
    expect(screen.getAllByText('Dashboard')).toHaveLength(2); // Navigation + header

    renderLayout(['/pipelines']);
    expect(screen.getByRole('heading', { name: 'Pipelines' })).toBeInTheDocument();

    renderLayout(['/executions']);
    expect(screen.getByRole('heading', { name: 'Executions' })).toBeInTheDocument();
  });

  it('displays correct page title for nested routes', () => {
    renderLayout(['/pipelines/new']);
    expect(screen.getByText('Create Pipeline')).toBeInTheDocument();
    
    renderLayout(['/pipelines/123']);
    expect(screen.getByText('Pipeline Details')).toBeInTheDocument();
    
    renderLayout(['/executions/456']);
    expect(screen.getByText('Execution Details')).toBeInTheDocument();
  });

  it('renders children content in main area', () => {
    const testContent = <div data-testid="custom-content">Custom Test Content</div>;
    renderLayout(['/'], testContent);
    
    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('Custom Test Content')).toBeInTheDocument();
  });

  it('has proper responsive layout classes', () => {
    renderLayout();

    // Check for responsive layout classes - find the actual sidebar container
    const sidebarContainer = screen.getByText('ChainOps').closest('div')?.parentElement;
    expect(sidebarContainer).toHaveClass('w-64', 'bg-white', 'shadow-lg');

    const mainContent = screen.getByText('Test Content').closest('main');
    expect(mainContent).toHaveClass('flex-1', 'overflow-y-auto');
  });

  it('navigation links have correct href attributes', () => {
    renderLayout();
    
    expect(screen.getByRole('link', { name: /dashboard/i })).toHaveAttribute('href', '/');
    expect(screen.getByRole('link', { name: /pipelines/i })).toHaveAttribute('href', '/pipelines');
    expect(screen.getByRole('link', { name: /executions/i })).toHaveAttribute('href', '/executions');
    expect(screen.getByRole('link', { name: /deployments/i })).toHaveAttribute('href', '/deployments');
    expect(screen.getByRole('link', { name: /secrets/i })).toHaveAttribute('href', '/secrets');
    expect(screen.getByRole('link', { name: /monitoring/i })).toHaveAttribute('href', '/monitoring');
    expect(screen.getByRole('link', { name: /notifications/i })).toHaveAttribute('href', '/notifications');
    expect(screen.getByRole('link', { name: /infrastructure/i })).toHaveAttribute('href', '/infrastructure');
    expect(screen.getByRole('link', { name: /settings/i })).toHaveAttribute('href', '/settings');
  });
});
