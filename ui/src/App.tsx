import { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Sidebar from './components/Sidebar';
import TopNavbar from './components/TopNavbar';
import Dashboard from './pages/Dashboard';
import Pipelines from './pages/Pipelines';
import PipelineDetail from './pages/PipelineDetail';
import Projects from './pages/Projects';
import Deployments from './pages/Deployments';
import Insights from './pages/Insights';
import Runners from './pages/Runners';
import Settings from './pages/Settings';
import Plan from './pages/Plan';

function App() {
  const [darkMode, setDarkMode] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  return (
    <div className={`min-h-screen ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <Router>
        <div className="flex">
          <Sidebar
            collapsed={sidebarCollapsed}
            onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
            darkMode={darkMode}
          />
          <div className={`flex-1 transition-all duration-300 ${sidebarCollapsed ? 'ml-16' : 'ml-64'}`}>
            <TopNavbar
              darkMode={darkMode}
              onToggleDarkMode={() => setDarkMode(!darkMode)}
              onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
            />
            <main className="p-6">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/pipelines" element={<Pipelines />} />
                <Route path="/pipelines/:id" element={<PipelineDetail />} />
                <Route path="/projects" element={<Projects />} />
                <Route path="/deployments" element={<Deployments />} />
                <Route path="/insights" element={<Insights />} />
                <Route path="/runners" element={<Runners />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/plan" element={<Plan />} />
              </Routes>
            </main>
          </div>
        </div>
        <Toaster position="top-right" />
      </Router>
    </div>
  );
}

export default App;
