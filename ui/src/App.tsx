import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Pipelines from './pages/Pipelines';
import PipelineDetail from './pages/PipelineDetail';
import Executions from './pages/Executions';
import ExecutionDetail from './pages/ExecutionDetail';
import CreatePipeline from './pages/CreatePipeline';
import Deployments from './pages/Deployments';
import Secrets from './pages/Secrets';
import Monitoring from './pages/Monitoring';
import UserManagement from './pages/UserManagement';
import Analytics from './pages/Analytics';
import Notifications from './pages/Notifications';
import Integrations from './pages/Integrations';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/pipelines" element={<Pipelines />} />
            <Route path="/pipelines/new" element={<CreatePipeline />} />
            <Route path="/pipelines/:id" element={<PipelineDetail />} />
            <Route path="/executions" element={<Executions />} />
            <Route path="/executions/:id" element={<ExecutionDetail />} />
            <Route path="/deployments" element={<Deployments />} />
            <Route path="/secrets" element={<Secrets />} />
            <Route path="/monitoring" element={<Monitoring />} />
            <Route path="/users" element={<UserManagement />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/notifications" element={<Notifications />} />
            <Route path="/integrations" element={<Integrations />} />
          </Routes>
        </Layout>
        <Toaster position="top-right" />
      </div>
    </Router>
  );
}

export default App;
