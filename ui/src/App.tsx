import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout';
import Overview from './pages/Overview';
import Pipelines from './pages/Pipelines';
import PipelineDetail from './pages/PipelineDetail';
import Executions from './pages/Executions';
import ExecutionDetail from './pages/ExecutionDetail';
import CreatePipeline from './pages/CreatePipeline';
import Deployments from './pages/Deployments';
import Secrets from './pages/Secrets';
import Monitoring from './pages/Monitoring';
import Analytics from './pages/Analytics';
import Projects from './pages/Projects';
import Runners from './pages/Runners';
import Plan from './pages/Plan';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Layout>
          <Routes>
            <Route path="/" element={<Overview />} />
            <Route path="/pipelines" element={<Pipelines />} />
            <Route path="/pipelines/new" element={<CreatePipeline />} />
            <Route path="/pipelines/:id" element={<PipelineDetail />} />
            <Route path="/projects" element={<Projects />} />
            <Route path="/deployments" element={<Deployments />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/runners" element={<Runners />} />
            <Route path="/settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Settings</h1><p>Coming soon...</p></div>} />
            <Route path="/plan" element={<Plan />} />
            <Route path="/executions" element={<Executions />} />
            <Route path="/executions/:id" element={<ExecutionDetail />} />
            <Route path="/secrets" element={<Secrets />} />
            <Route path="/monitoring" element={<Monitoring />} />
          </Routes>
        </Layout>
        <Toaster position="top-right" />
      </div>
    </Router>
  );
}

export default App;
