import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Pipelines from './pages/Pipelines';
import PipelineDetail from './pages/PipelineDetail';
import Executions from './pages/Executions';
import ExecutionDetail from './pages/ExecutionDetail';
import CreatePipeline from './pages/CreatePipeline';
import Deployments from './pages/Deployments';
import Secrets from './pages/Secrets';
import Monitoring from './pages/Monitoring';
import Analytics from './pages/Analytics';
import Projects from './pages/Projects';
import Runners from './pages/Runners';
import Plan from './pages/Plan';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Layout>
          <Routes>
            <Route path="/" element={
              <div className="p-6 bg-white">
                <div className="mb-6">
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">ChainOps - All Pipelines</h1>
                  <p className="text-sm text-gray-600">CircleCI-style interface</p>
                </div>

                {/* Test the exact CircleCI layout */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  {/* Header */}
                  <div className="grid grid-cols-12 gap-4 px-6 py-3 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
                    <div className="col-span-3">Project</div>
                    <div className="col-span-2">Status</div>
                    <div className="col-span-2">Workflow</div>
                    <div className="col-span-5">Trigger Event</div>
                  </div>

                  {/* Sample Row */}
                  <div className="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors items-center border-b border-gray-200">
                    <div className="col-span-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium text-blue-600">chatbot-ui 184719</span>
                      </div>
                    </div>
                    <div className="col-span-2">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Running
                      </span>
                    </div>
                    <div className="col-span-2">
                      <span className="text-sm text-blue-600">lint</span>
                    </div>
                    <div className="col-span-5">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">fix-favicon</div>
                          <div className="text-sm text-gray-500">2639 / Update</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            } />
            <Route path="/pipelines" element={<Pipelines />} />
            <Route path="/pipelines/new" element={<CreatePipeline />} />
            <Route path="/pipelines/:id" element={<PipelineDetail />} />
            <Route path="/projects" element={<Projects />} />
            <Route path="/deployments" element={<Deployments />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/runners" element={<Runners />} />
            <Route path="/settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Settings</h1><p>Coming soon...</p></div>} />
            <Route path="/plan" element={<Plan />} />
            <Route path="/executions" element={<Executions />} />
            <Route path="/executions/:id" element={<ExecutionDetail />} />
            <Route path="/secrets" element={<Secrets />} />
            <Route path="/monitoring" element={<Monitoring />} />
          </Routes>
        </Layout>
        <Toaster position="top-right" />
      </div>
    </Router>
  );
}

export default App;
