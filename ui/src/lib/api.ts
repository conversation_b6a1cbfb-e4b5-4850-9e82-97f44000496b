// Simple API client for ChainOps
export const api = {
  get: async (url: string) => {
    // Mock API responses for development
    console.log(`API GET: ${url}`);
    return {
      data: {
        pipelines: []
      }
    };
  },
  
  post: async (url: string, data: any) => {
    console.log(`API POST: ${url}`, data);
    return { data: {} };
  },
  
  put: async (url: string, data: any) => {
    console.log(`API PUT: ${url}`, data);
    return { data: {} };
  },
  
  delete: async (url: string) => {
    console.log(`API DELETE: ${url}`);
    return { data: {} };
  }
};
