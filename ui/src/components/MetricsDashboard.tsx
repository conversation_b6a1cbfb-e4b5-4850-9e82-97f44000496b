import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  CpuChipIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
} from '@heroicons/react/24/outline';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { api } from '../services/api';

// Simple components for the metrics dashboard
const Card = ({ children, className }: { children: React.ReactNode; className?: string }) => (
  <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>{children}</div>
);

const Select = ({ value, onChange, options, className, placeholder }: any) => (
  <select
    value={value}
    onChange={(e) => onChange(e.target.value)}
    className={`border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
  >
    <option value="">{placeholder}</option>
    {options.map((opt: any) => (
      <option key={opt.value} value={opt.value}>{opt.label}</option>
    ))}
  </select>
);

const Button = ({ children, onClick, variant, className }: any) => (
  <button
    onClick={onClick}
    className={`px-4 py-2 rounded-md font-medium transition-colors ${
      variant === 'outline'
        ? 'border border-gray-300 text-gray-700 hover:bg-gray-50'
        : 'bg-blue-600 text-white hover:bg-blue-700'
    } ${className}`}
  >
    {children}
  </button>
);

// Utility formatters
const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds}s`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
};

const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;
const formatNumber = (num: number) => num.toLocaleString();

interface MetricsDashboardProps {
  timeRange?: string;
  pipelineId?: string;
  environment?: string;
}

interface DashboardData {
  pipeline_metrics: PipelineMetrics;
  job_metrics: JobMetrics;
  deployment_metrics: DeploymentMetrics;
  resource_usage: ResourceUsage;
  trend_data: TrendData;
  generated_at: string;
}

interface PipelineMetrics {
  total_executions: number;
  successful_executions: number;
  failed_executions: number;
  success_rate: number;
  average_duration: number;
  median_duration?: number;
  p95_duration?: number;
  executions_by_status: Record<string, number>;
  executions_by_branch?: Record<string, number>;
  executions_by_pipeline?: Record<string, number>;
  daily_executions: DailyMetric[];
  top_failure_reasons: FailureReason[];
}

interface JobMetrics {
  total_jobs: number;
  successful_jobs: number;
  failed_jobs: number;
  success_rate: number;
  average_duration: number;
  jobs_by_stage: Record<string, number>;
  jobs_by_status: Record<string, number>;
  slowest_jobs: JobPerformance[];
  most_failed_jobs: JobFailure[];
}

interface DeploymentMetrics {
  total_deployments: number;
  successful_deployments: number;
  failed_deployments: number;
  success_rate: number;
  average_duration: number;
  deployments_by_environment: Record<string, number>;
  deployments_by_strategy: Record<string, number>;
  rollback_rate: number;
  mttr: number;
  change_failure_rate: number;
}

interface ResourceUsage {
  total_cpu_hours: number;
  total_memory_gb_hours: number;
  total_storage_gb: number;
  cost_estimate: number;
  usage_by_pipeline: Record<string, ResourceMetric>;
  usage_by_environment: Record<string, ResourceMetric>;
  daily_usage: DailyResourceUsage[];
}

interface TrendData {
  execution_trend: TrendPoint[];
  success_rate_trend: TrendPoint[];
  duration_trend: TrendPoint[];
  failure_rate_trend: TrendPoint[];
  resource_usage_trend: TrendPoint[];
}

interface DailyMetric {
  date: string;
  count: number;
  successful: number;
  failed: number;
}

interface FailureReason {
  reason: string;
  count: number;
}

interface JobPerformance {
  job_name: string;
  pipeline_id: string;
  duration: number;
  execution_id: string;
}

interface JobFailure {
  job_name: string;
  pipeline_id: string;
  failure_count: number;
  last_failure: string;
}

interface ResourceMetric {
  cpu_hours: number;
  memory_gb_hours: number;
  storage_gb: number;
  cost: number;
}

interface DailyResourceUsage {
  date: string;
  cpu_hours: number;
  memory_gb_hours: number;
  storage_gb: number;
  cost: number;
}

interface TrendPoint {
  timestamp: string;
  value: number;
}

const MetricsDashboard: React.FC<MetricsDashboardProps> = ({
  timeRange = '7d',
  pipelineId,
  environment,
}) => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [selectedPipeline, setSelectedPipeline] = useState(pipelineId || '');
  const [selectedEnvironment, setSelectedEnvironment] = useState(environment || '');

  useEffect(() => {
    loadDashboardData();
  }, [selectedTimeRange, selectedPipeline, selectedEnvironment]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Mock data for demonstration
      const mockData: DashboardData = {
        pipeline_metrics: {
          total_executions: 1247,
          successful_executions: 1187,
          failed_executions: 60,
          success_rate: 95.2,
          average_duration: 180,
          executions_by_status: {
            success: 1187,
            failed: 45,
            running: 12,
            cancelled: 3
          },
          executions_by_pipeline: {
            'Frontend Build': 423,
            'Backend API': 387,
            'Security Scan': 245,
            'Local Testing': 192
          },
          daily_executions: [
            { date: '2024-01-01', count: 45, successful: 43, failed: 2 },
            { date: '2024-01-02', count: 52, successful: 49, failed: 3 },
            { date: '2024-01-03', count: 38, successful: 36, failed: 2 },
            { date: '2024-01-04', count: 61, successful: 58, failed: 3 },
            { date: '2024-01-05', count: 47, successful: 45, failed: 2 },
            { date: '2024-01-06', count: 55, successful: 52, failed: 3 },
            { date: '2024-01-07', count: 49, successful: 47, failed: 2 }
          ],
          top_failure_reasons: [
            { reason: 'Test failures', count: 18 },
            { reason: 'Build timeout', count: 12 },
            { reason: 'Dependency issues', count: 8 },
            { reason: 'Network errors', count: 6 },
            { reason: 'Configuration errors', count: 4 }
          ]
        },
        job_metrics: {
          total_jobs: 5234,
          successful_jobs: 4987,
          failed_jobs: 247,
          success_rate: 95.3,
          average_duration: 45,
          jobs_by_stage: {
            'build': 1245,
            'test': 1567,
            'security': 892,
            'deploy': 1530
          },
          jobs_by_status: {
            'success': 4987,
            'failed': 247
          },
          slowest_jobs: [
            { job_name: 'Integration Tests', pipeline_id: '1', duration: 420, execution_id: 'exec-1' },
            { job_name: 'Security Scan', pipeline_id: '2', duration: 380, execution_id: 'exec-2' },
            { job_name: 'Build Frontend', pipeline_id: '3', duration: 240, execution_id: 'exec-3' },
            { job_name: 'Deploy Production', pipeline_id: '4', duration: 180, execution_id: 'exec-4' }
          ],
          most_failed_jobs: [
            { job_name: 'Flaky Test Suite', pipeline_id: '1', failure_count: 12, last_failure: '2024-01-07T10:30:00Z' },
            { job_name: 'Network Dependent Test', pipeline_id: '2', failure_count: 8, last_failure: '2024-01-07T09:15:00Z' }
          ]
        },
        deployment_metrics: {
          total_deployments: 342,
          successful_deployments: 325,
          failed_deployments: 17,
          success_rate: 95.0,
          average_duration: 120,
          deployments_by_environment: {
            'production': 156,
            'staging': 186
          },
          deployments_by_strategy: {
            'rolling': 234,
            'blue-green': 108
          },
          rollback_rate: 2.3,
          mttr: 1800,
          change_failure_rate: 4.7
        },
        resource_usage: {
          total_cpu_hours: 2847,
          total_memory_gb_hours: 5694,
          total_storage_gb: 1250,
          cost_estimate: 1247.50,
          usage_by_pipeline: {
            'Frontend Build': { cpu_hours: 456, memory_gb_hours: 912, storage_gb: 200, cost: 234.50 },
            'Backend API': { cpu_hours: 678, memory_gb_hours: 1356, storage_gb: 300, cost: 345.75 }
          },
          usage_by_environment: {
            'production': { cpu_hours: 1423, memory_gb_hours: 2846, storage_gb: 625, cost: 623.75 },
            'staging': { cpu_hours: 1424, memory_gb_hours: 2848, storage_gb: 625, cost: 623.75 }
          },
          daily_usage: [
            { date: '2024-01-01', cpu_hours: 45, memory_gb_hours: 90, storage_gb: 180, cost: 23.50 },
            { date: '2024-01-02', cpu_hours: 52, memory_gb_hours: 104, storage_gb: 185, cost: 27.25 },
            { date: '2024-01-03', cpu_hours: 38, memory_gb_hours: 76, storage_gb: 175, cost: 19.75 },
            { date: '2024-01-04', cpu_hours: 61, memory_gb_hours: 122, storage_gb: 195, cost: 31.50 },
            { date: '2024-01-05', cpu_hours: 47, memory_gb_hours: 94, storage_gb: 182, cost: 24.25 },
            { date: '2024-01-06', cpu_hours: 55, memory_gb_hours: 110, storage_gb: 188, cost: 28.75 },
            { date: '2024-01-07', cpu_hours: 49, memory_gb_hours: 98, storage_gb: 185, cost: 25.50 }
          ]
        },
        trend_data: {
          execution_trend: [
            { timestamp: '2024-01-01', value: 45 },
            { timestamp: '2024-01-02', value: 52 },
            { timestamp: '2024-01-03', value: 38 },
            { timestamp: '2024-01-04', value: 61 },
            { timestamp: '2024-01-05', value: 47 },
            { timestamp: '2024-01-06', value: 55 },
            { timestamp: '2024-01-07', value: 49 }
          ],
          success_rate_trend: [
            { timestamp: '2024-01-01', value: 95.6 },
            { timestamp: '2024-01-02', value: 94.2 },
            { timestamp: '2024-01-03', value: 94.7 },
            { timestamp: '2024-01-04', value: 95.1 },
            { timestamp: '2024-01-05', value: 95.7 },
            { timestamp: '2024-01-06', value: 94.5 },
            { timestamp: '2024-01-07', value: 95.9 }
          ],
          duration_trend: [
            { timestamp: '2024-01-01', value: 185 },
            { timestamp: '2024-01-02', value: 172 },
            { timestamp: '2024-01-03', value: 195 },
            { timestamp: '2024-01-04', value: 168 },
            { timestamp: '2024-01-05', value: 180 },
            { timestamp: '2024-01-06', value: 177 },
            { timestamp: '2024-01-07', value: 183 }
          ],
          failure_rate_trend: [
            { timestamp: '2024-01-01', value: 4.4 },
            { timestamp: '2024-01-02', value: 5.8 },
            { timestamp: '2024-01-03', value: 5.3 },
            { timestamp: '2024-01-04', value: 4.9 },
            { timestamp: '2024-01-05', value: 4.3 },
            { timestamp: '2024-01-06', value: 5.5 },
            { timestamp: '2024-01-07', value: 4.1 }
          ],
          resource_usage_trend: [
            { timestamp: '2024-01-01', value: 23.50 },
            { timestamp: '2024-01-02', value: 27.25 },
            { timestamp: '2024-01-03', value: 19.75 },
            { timestamp: '2024-01-04', value: 31.50 },
            { timestamp: '2024-01-05', value: 24.25 },
            { timestamp: '2024-01-06', value: 28.75 },
            { timestamp: '2024-01-07', value: 25.50 }
          ]
        },
        generated_at: new Date().toISOString()
      };

      setData(mockData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Failed to load dashboard data</p>
        <Button onClick={loadDashboardData} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <Select
            value={selectedTimeRange}
            onChange={setSelectedTimeRange}
            options={[
              { value: '1d', label: 'Last 24 hours' },
              { value: '7d', label: 'Last 7 days' },
              { value: '30d', label: 'Last 30 days' },
              { value: '90d', label: 'Last 90 days' },
            ]}
            className="w-40"
          />
          <Select
            value={selectedPipeline}
            onChange={setSelectedPipeline}
            options={[
              { value: '', label: 'All Pipelines' },
              // Add pipeline options dynamically
            ]}
            className="w-48"
            placeholder="Select Pipeline"
          />
          <Select
            value={selectedEnvironment}
            onChange={setSelectedEnvironment}
            options={[
              { value: '', label: 'All Environments' },
              { value: 'development', label: 'Development' },
              { value: 'staging', label: 'Staging' },
              { value: 'production', label: 'Production' },
            ]}
            className="w-48"
            placeholder="Select Environment"
          />
          <Button onClick={loadDashboardData} variant="outline">
            Refresh
          </Button>
        </div>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Executions"
          value={formatNumber(data.pipeline_metrics.total_executions)}
          icon={<ChartBarIcon className="w-6 h-6" />}
          color="blue"
        />
        <MetricCard
          title="Success Rate"
          value={`${data.pipeline_metrics.success_rate.toFixed(1)}%`}
          icon={<CheckCircleIcon className="w-6 h-6" />}
          color="green"
          trend={data.pipeline_metrics.success_rate > 90 ? 'up' : 'down'}
        />
        <MetricCard
          title="Avg Duration"
          value={formatDuration(data.pipeline_metrics.average_duration)}
          icon={<ClockIcon className="w-6 h-6" />}
          color="yellow"
        />
        <MetricCard
          title="Cost Estimate"
          value={formatCurrency(data.resource_usage.cost_estimate)}
          icon={<CurrencyDollarIcon className="w-6 h-6" />}
          color="purple"
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Execution Trend */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Execution Trend</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data.trend_data.execution_trend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#3B82F6"
                strokeWidth={2}
                dot={{ fill: '#3B82F6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </Card>

        {/* Success Rate Trend */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Success Rate Trend</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data.trend_data.success_rate_trend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="value"
                stroke="#10B981"
                fill="#10B981"
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Executions by Status */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Executions by Status</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={Object.entries(data.pipeline_metrics.executions_by_status).map(([key, value]) => ({
                  name: key,
                  value,
                }))}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {Object.entries(data.pipeline_metrics.executions_by_status).map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Card>

        {/* Resource Usage */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Daily Resource Usage</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.resource_usage.daily_usage}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="cpu_hours" fill="#3B82F6" name="CPU Hours" />
              <Bar dataKey="memory_gb_hours" fill="#10B981" name="Memory GB Hours" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Tables Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Failure Reasons */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Top Failure Reasons</h3>
          <div className="space-y-2">
            {data.pipeline_metrics.top_failure_reasons.map((reason, index) => (
              <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span className="text-sm">{reason.reason}</span>
                <span className="text-sm font-medium">{reason.count}</span>
              </div>
            ))}
          </div>
        </Card>

        {/* Slowest Jobs */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Slowest Jobs</h3>
          <div className="space-y-2">
            {data.job_metrics.slowest_jobs.map((job, index) => (
              <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span className="text-sm">{job.job_name}</span>
                <span className="text-sm font-medium">{formatDuration(job.duration)}</span>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Deployment Metrics */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Deployment Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {data.deployment_metrics.success_rate.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatDuration(data.deployment_metrics.mttr)}
            </div>
            <div className="text-sm text-gray-600">MTTR</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {data.deployment_metrics.change_failure_rate.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Change Failure Rate</div>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Metric Card Component
interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'yellow' | 'purple' | 'red';
  trend?: 'up' | 'down';
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, color, trend }) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-white',
    green: 'bg-green-500 text-white',
    yellow: 'bg-yellow-500 text-white',
    purple: 'bg-purple-500 text-white',
    red: 'bg-red-500 text-white',
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          {icon}
        </div>
      </div>
      {trend && (
        <div className="mt-2 flex items-center">
          {trend === 'up' ? (
            <svg className="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          ) : (
            <svg className="w-4 h-4 text-red-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
            </svg>
          )}
          <span className={`text-sm ${trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
            {trend === 'up' ? 'Trending up' : 'Trending down'}
          </span>
        </div>
      )}
    </Card>
  );
};

export default MetricsDashboard;
