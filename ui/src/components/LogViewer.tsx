import React, { useState, useEffect, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { 
  PlayIcon, 
  PauseIcon, 
  ArrowDownIcon, 
  MagnifyingGlassIcon,
  DocumentArrowDownIcon,
  AdjustmentsHorizontalIcon 
} from '@heroicons/react/24/outline';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { Select } from './ui/Select';
import { Card } from './ui/Card';

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  source: string;
  jobId?: string;
  stepId?: string;
  metadata?: Record<string, any>;
}

interface LogViewerProps {
  executionId?: string;
  jobId?: string;
  stepId?: string;
  height?: string;
  showControls?: boolean;
  autoScroll?: boolean;
}

const LogViewer: React.FC<LogViewerProps> = ({
  executionId,
  jobId,
  stepId,
  height = '400px',
  showControls = true,
  autoScroll = true,
}) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [logLevel, setLogLevel] = useState<string>('all');
  const [isAutoScrolling, setIsAutoScrolling] = useState(autoScroll);
  const [showTimestamps, setShowTimestamps] = useState(true);
  const [showMetadata, setShowMetadata] = useState(false);

  const logContainerRef = useRef<HTMLDivElement>(null);
  const socketRef = useRef<Socket | null>(null);
  const lastLogIdRef = useRef<string>('');

  // WebSocket connection
  useEffect(() => {
    if (!executionId && !jobId) return;

    const socket = io('/logs', {
      query: {
        executionId,
        jobId,
        stepId,
      },
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      setIsConnected(true);
      console.log('Connected to log stream');
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
      console.log('Disconnected from log stream');
    });

    socket.on('log', (logEntry: LogEntry) => {
      if (!isPaused) {
        setLogs(prev => {
          const newLogs = [...prev, logEntry];
          // Keep only last 1000 logs for performance
          return newLogs.slice(-1000);
        });
        lastLogIdRef.current = logEntry.id;
      }
    });

    socket.on('logs_batch', (logEntries: LogEntry[]) => {
      if (!isPaused) {
        setLogs(prev => {
          const newLogs = [...prev, ...logEntries];
          return newLogs.slice(-1000);
        });
        if (logEntries.length > 0) {
          lastLogIdRef.current = logEntries[logEntries.length - 1].id;
        }
      }
    });

    socket.on('error', (error: any) => {
      console.error('Log stream error:', error);
    });

    return () => {
      socket.disconnect();
    };
  }, [executionId, jobId, stepId, isPaused]);

  // Filter logs based on search and level
  useEffect(() => {
    let filtered = logs;

    // Filter by log level
    if (logLevel !== 'all') {
      filtered = filtered.filter(log => log.level === logLevel);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(term) ||
        log.source.toLowerCase().includes(term) ||
        (log.metadata && JSON.stringify(log.metadata).toLowerCase().includes(term))
      );
    }

    setFilteredLogs(filtered);
  }, [logs, searchTerm, logLevel]);

  // Auto-scroll to bottom
  useEffect(() => {
    if (isAutoScrolling && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [filteredLogs, isAutoScrolling]);

  const handlePauseToggle = () => {
    setIsPaused(!isPaused);
  };

  const handleScrollToBottom = () => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  };

  const handleDownloadLogs = () => {
    const logText = filteredLogs
      .map(log => `[${log.timestamp}] [${log.level.toUpperCase()}] [${log.source}] ${log.message}`)
      .join('\n');
    
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs-${executionId || jobId}-${new Date().toISOString()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-red-600';
      case 'warn':
        return 'text-yellow-600';
      case 'info':
        return 'text-blue-600';
      case 'debug':
        return 'text-gray-500';
      default:
        return 'text-gray-700';
    }
  };

  const getLogLevelBg = (level: string) => {
    switch (level) {
      case 'error':
        return 'bg-red-50 border-l-red-500';
      case 'warn':
        return 'bg-yellow-50 border-l-yellow-500';
      case 'info':
        return 'bg-blue-50 border-l-blue-500';
      case 'debug':
        return 'bg-gray-50 border-l-gray-500';
      default:
        return 'bg-white border-l-gray-300';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <Card className="flex flex-col h-full">
      {/* Controls */}
      {showControls && (
        <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
            <span className="text-sm text-gray-500">
              ({filteredLogs.length} logs)
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <Input
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-48"
              icon={<MagnifyingGlassIcon className="w-4 h-4" />}
            />

            <Select
              value={logLevel}
              onChange={setLogLevel}
              options={[
                { value: 'all', label: 'All Levels' },
                { value: 'error', label: 'Error' },
                { value: 'warn', label: 'Warning' },
                { value: 'info', label: 'Info' },
                { value: 'debug', label: 'Debug' },
              ]}
              className="w-32"
            />

            <Button
              variant="outline"
              size="sm"
              onClick={handlePauseToggle}
              icon={isPaused ? <PlayIcon className="w-4 h-4" /> : <PauseIcon className="w-4 h-4" />}
            >
              {isPaused ? 'Resume' : 'Pause'}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleScrollToBottom}
              icon={<ArrowDownIcon className="w-4 h-4" />}
            >
              Bottom
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadLogs}
              icon={<DocumentArrowDownIcon className="w-4 h-4" />}
            >
              Download
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTimestamps(!showTimestamps)}
              icon={<AdjustmentsHorizontalIcon className="w-4 h-4" />}
            >
              Settings
            </Button>
          </div>
        </div>
      )}

      {/* Log Content */}
      <div
        ref={logContainerRef}
        className="flex-1 overflow-auto bg-gray-900 text-green-400 font-mono text-sm"
        style={{ height }}
        onScroll={(e) => {
          const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
          const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
          setIsAutoScrolling(isAtBottom);
        }}
      >
        {filteredLogs.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            {isPaused ? 'Logs paused' : 'Waiting for logs...'}
          </div>
        ) : (
          <div className="p-2">
            {filteredLogs.map((log, index) => (
              <div
                key={`${log.id}-${index}`}
                className={`border-l-2 pl-3 py-1 mb-1 ${getLogLevelBg(log.level)}`}
              >
                <div className="flex items-start space-x-2">
                  {showTimestamps && (
                    <span className="text-gray-400 text-xs whitespace-nowrap">
                      {formatTimestamp(log.timestamp)}
                    </span>
                  )}
                  <span className={`text-xs font-semibold uppercase ${getLogLevelColor(log.level)}`}>
                    {log.level}
                  </span>
                  <span className="text-gray-500 text-xs">
                    [{log.source}]
                  </span>
                  <span className="flex-1 text-white">
                    {log.message}
                  </span>
                </div>
                {showMetadata && log.metadata && (
                  <div className="mt-1 ml-4 text-xs text-gray-400">
                    <pre>{JSON.stringify(log.metadata, null, 2)}</pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-3 py-2 bg-gray-100 border-t border-gray-200 text-xs text-gray-600">
        <div className="flex items-center space-x-4">
          <span>Auto-scroll: {isAutoScrolling ? 'On' : 'Off'}</span>
          <span>Timestamps: {showTimestamps ? 'On' : 'Off'}</span>
          <span>Level: {logLevel}</span>
        </div>
        <div>
          Last update: {logs.length > 0 ? formatTimestamp(logs[logs.length - 1]?.timestamp) : 'Never'}
        </div>
      </div>
    </Card>
  );
};

export default LogViewer;
