import React, { useState, useEffect } from 'react';
import {
  PlayIcon,
  StopIcon,
  ArrowPathIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  CloudArrowDownIcon,
  CogIcon,
  ChartBarIcon,
  CalendarIcon,
  UserIcon,
  TagIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Tabs } from '../ui/Tabs';
import { Progress } from '../ui/Progress';
import { formatDistanceToNow, format } from 'date-fns';

interface JenkinsStyleProps {
  pipeline: any;
  execution?: any;
  onTriggerBuild?: () => void;
  onStopBuild?: () => void;
  onConfigurePipeline?: () => void;
}

const JenkinsStyle: React.FC<JenkinsStyleProps> = ({
  pipeline,
  execution,
  onTriggerBuild,
  onStopBuild,
  onConfigurePipeline,
}) => {
  const [activeTab, setActiveTab] = useState('status');
  const [buildHistory, setBuildHistory] = useState([]);
  const [expandedStages, setExpandedStages] = useState<Set<string>>(new Set());

  // Mock data for demonstration
  const mockPipeline = {
    id: 'pipeline-1',
    name: 'Frontend Build Pipeline',
    description: 'Builds and deploys the frontend application',
    repository: 'chainops/frontend',
    branch: 'main',
    lastBuild: {
      number: 42,
      status: 'success',
      duration: 180,
      timestamp: new Date(Date.now() - 300000),
    },
    health: 85, // Health percentage based on recent builds
    ...pipeline,
  };

  const mockExecution = {
    id: 'exec-42',
    number: 42,
    status: 'running',
    startedAt: new Date(Date.now() - 120000),
    estimatedDuration: 180,
    currentDuration: 120,
    progress: 67,
    triggeredBy: 'John Doe',
    commit: {
      sha: 'abc123def456',
      message: 'Update dependencies and fix build issues',
      author: 'John Doe',
      timestamp: new Date(Date.now() - 150000),
    },
    stages: [
      {
        id: 'checkout',
        name: 'Checkout',
        status: 'success',
        duration: 15,
        startedAt: new Date(Date.now() - 120000),
        steps: [
          { name: 'Git checkout', status: 'success', duration: 15 },
        ],
      },
      {
        id: 'build',
        name: 'Build',
        status: 'success',
        duration: 60,
        startedAt: new Date(Date.now() - 105000),
        steps: [
          { name: 'Install dependencies', status: 'success', duration: 30 },
          { name: 'Compile TypeScript', status: 'success', duration: 20 },
          { name: 'Bundle assets', status: 'success', duration: 10 },
        ],
      },
      {
        id: 'test',
        name: 'Test',
        status: 'running',
        duration: 45,
        startedAt: new Date(Date.now() - 45000),
        steps: [
          { name: 'Unit tests', status: 'success', duration: 25 },
          { name: 'Integration tests', status: 'running', duration: 20 },
          { name: 'E2E tests', status: 'pending', duration: 0 },
        ],
      },
      {
        id: 'deploy',
        name: 'Deploy',
        status: 'pending',
        duration: 0,
        startedAt: null,
        steps: [
          { name: 'Deploy to staging', status: 'pending', duration: 0 },
          { name: 'Smoke tests', status: 'pending', duration: 0 },
        ],
      },
    ],
    ...execution,
  };

  const mockBuildHistory = [
    { number: 42, status: 'running', duration: 120, timestamp: new Date(Date.now() - 120000), triggeredBy: 'John Doe' },
    { number: 41, status: 'success', duration: 175, timestamp: new Date(Date.now() - 3600000), triggeredBy: 'Jane Smith' },
    { number: 40, status: 'success', duration: 168, timestamp: new Date(Date.now() - 7200000), triggeredBy: 'Bob Wilson' },
    { number: 39, status: 'failed', duration: 95, timestamp: new Date(Date.now() - 10800000), triggeredBy: 'Alice Brown' },
    { number: 38, status: 'success', duration: 182, timestamp: new Date(Date.now() - 14400000), triggeredBy: 'John Doe' },
  ];

  useEffect(() => {
    setBuildHistory(mockBuildHistory);
  }, []);

  const currentExecution = execution || mockExecution;
  const currentPipeline = pipeline || mockPipeline;

  const getStatusIcon = (status: string, size = 'w-5 h-5') => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className={`${size} text-green-500`} />;
      case 'failed':
        return <XCircleIcon className={`${size} text-red-500`} />;
      case 'running':
        return <PlayIcon className={`${size} text-blue-500 animate-pulse`} />;
      case 'pending':
        return <ClockIcon className={`${size} text-gray-400`} />;
      case 'unstable':
        return <ExclamationTriangleIcon className={`${size} text-yellow-500`} />;
      default:
        return <ClockIcon className={`${size} text-gray-400`} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'running':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'pending':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'unstable':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const toggleStageExpansion = (stageId: string) => {
    const newExpanded = new Set(expandedStages);
    if (newExpanded.has(stageId)) {
      newExpanded.delete(stageId);
    } else {
      newExpanded.add(stageId);
    }
    setExpandedStages(newExpanded);
  };

  const statusTab = (
    <div className="space-y-6">
      {/* Build Header */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            {getStatusIcon(currentExecution.status, 'w-8 h-8')}
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Build #{currentExecution.number}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {currentPipeline.name}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {currentExecution.status === 'running' && (
              <Button
                variant="outline"
                onClick={onStopBuild}
                icon={<StopIcon className="w-4 h-4" />}
              >
                Stop Build
              </Button>
            )}
            <Button
              onClick={onTriggerBuild}
              icon={<PlayIcon className="w-4 h-4" />}
            >
              Build Now
            </Button>
            <Button
              variant="outline"
              onClick={onConfigurePipeline}
              icon={<CogIcon className="w-4 h-4" />}
            >
              Configure
            </Button>
          </div>
        </div>

        {/* Build Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="flex items-center space-x-2">
            <UserIcon className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Started by {currentExecution.triggeredBy}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <CalendarIcon className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {formatDistanceToNow(currentExecution.startedAt, { addSuffix: true })}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <ClockIcon className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Duration: {formatDuration(currentExecution.currentDuration)}
              {currentExecution.estimatedDuration && (
                <span className="text-gray-500">
                  {' '}/ ~{formatDuration(currentExecution.estimatedDuration)}
                </span>
              )}
            </span>
          </div>
        </div>

        {/* Progress Bar */}
        {currentExecution.status === 'running' && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Build Progress
              </span>
              <span className="text-sm text-gray-500">
                {currentExecution.progress}%
              </span>
            </div>
            <Progress value={currentExecution.progress} className="h-2" />
          </div>
        )}

        {/* Commit Info */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <CodeBracketIcon className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Commit Information
            </span>
          </div>
          <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <code className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                {currentExecution.commit.sha.substring(0, 8)}
              </code>
              <span>{currentExecution.commit.message}</span>
            </div>
            <div>
              by {currentExecution.commit.author} • {formatDistanceToNow(currentExecution.commit.timestamp, { addSuffix: true })}
            </div>
          </div>
        </div>
      </Card>

      {/* Build Stages */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Build Stages</h3>
        <div className="space-y-4">
          {currentExecution.stages.map((stage: any) => (
            <div
              key={stage.id}
              className={`border rounded-lg ${getStatusColor(stage.status)}`}
            >
              <div
                className="p-4 cursor-pointer"
                onClick={() => toggleStageExpansion(stage.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <button>
                      {expandedStages.has(stage.id) ? (
                        <ChevronDownIcon className="w-4 h-4" />
                      ) : (
                        <ChevronRightIcon className="w-4 h-4" />
                      )}
                    </button>
                    {getStatusIcon(stage.status)}
                    <span className="font-medium">{stage.name}</span>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm">
                    {stage.startedAt && (
                      <span>
                        Started {formatDistanceToNow(stage.startedAt, { addSuffix: true })}
                      </span>
                    )}
                    {stage.duration > 0 && (
                      <span>Duration: {formatDuration(stage.duration)}</span>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      icon={<DocumentTextIcon className="w-4 h-4" />}
                    >
                      Logs
                    </Button>
                  </div>
                </div>
              </div>

              {/* Stage Steps */}
              {expandedStages.has(stage.id) && (
                <div className="border-t bg-white dark:bg-gray-800 p-4">
                  <div className="space-y-2">
                    {stage.steps.map((step: any, index: number) => (
                      <div
                        key={index}
                        className="flex items-center justify-between py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded"
                      >
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(step.status, 'w-4 h-4')}
                          <span className="text-sm">{step.name}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                          {step.duration > 0 && (
                            <span>{formatDuration(step.duration)}</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  const historyTab = (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Build History</h2>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Health: {currentPipeline.health}%
          </span>
          <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
            <div
              className="h-full bg-green-500 rounded-full"
              style={{ width: `${currentPipeline.health}%` }}
            />
          </div>
        </div>
      </div>

      <Card>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Build
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Started
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Triggered By
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {buildHistory.map((build: any) => (
                <tr key={build.number} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(build.status, 'w-4 h-4')}
                      <span className="font-medium">#{build.number}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge
                      variant={
                        build.status === 'success' ? 'success' :
                        build.status === 'failed' ? 'error' :
                        build.status === 'running' ? 'info' : 'warning'
                      }
                    >
                      {build.status}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                    {formatDuration(build.duration)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                    {format(build.timestamp, 'MMM dd, HH:mm')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                    {build.triggeredBy}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.location.href = `/executions/${build.number}`}
                    >
                      View
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );

  const configTab = (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Pipeline Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-2">General</h4>
            <div className="space-y-2 text-sm">
              <div><strong>Name:</strong> {currentPipeline.name}</div>
              <div><strong>Repository:</strong> {currentPipeline.repository}</div>
              <div><strong>Branch:</strong> {currentPipeline.branch}</div>
              <div><strong>Description:</strong> {currentPipeline.description}</div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">Build Settings</h4>
            <div className="space-y-2 text-sm">
              <div><strong>Timeout:</strong> 30 minutes</div>
              <div><strong>Concurrent Builds:</strong> Disabled</div>
              <div><strong>Retry Count:</strong> 3</div>
              <div><strong>Quiet Period:</strong> 5 seconds</div>
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <Button
            onClick={onConfigurePipeline}
            icon={<CogIcon className="w-4 h-4" />}
          >
            Edit Configuration
          </Button>
        </div>
      </Card>
    </div>
  );

  const tabs = [
    {
      id: 'status',
      label: 'Build Status',
      icon: <PlayIcon className="w-4 h-4" />,
      content: statusTab,
    },
    {
      id: 'history',
      label: 'Build History',
      icon: <ClockIcon className="w-4 h-4" />,
      content: historyTab,
    },
    {
      id: 'config',
      label: 'Configuration',
      icon: <CogIcon className="w-4 h-4" />,
      content: configTab,
    },
  ];

  return (
    <div className="space-y-6">
      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </div>
  );
};

export default JenkinsStyle;
