import React, { useState, useEffect } from 'react';
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  CloudArrowDownIcon,
  EyeIcon,
  CommandLineIcon,
} from '@heroicons/react/24/outline';
import { Badge } from '../ui/Badge';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Tabs } from '../ui/Tabs';
import { formatDistanceToNow } from 'date-fns';

interface ExecutionViewProps {
  execution: any;
  onCancel?: () => void;
  onRetry?: () => void;
  onRerun?: () => void;
}

const GitHubActionsStyle: React.FC<ExecutionViewProps> = ({
  execution,
  onCancel,
  onRetry,
  onRerun,
}) => {
  const [expandedJobs, setExpandedJobs] = useState<Set<string>>(new Set());
  const [selectedJob, setSelectedJob] = useState<string | null>(null);
  const [logs, setLogs] = useState<Record<string, string[]>>({});
  const [autoScroll, setAutoScroll] = useState(true);

  // Mock data for demonstration
  const mockExecution = {
    id: 'exec-123',
    pipeline: { name: 'CI/CD Pipeline', repository: 'chainops/example' },
    status: 'running',
    branch: 'main',
    commit: {
      sha: 'abc123def',
      message: 'Add new feature',
      author: 'John Doe',
    },
    trigger: 'push',
    startedAt: new Date(Date.now() - 300000), // 5 minutes ago
    duration: 300,
    jobs: [
      {
        id: 'build',
        name: 'Build',
        status: 'success',
        startedAt: new Date(Date.now() - 280000),
        duration: 120,
        steps: [
          { name: 'Checkout code', status: 'success', duration: 5 },
          { name: 'Setup Node.js', status: 'success', duration: 15 },
          { name: 'Install dependencies', status: 'success', duration: 45 },
          { name: 'Build application', status: 'success', duration: 55 },
        ],
      },
      {
        id: 'test',
        name: 'Test',
        status: 'running',
        startedAt: new Date(Date.now() - 180000),
        duration: 180,
        steps: [
          { name: 'Run unit tests', status: 'success', duration: 90 },
          { name: 'Run integration tests', status: 'running', duration: 90 },
          { name: 'Generate coverage report', status: 'pending', duration: 0 },
        ],
      },
      {
        id: 'deploy',
        name: 'Deploy to Staging',
        status: 'pending',
        startedAt: null,
        duration: 0,
        steps: [
          { name: 'Deploy to staging', status: 'pending', duration: 0 },
          { name: 'Run smoke tests', status: 'pending', duration: 0 },
        ],
      },
    ],
  };

  const currentExecution = execution || mockExecution;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'failure':
      case 'failed':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'running':
        return <PlayIcon className="w-5 h-5 text-blue-500 animate-pulse" />;
      case 'pending':
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
      case 'cancelled':
        return <PauseIcon className="w-5 h-5 text-yellow-500" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="success">Success</Badge>;
      case 'failure':
      case 'failed':
        return <Badge variant="error">Failed</Badge>;
      case 'running':
        return <Badge variant="info">Running</Badge>;
      case 'pending':
        return <Badge variant="warning">Pending</Badge>;
      case 'cancelled':
        return <Badge variant="warning">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const toggleJobExpansion = (jobId: string) => {
    const newExpanded = new Set(expandedJobs);
    if (newExpanded.has(jobId)) {
      newExpanded.delete(jobId);
    } else {
      newExpanded.add(jobId);
    }
    setExpandedJobs(newExpanded);
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {getStatusIcon(currentExecution.status)}
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {currentExecution.pipeline.name}
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {currentExecution.pipeline.repository} • {currentExecution.branch}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {getStatusBadge(currentExecution.status)}
            {currentExecution.status === 'running' && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCancel}
                icon={<StopIcon className="w-4 h-4" />}
              >
                Cancel
              </Button>
            )}
            {(currentExecution.status === 'failed' || currentExecution.status === 'cancelled') && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                icon={<ArrowPathIcon className="w-4 h-4" />}
              >
                Re-run failed jobs
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={onRerun}
              icon={<PlayIcon className="w-4 h-4" />}
            >
              Re-run all jobs
            </Button>
          </div>
        </div>

        {/* Commit info */}
        <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center space-x-2">
            <span>Commit:</span>
            <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
              {currentExecution.commit.sha.substring(0, 7)}
            </code>
            <span>{currentExecution.commit.message}</span>
          </div>
          <div>by {currentExecution.commit.author}</div>
          <div>
            {currentExecution.startedAt && formatDistanceToNow(currentExecution.startedAt, { addSuffix: true })}
          </div>
          {currentExecution.duration > 0 && (
            <div>Duration: {formatDuration(currentExecution.duration)}</div>
          )}
        </div>
      </div>

      {/* Jobs */}
      <div className="space-y-4">
        {currentExecution.jobs.map((job: any) => (
          <Card key={job.id} className="overflow-hidden">
            <div
              className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              onClick={() => toggleJobExpansion(job.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <button className="flex items-center space-x-2">
                    {expandedJobs.has(job.id) ? (
                      <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                    ) : (
                      <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                    )}
                    {getStatusIcon(job.status)}
                  </button>
                  
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      {job.name}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      {job.startedAt && (
                        <span>
                          Started {formatDistanceToNow(job.startedAt, { addSuffix: true })}
                        </span>
                      )}
                      {job.duration > 0 && (
                        <span>Duration: {formatDuration(job.duration)}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {getStatusBadge(job.status)}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedJob(job.id);
                    }}
                    icon={<DocumentTextIcon className="w-4 h-4" />}
                  >
                    Logs
                  </Button>
                </div>
              </div>
            </div>

            {/* Job Steps */}
            {expandedJobs.has(job.id) && (
              <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="p-4 space-y-2">
                  {job.steps.map((step: any, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between py-2 px-3 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600"
                    >
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(step.status)}
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {step.name}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                        {step.duration > 0 && (
                          <span>{formatDuration(step.duration)}</span>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedJob(`${job.id}-${index}`)}
                          icon={<EyeIcon className="w-3 h-3" />}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>

      {/* Log Viewer Modal */}
      {selectedJob && (
        <LogViewerModal
          jobId={selectedJob}
          onClose={() => setSelectedJob(null)}
          logs={logs[selectedJob] || []}
          autoScroll={autoScroll}
          onAutoScrollChange={setAutoScroll}
        />
      )}
    </div>
  );
};

// Log Viewer Modal Component
interface LogViewerModalProps {
  jobId: string;
  onClose: () => void;
  logs: string[];
  autoScroll: boolean;
  onAutoScrollChange: (enabled: boolean) => void;
}

const LogViewerModal: React.FC<LogViewerModalProps> = ({
  jobId,
  onClose,
  logs,
  autoScroll,
  onAutoScrollChange,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const logContainerRef = React.useRef<HTMLDivElement>(null);

  // Mock logs for demonstration
  const mockLogs = [
    '2024-01-15 10:30:00 | Starting job execution...',
    '2024-01-15 10:30:01 | Checking out code from repository',
    '2024-01-15 10:30:02 | Setting up Node.js environment',
    '2024-01-15 10:30:05 | Installing dependencies...',
    '2024-01-15 10:30:15 | npm install completed successfully',
    '2024-01-15 10:30:16 | Running build process...',
    '2024-01-15 10:30:45 | Build completed successfully',
    '2024-01-15 10:30:46 | Running tests...',
    '2024-01-15 10:31:30 | All tests passed ✓',
    '2024-01-15 10:31:31 | Job completed successfully',
  ];

  const displayLogs = logs.length > 0 ? logs : mockLogs;
  const filteredLogs = searchTerm
    ? displayLogs.filter(log => log.toLowerCase().includes(searchTerm.toLowerCase()))
    : displayLogs;

  useEffect(() => {
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [filteredLogs, autoScroll]);

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-gray-500 bg-opacity-75" onClick={onClose} />
      
      <div className="absolute inset-4 bg-white dark:bg-gray-800 rounded-lg shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <CommandLineIcon className="w-5 h-5 text-gray-500" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Job Logs - {jobId}
            </h3>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={autoScroll}
                onChange={(e) => onAutoScrollChange(e.target.checked)}
                className="rounded"
              />
              <span className="text-gray-700 dark:text-gray-300">Auto-scroll</span>
            </label>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const logText = filteredLogs.join('\n');
                navigator.clipboard.writeText(logText);
              }}
              icon={<CloudArrowDownIcon className="w-4 h-4" />}
            >
              Copy
            </Button>
            
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>

        {/* Logs */}
        <div
          ref={logContainerRef}
          className="flex-1 overflow-auto bg-gray-900 text-green-400 font-mono text-sm p-4"
        >
          {filteredLogs.map((log, index) => (
            <div key={index} className="whitespace-pre-wrap hover:bg-gray-800 px-2 py-1 rounded">
              <span className="text-gray-500 mr-2">{index + 1}</span>
              {log}
            </div>
          ))}
          
          {filteredLogs.length === 0 && (
            <div className="text-gray-500 text-center py-8">
              No logs found matching "{searchTerm}"
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GitHubActionsStyle;
