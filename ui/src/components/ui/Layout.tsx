import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import {
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  RocketLaunchIcon,
  PlayIcon,
  CloudArrowUpIcon,
  KeyIcon,
  ChartBarIcon,
  UserGroupIcon,
  BellIcon,
  LinkIcon,
  CogIcon,
  QuestionMarkCircleIcon,
  MagnifyingGlassIcon,
  CommandLineIcon,
  SunIcon,
  MoonIcon,
} from '@heroicons/react/24/outline';
import { Button } from './Button';
import { Input } from './Input';
import { Avatar } from './Avatar';
import { Badge } from './Badge';
import { Dropdown } from './Dropdown';

interface LayoutProps {
  children?: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();
  const navigate = useNavigate();

  // Navigation items
  const navigation = [
    { name: 'Dashboard', href: '/', icon: HomeIcon, current: location.pathname === '/' },
    { name: 'Pipelines', href: '/pipelines', icon: RocketLaunchIcon, current: location.pathname.startsWith('/pipelines') },
    { name: 'Executions', href: '/executions', icon: PlayIcon, current: location.pathname.startsWith('/executions') },
    { name: 'Deployments', href: '/deployments', icon: CloudArrowUpIcon, current: location.pathname.startsWith('/deployments') },
    { name: 'Secrets', href: '/secrets', icon: KeyIcon, current: location.pathname.startsWith('/secrets') },
    { name: 'Analytics', href: '/analytics', icon: ChartBarIcon, current: location.pathname.startsWith('/analytics') },
    { name: 'Users', href: '/users', icon: UserGroupIcon, current: location.pathname.startsWith('/users') },
    { name: 'Notifications', href: '/notifications', icon: BellIcon, current: location.pathname.startsWith('/notifications') },
    { name: 'Integrations', href: '/integrations', icon: LinkIcon, current: location.pathname.startsWith('/integrations') },
    { name: 'Settings', href: '/settings', icon: CogIcon, current: location.pathname.startsWith('/settings') },
  ];

  // Quick actions
  const quickActions = [
    { name: 'New Pipeline', action: () => navigate('/pipelines/new'), icon: RocketLaunchIcon },
    { name: 'Run Pipeline', action: () => navigate('/pipelines'), icon: PlayIcon },
    { name: 'View Logs', action: () => navigate('/executions'), icon: CommandLineIcon },
  ];

  useEffect(() => {
    // Load theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      setDarkMode(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    if (!darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''}`}>
      <div className="bg-gray-50 dark:bg-gray-900 min-h-screen">
        {/* Mobile sidebar */}
        <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
          <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-800 shadow-xl">
            <div className="flex h-16 items-center justify-between px-4">
              <img className="h-8 w-auto" src="/logo.svg" alt="ChainOps" />
              <button onClick={() => setSidebarOpen(false)}>
                <XMarkIcon className="h-6 w-6 text-gray-400" />
              </button>
            </div>
            <nav className="flex-1 space-y-1 px-2 py-4">
              {navigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    item.current
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </a>
              ))}
            </nav>
          </div>
        </div>

        {/* Desktop sidebar */}
        <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
          <div className="flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm">
            {/* Logo */}
            <div className="flex h-16 items-center px-4 border-b border-gray-200 dark:border-gray-700">
              <img className="h-8 w-auto" src="/logo.svg" alt="ChainOps" />
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">ChainOps</span>
            </div>

            {/* Navigation */}
            <nav className="flex-1 space-y-1 px-2 py-4">
              {navigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                    item.current
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                  {item.name}
                </a>
              ))}
            </nav>

            {/* Quick Actions */}
            <div className="px-2 py-4 border-t border-gray-200 dark:border-gray-700">
              <h3 className="px-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Quick Actions
              </h3>
              <div className="mt-2 space-y-1">
                {quickActions.map((action) => (
                  <button
                    key={action.name}
                    onClick={action.action}
                    className="group flex items-center w-full px-2 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    <action.icon className="mr-3 h-4 w-4 flex-shrink-0" />
                    {action.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:pl-64 flex flex-col flex-1">
          {/* Top navigation */}
          <div className="sticky top-0 z-40 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
              {/* Mobile menu button */}
              <button
                className="lg:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Bars3Icon className="h-6 w-6 text-gray-500" />
              </button>

              {/* Search */}
              <div className="flex-1 max-w-lg mx-4">
                <form onSubmit={handleSearch} className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    type="text"
                    placeholder="Search pipelines, executions, users..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-full"
                  />
                </form>
              </div>

              {/* Right side */}
              <div className="flex items-center space-x-4">
                {/* Theme toggle */}
                <button
                  onClick={toggleDarkMode}
                  className="p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                >
                  {darkMode ? (
                    <SunIcon className="h-5 w-5" />
                  ) : (
                    <MoonIcon className="h-5 w-5" />
                  )}
                </button>

                {/* Notifications */}
                <button className="relative p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                  <BellIcon className="h-5 w-5" />
                  <Badge className="absolute -top-1 -right-1 h-4 w-4 text-xs">3</Badge>
                </button>

                {/* Help */}
                <button className="p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                  <QuestionMarkCircleIcon className="h-5 w-5" />
                </button>

                {/* User menu */}
                <Dropdown
                  trigger={
                    <Avatar
                      src="/avatar.jpg"
                      alt="User"
                      fallback="U"
                      size="sm"
                      className="cursor-pointer"
                    />
                  }
                  items={[
                    { label: 'Profile', href: '/profile' },
                    { label: 'Settings', href: '/settings' },
                    { label: 'API Keys', href: '/api-keys' },
                    { type: 'divider' },
                    { label: 'Sign out', href: '/logout' },
                  ]}
                />
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className="flex-1">
            <div className="py-6">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {children || <Outlet />}
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default Layout;
