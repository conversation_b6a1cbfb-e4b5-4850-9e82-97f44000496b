
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  RocketLaunchIcon,
  Squares2X2Icon,
  CloudArrowUpIcon,
  ChartBarIcon,
  ServerIcon,
  Cog6ToothIcon,
  CreditCardIcon,
  BellIcon,
  UserIcon,
  MagnifyingGlassIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon,
  PlusIcon,
  CommandLineIcon,
  BookOpenIcon,
  QuestionMarkCircleIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Overview', href: '/', icon: HomeIcon, description: 'Dashboard and recent activity' },
  { name: 'Pipelines', href: '/pipelines', icon: RocketLaunchIcon, description: 'CI/CD pipeline management' },
  { name: 'Projects', href: '/projects', icon: Squares2X2Icon, description: 'Repository and project settings' },
  { name: 'Deployments', href: '/deployments', icon: CloudArrowUpIcon, description: 'Application deployments' },
  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon, description: 'Performance metrics and insights' },
  { name: 'Runners', href: '/runners', icon: ServerIcon, description: 'Execution environment management' },
];

const secondaryNavigation = [
  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },
  { name: 'Documentation', href: '/docs', icon: BookOpenIcon },
  { name: 'Support', href: '/support', icon: QuestionMarkCircleIcon },
  { name: 'Plan', href: '/plan', icon: CreditCardIcon },
];

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    if (href === '/pipelines') {
      return location.pathname === '/pipelines' || location.pathname.startsWith('/pipeline');
    }
    return location.pathname.startsWith(href);
  };

  const currentPage = navigation.find(item => isActive(item.href)) ||
                     secondaryNavigation.find(item => isActive(item.href));

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-sm">CO</span>
              </div>
              <span className="ml-3 text-lg font-bold text-gray-900">ChainOps</span>
            </div>
            <button onClick={() => setSidebarOpen(false)} className="text-gray-400 hover:text-gray-600">
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 px-4 py-4 space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={`nav-item ${isActive(item.href) ? 'nav-item-active' : 'nav-item-inactive'}`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-72 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm">
          {/* Logo */}
          <div className="flex items-center px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">CO</span>
              </div>
              <div className="ml-3">
                <span className="text-xl font-bold text-gray-900">ChainOps</span>
                <div className="text-xs text-gray-500">CI/CD Platform</div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="px-4 py-3 border-b border-gray-200">
            <button className="btn btn-primary btn-sm w-full">
              <PlusIcon className="w-4 h-4 mr-2" />
              New Pipeline
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`nav-item group ${isActive(item.href) ? 'nav-item-active' : 'nav-item-inactive'}`}
                  title={item.description}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  <span className="flex-1">{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Secondary Navigation */}
          <div className="px-4 py-4 border-t border-gray-200">
            <div className="space-y-1">
              {secondaryNavigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`nav-item ${isActive(item.href) ? 'nav-item-active' : 'nav-item-inactive'}`}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-72">
        {/* Top header */}
        <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
          <div className="flex items-center justify-between px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 text-gray-400 hover:text-gray-600 rounded-md"
              >
                <Bars3Icon className="h-6 w-6" />
              </button>

              <div className="flex items-center space-x-3">
                <h1 className="text-2xl font-bold text-gray-900">
                  {currentPage?.name || 'ChainOps'}
                </h1>
                {navigation.find(item => isActive(item.href))?.description && (
                  <span className="hidden sm:block text-sm text-gray-500">
                    {navigation.find(item => isActive(item.href))?.description}
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="hidden md:block relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search pipelines, projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="form-input pl-10 pr-4 py-2 w-64 text-sm"
                />
                {searchQuery && (
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <kbd className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100 border border-gray-200 rounded">
                      ⌘K
                    </kbd>
                  </div>
                )}
              </div>

              {/* Quick Actions */}
              <button className="btn btn-ghost btn-sm">
                <CommandLineIcon className="w-4 h-4 mr-2" />
                CLI
              </button>

              {/* Notifications */}
              <button className="relative p-2 text-gray-400 hover:text-gray-600 rounded-md">
                <BellIcon className="h-5 w-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>

              {/* User menu */}
              <div className="relative">
                <button className="flex items-center space-x-2 p-2 text-gray-700 hover:text-gray-900 rounded-md">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <UserIcon className="h-5 w-5 text-gray-600" />
                  </div>
                  <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1">
          <div className="animate-fade-in">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}


