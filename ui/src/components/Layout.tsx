[plugin:vite:import-analysis] Failed to resolve import "../components/ui/LoadingSpinner" from "src/pages/Projects.tsx". Does the file exist?
/Users/<USER>/Developer/ChainOps/ui/src/pages/Projects.tsx:11:31
26 |    XCircleIcon
27 |  } from "@heroicons/react/24/outline";
28 |  import { LoadingSpinner } from "../components/ui/LoadingSpinner";
   |                                  ^
29 |  export default function Projects() {
30 |    _s();
    at TransformPluginContext._formatError (file:///Users/<USER>/Developer/ChainOps/ui/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41)
    at TransformPluginContext.error (file:///Users/<USER>/Developer/ChainOps/ui/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16)
    at normalizeUrl (file:///Users/<USER>/Developer/ChainOps/ui/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async file:///Users/<USER>/Developer/ChainOps/ui/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39
    at async Promise.all (index 6)
    at async TransformPluginContext.transform (file:///Users/<USER>/Developer/ChainOps/ui/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7)
    at async PluginContainer.transform (file:///Users/<USER>/Developer/ChainOps/ui/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18)
    at async loadAndTransform (file:///Users/<USER>/Developer/ChainOps/ui/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27)
    at async viteTransformMiddleware (file:///Users/<USER>/Developer/ChainOps/ui/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:62105:24
Click outside, press Esc key, or fix the code to dismiss.
You can also disable this overlay by setting server.hmr.overlay to false in vite.config.ts.
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  CogIcon,
  PlayIcon,
  ClockIcon,
  RocketLaunchIcon,
  ChartBarIcon,
  ChevronDownIcon,
  Squares2X2Icon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';

const navigation = [
  { name: 'Home', href: '/', icon: HomeIcon },
  { name: 'Pipelines', href: '/pipelines', icon: PlayIcon, active: true },
  { name: 'Projects', href: '/projects', icon: Squares2X2Icon },
  { name: 'Deploys', href: '/deployments', icon: RocketLaunchIcon },
  { name: 'Insights', href: '/analytics', icon: ChartBarIcon },
  { name: 'Runners', href: '/runners', icon: ClockIcon },
  { name: 'Settings', href: '/settings', icon: CogIcon },
  { name: 'Plan', href: '/plan', icon: CurrencyDollarIcon },
];

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const location = useLocation();
  const [selectedProject, setSelectedProject] = useState('project-name');

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="flex flex-col w-64 bg-white shadow-sm border-r border-gray-200">
        <div className="flex items-center px-4 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">CO</span>
            </div>
            <span className="ml-3 text-lg font-semibold text-gray-900">ChainOps</span>
            <ChevronDownIcon className="ml-2 h-4 w-4 text-gray-400" />
          </div>
        </div>

        <nav className="flex-1 px-3 py-4 space-y-1">
          {navigation.map((item) => {
            const isActive = location.pathname === item.href ||
              (item.name === 'Pipelines' && location.pathname === '/') ||
              (item.href !== '/' && location.pathname.startsWith(item.href));

            return (
              <Link
                key={item.name}
                to={item.href}
                className={clsx(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-gray-100 text-gray-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                )}
              >
                <item.icon className={clsx(
                  'w-5 h-5 mr-3',
                  isActive ? 'text-gray-700' : 'text-gray-400'
                )} />
                {item.name}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Squares2X2Icon className="h-6 w-6 text-gray-400" />
                <h1 className="text-2xl font-semibold text-gray-900">{getPageTitle(location.pathname)}</h1>
              </div>

              {/* Top filters for pipelines page */}
              {(location.pathname === '/pipelines' || location.pathname === '/') && (
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Squares2X2Icon className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Everyone's Pipelines</span>
                  </div>

                  <div className="flex items-center space-x-2 border border-gray-300 rounded-md px-3 py-1 cursor-pointer hover:bg-gray-50">
                    <span className="text-sm text-gray-700">{selectedProject}</span>
                    <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                  </div>

                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                    <span className="text-sm text-gray-600">Main</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-y-auto bg-gray-50">
          {children}
        </main>
      </div>
    </div>
  );
}

function getPageTitle(pathname: string): string {
  if (pathname === '/' || pathname.startsWith('/pipelines')) return 'All Pipelines';
  if (pathname.startsWith('/projects')) return 'Projects';
  if (pathname.startsWith('/deployments')) return 'Deploys';
  if (pathname.startsWith('/analytics')) return 'Insights';
  if (pathname.startsWith('/runners')) return 'Runners';
  if (pathname.startsWith('/settings')) return 'Settings';
  if (pathname.startsWith('/plan')) return 'Plan';
  return 'Home';
}
