import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  RocketLaunchIcon,
  FolderIcon,
  CloudArrowUpIcon,
  ChartBarIcon,
  ServerIcon,
  Cog6ToothIcon,
  CreditCardIcon,
  Bars3Icon,
  ChevronLeftIcon,
} from '@heroicons/react/24/outline';

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  darkMode: boolean;
}

const navigation = [
  { name: 'Home', href: '/', icon: HomeIcon },
  { name: 'Pipelines', href: '/pipelines', icon: RocketLaunchIcon },
  { name: 'Projects', href: '/projects', icon: FolderIcon },
  { name: 'Deploys', href: '/deployments', icon: CloudArrowUpIcon },
  { name: 'Insights', href: '/insights', icon: ChartBarIcon },
  { name: 'Runners', href: '/runners', icon: ServerIcon },
  { name: 'Setting<PERSON>', href: '/settings', icon: Cog6ToothIcon },
  { name: 'Plan', href: '/plan', icon: CreditCardIcon },
];

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onToggle, darkMode }) => {
  const location = useLocation();

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div
      data-testid="sidebar"
      className={`fixed left-0 top-0 h-full z-40 transition-all duration-300 ${
        collapsed ? 'w-16' : 'w-64'
      } ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-r shadow-lg`}
    >
      
      {/* Header */}
      <div className={`flex items-center justify-between p-4 border-b ${
        darkMode ? 'border-gray-700' : 'border-gray-200'
      }`}>
        {!collapsed && (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CO</span>
            </div>
            <span className={`text-lg font-semibold ${
              darkMode ? 'text-white' : 'text-gray-900'
            }`}>
              ChainOps
            </span>
          </div>
        )}
        <button
          onClick={onToggle}
          className={`p-2 rounded-lg transition-colors ${
            darkMode 
              ? 'hover:bg-gray-700 text-gray-300 hover:text-white' 
              : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
          }`}
        >
          {collapsed ? (
            <Bars3Icon className="w-5 h-5" />
          ) : (
            <ChevronLeftIcon className="w-5 h-5" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);
          
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 group ${
                active
                  ? darkMode
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-blue-50 text-blue-700 border border-blue-200'
                  : darkMode
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
              title={collapsed ? item.name : undefined}
            >
              <Icon className={`w-5 h-5 flex-shrink-0 ${
                active && !darkMode ? 'text-blue-600' : ''
              }`} />
              {!collapsed && (
                <span className="font-medium">{item.name}</span>
              )}
              {collapsed && active && (
                <div className="absolute left-14 bg-gray-900 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity">
                  {item.name}
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* User Profile Section */}
      {!collapsed && (
        <div className={`absolute bottom-0 left-0 right-0 p-4 border-t ${
          darkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-sm">AU</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className={`text-sm font-medium truncate ${
                darkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Admin User
              </p>
              <p className={`text-xs truncate ${
                darkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                <EMAIL>
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
