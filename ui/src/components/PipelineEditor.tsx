import React, { useState, useCallback, useEffect } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  Connection,
  ConnectionMode,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Button } from './ui/Button';
import { Card } from './ui/Card';
import { Input } from './ui/Input';
import { Select } from './ui/Select';
import { Textarea } from './ui/Textarea';
import { Modal } from './ui/Modal';
import { PlusIcon, PlayIcon, SaveIcon, CodeIcon } from '@heroicons/react/24/outline';

interface PipelineStep {
  id: string;
  name: string;
  type: 'build' | 'test' | 'deploy' | 'approval' | 'notification';
  script?: string;
  image?: string;
  environment?: Record<string, string>;
  dependsOn?: string[];
  condition?: string;
}

interface PipelineEditorProps {
  initialPipeline?: any;
  onSave: (pipeline: any) => void;
  onExecute?: (pipeline: any) => void;
}

const nodeTypes = {
  build: { label: 'Build', color: '#3B82F6', icon: '🔨' },
  test: { label: 'Test', color: '#10B981', icon: '🧪' },
  deploy: { label: 'Deploy', color: '#F59E0B', icon: '🚀' },
  approval: { label: 'Approval', color: '#8B5CF6', icon: '✋' },
  notification: { label: 'Notification', color: '#EF4444', icon: '📢' },
};

const PipelineEditor: React.FC<PipelineEditorProps> = ({
  initialPipeline,
  onSave,
  onExecute,
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [showStepModal, setShowStepModal] = useState(false);
  const [showYamlView, setShowYamlView] = useState(false);
  const [pipelineName, setPipelineName] = useState('');
  const [pipelineDescription, setPipelineDescription] = useState('');

  // Load initial pipeline
  useEffect(() => {
    if (initialPipeline) {
      setPipelineName(initialPipeline.name || '');
      setPipelineDescription(initialPipeline.description || '');
      // Convert pipeline to nodes and edges
      loadPipelineToEditor(initialPipeline);
    }
  }, [initialPipeline]);

  const loadPipelineToEditor = (pipeline: any) => {
    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];

    if (pipeline.stages) {
      pipeline.stages.forEach((stage: any, stageIndex: number) => {
        stage.steps?.forEach((step: any, stepIndex: number) => {
          const nodeId = `${stageIndex}-${stepIndex}`;
          newNodes.push({
            id: nodeId,
            type: 'default',
            position: { x: stageIndex * 200, y: stepIndex * 100 },
            data: {
              label: step.name,
              type: step.type || 'build',
              step: step,
            },
            style: {
              background: nodeTypes[step.type as keyof typeof nodeTypes]?.color || '#3B82F6',
              color: 'white',
              border: '1px solid #ccc',
              borderRadius: '8px',
              padding: '10px',
            },
          });

          // Add edges based on dependencies
          if (step.dependsOn) {
            step.dependsOn.forEach((dep: string) => {
              const depNodeId = findNodeIdByName(dep, newNodes);
              if (depNodeId) {
                newEdges.push({
                  id: `${depNodeId}-${nodeId}`,
                  source: depNodeId,
                  target: nodeId,
                  type: 'smoothstep',
                });
              }
            });
          }
        });
      });
    }

    setNodes(newNodes);
    setEdges(newEdges);
  };

  const findNodeIdByName = (name: string, nodeList: Node[]): string | null => {
    const node = nodeList.find(n => n.data.step.name === name);
    return node ? node.id : null;
  };

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const addNewStep = (type: keyof typeof nodeTypes) => {
    const newNode: Node = {
      id: `step-${Date.now()}`,
      type: 'default',
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: {
        label: `New ${nodeTypes[type].label}`,
        type: type,
        step: {
          id: `step-${Date.now()}`,
          name: `New ${nodeTypes[type].label}`,
          type: type,
          script: type === 'build' ? 'echo "Building..."' : 
                  type === 'test' ? 'echo "Testing..."' : 
                  type === 'deploy' ? 'echo "Deploying..."' : '',
          image: 'alpine:latest',
          environment: {},
        },
      },
      style: {
        background: nodeTypes[type].color,
        color: 'white',
        border: '1px solid #ccc',
        borderRadius: '8px',
        padding: '10px',
      },
    };

    setNodes((nds) => nds.concat(newNode));
  };

  const onNodeClick = (event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setShowStepModal(true);
  };

  const updateSelectedNode = (updatedStep: PipelineStep) => {
    if (!selectedNode) return;

    setNodes((nds) =>
      nds.map((node) =>
        node.id === selectedNode.id
          ? {
              ...node,
              data: {
                ...node.data,
                label: updatedStep.name,
                step: updatedStep,
              },
            }
          : node
      )
    );
    setShowStepModal(false);
    setSelectedNode(null);
  };

  const generateYaml = () => {
    const stages: any[] = [];
    const stageMap = new Map<number, any>();

    // Group nodes by x position (stages)
    nodes.forEach((node) => {
      const stageIndex = Math.floor(node.position.x / 200);
      if (!stageMap.has(stageIndex)) {
        stageMap.set(stageIndex, {
          name: `stage-${stageIndex}`,
          steps: [],
        });
      }
      stageMap.get(stageIndex).steps.push(node.data.step);
    });

    // Convert to array
    Array.from(stageMap.keys())
      .sort((a, b) => a - b)
      .forEach((key) => {
        stages.push(stageMap.get(key));
      });

    return {
      name: pipelineName,
      description: pipelineDescription,
      stages: stages,
    };
  };

  const handleSave = () => {
    const pipeline = generateYaml();
    onSave(pipeline);
  };

  const handleExecute = () => {
    if (onExecute) {
      const pipeline = generateYaml();
      onExecute(pipeline);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1 mr-4">
            <Input
              placeholder="Pipeline Name"
              value={pipelineName}
              onChange={(e) => setPipelineName(e.target.value)}
              className="mb-2"
            />
            <Input
              placeholder="Pipeline Description"
              value={pipelineDescription}
              onChange={(e) => setPipelineDescription(e.target.value)}
            />
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowYamlView(!showYamlView)}
              icon={<CodeIcon className="w-4 h-4" />}
            >
              {showYamlView ? 'Visual' : 'YAML'}
            </Button>
            <Button
              variant="outline"
              onClick={handleSave}
              icon={<SaveIcon className="w-4 h-4" />}
            >
              Save
            </Button>
            {onExecute && (
              <Button
                onClick={handleExecute}
                icon={<PlayIcon className="w-4 h-4" />}
              >
                Execute
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-200 p-2">
        <div className="flex space-x-2">
          {Object.entries(nodeTypes).map(([type, config]) => (
            <Button
              key={type}
              variant="outline"
              size="sm"
              onClick={() => addNewStep(type as keyof typeof nodeTypes)}
              className="flex items-center space-x-1"
            >
              <span>{config.icon}</span>
              <span>{config.label}</span>
              <PlusIcon className="w-3 h-3" />
            </Button>
          ))}
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1">
        {showYamlView ? (
          <div className="h-full p-4">
            <pre className="bg-gray-900 text-green-400 p-4 rounded-lg h-full overflow-auto">
              {JSON.stringify(generateYaml(), null, 2)}
            </pre>
          </div>
        ) : (
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            connectionMode={ConnectionMode.Loose}
            fitView
          >
            <Background />
            <Controls />
            <MiniMap />
            <Panel position="top-right">
              <Card className="p-2">
                <p className="text-sm text-gray-600">
                  Drag to create connections between steps
                </p>
              </Card>
            </Panel>
          </ReactFlow>
        )}
      </div>

      {/* Step Configuration Modal */}
      {showStepModal && selectedNode && (
        <StepConfigModal
          step={selectedNode.data.step}
          onSave={updateSelectedNode}
          onClose={() => {
            setShowStepModal(false);
            setSelectedNode(null);
          }}
        />
      )}
    </div>
  );
};

// Step Configuration Modal Component
interface StepConfigModalProps {
  step: PipelineStep;
  onSave: (step: PipelineStep) => void;
  onClose: () => void;
}

const StepConfigModal: React.FC<StepConfigModalProps> = ({
  step,
  onSave,
  onClose,
}) => {
  const [formData, setFormData] = useState<PipelineStep>(step);

  const handleSave = () => {
    onSave(formData);
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={`Configure ${formData.name}`}
      size="lg"
    >
      <div className="space-y-4">
        <Input
          label="Step Name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
        />

        <Select
          label="Step Type"
          value={formData.type}
          onChange={(value) => setFormData({ ...formData, type: value as any })}
          options={Object.entries(nodeTypes).map(([key, config]) => ({
            value: key,
            label: config.label,
          }))}
        />

        <Input
          label="Docker Image"
          value={formData.image || ''}
          onChange={(e) => setFormData({ ...formData, image: e.target.value })}
          placeholder="alpine:latest"
        />

        <Textarea
          label="Script"
          value={formData.script || ''}
          onChange={(e) => setFormData({ ...formData, script: e.target.value })}
          placeholder="echo 'Hello World'"
          rows={4}
        />

        <Input
          label="Condition"
          value={formData.condition || ''}
          onChange={(e) => setFormData({ ...formData, condition: e.target.value })}
          placeholder="${{ success() }}"
        />

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Step
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default PipelineEditor;
