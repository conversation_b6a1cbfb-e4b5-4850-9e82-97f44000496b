import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import {
  PlayIcon,
  PauseIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CloudArrowUpIcon,
  UserIcon,
  CogIcon,
  CommandLineIcon,
} from '@heroicons/react/24/outline';
import { Badge } from '../ui/Badge';

// Status colors and icons
const getStatusConfig = (status: string) => {
  switch (status) {
    case 'success':
      return { color: 'bg-green-500', icon: CheckCircleIcon, textColor: 'text-green-700' };
    case 'failure':
    case 'failed':
      return { color: 'bg-red-500', icon: XCircleIcon, textColor: 'text-red-700' };
    case 'running':
      return { color: 'bg-blue-500', icon: PlayIcon, textColor: 'text-blue-700' };
    case 'pending':
      return { color: 'bg-gray-400', icon: ClockIcon, textColor: 'text-gray-700' };
    case 'cancelled':
      return { color: 'bg-yellow-500', icon: PauseIcon, textColor: 'text-yellow-700' };
    case 'waiting_approval':
      return { color: 'bg-orange-500', icon: ExclamationTriangleIcon, textColor: 'text-orange-700' };
    default:
      return { color: 'bg-gray-400', icon: ClockIcon, textColor: 'text-gray-700' };
  }
};

// Base node wrapper
const BaseNode: React.FC<{
  children: React.ReactNode;
  status?: string;
  selected?: boolean;
  className?: string;
}> = ({ children, status = 'pending', selected, className = '' }) => {
  const statusConfig = getStatusConfig(status);
  
  return (
    <div
      className={`
        relative bg-white dark:bg-gray-800 border-2 rounded-lg shadow-lg min-w-[200px]
        ${selected ? 'border-blue-500 shadow-blue-200' : 'border-gray-200 dark:border-gray-700'}
        hover:shadow-xl transition-all duration-200
        ${className}
      `}
    >
      {/* Status indicator */}
      <div className={`absolute -top-2 -right-2 w-4 h-4 rounded-full ${statusConfig.color} border-2 border-white dark:border-gray-800`} />
      
      {children}
    </div>
  );
};

// Stage Node
export const StageNode: React.FC<NodeProps> = ({ data, selected }) => {
  const statusConfig = getStatusConfig(data.status);
  const StatusIcon = statusConfig.icon;

  return (
    <BaseNode status={data.status} selected={selected}>
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <CogIcon className="w-5 h-5 text-gray-500" />
            <span className="font-semibold text-gray-900 dark:text-white">
              {data.label}
            </span>
          </div>
          <StatusIcon className={`w-4 h-4 ${statusConfig.textColor}`} />
        </div>
        
        <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          Stage: {data.stage}
        </div>
        
        {data.jobs && data.jobs.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {data.jobs.slice(0, 3).map((job: string, index: number) => (
              <Badge key={index} variant="outline" size="sm">
                {job}
              </Badge>
            ))}
            {data.jobs.length > 3 && (
              <Badge variant="outline" size="sm">
                +{data.jobs.length - 3}
              </Badge>
            )}
          </div>
        )}
        
        {data.duration && (
          <div className="text-xs text-gray-500 mt-2">
            Duration: {data.duration}
          </div>
        )}
      </div>
      
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </BaseNode>
  );
};

// Job Node
export const JobNode: React.FC<NodeProps> = ({ data, selected }) => {
  const statusConfig = getStatusConfig(data.status);
  const StatusIcon = statusConfig.icon;

  return (
    <BaseNode status={data.status} selected={selected}>
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <CommandLineIcon className="w-5 h-5 text-blue-500" />
            <span className="font-semibold text-gray-900 dark:text-white">
              {data.label}
            </span>
          </div>
          <StatusIcon className={`w-4 h-4 ${statusConfig.textColor}`} />
        </div>
        
        {data.image && (
          <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Image: {data.image}
          </div>
        )}
        
        {data.script && data.script.length > 0 && (
          <div className="bg-gray-100 dark:bg-gray-700 rounded p-2 text-xs font-mono">
            {data.script.slice(0, 2).map((line: string, index: number) => (
              <div key={index} className="text-gray-700 dark:text-gray-300">
                {line.length > 30 ? `${line.substring(0, 30)}...` : line}
              </div>
            ))}
            {data.script.length > 2 && (
              <div className="text-gray-500">
                +{data.script.length - 2} more lines
              </div>
            )}
          </div>
        )}
        
        {data.duration && (
          <div className="text-xs text-gray-500 mt-2">
            Duration: {data.duration}
          </div>
        )}
      </div>
      
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </BaseNode>
  );
};

// Approval Node
export const ApprovalNode: React.FC<NodeProps> = ({ data, selected }) => {
  const statusConfig = getStatusConfig(data.status);
  const StatusIcon = statusConfig.icon;

  return (
    <BaseNode status={data.status} selected={selected} className="border-orange-200 dark:border-orange-800">
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <UserIcon className="w-5 h-5 text-orange-500" />
            <span className="font-semibold text-gray-900 dark:text-white">
              {data.label}
            </span>
          </div>
          <StatusIcon className={`w-4 h-4 ${statusConfig.textColor}`} />
        </div>
        
        <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          Manual Approval Required
        </div>
        
        {data.approvers && data.approvers.length > 0 && (
          <div className="mb-2">
            <div className="text-xs text-gray-500 mb-1">Approvers:</div>
            <div className="flex flex-wrap gap-1">
              {data.approvers.slice(0, 2).map((approver: string, index: number) => (
                <Badge key={index} variant="outline" size="sm">
                  {approver}
                </Badge>
              ))}
              {data.approvers.length > 2 && (
                <Badge variant="outline" size="sm">
                  +{data.approvers.length - 2}
                </Badge>
              )}
            </div>
          </div>
        )}
        
        {data.timeout && (
          <div className="text-xs text-gray-500">
            Timeout: {data.timeout}
          </div>
        )}
        
        {data.status === 'waiting_approval' && (
          <div className="mt-2 flex space-x-1">
            <button className="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600">
              Approve
            </button>
            <button className="px-2 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600">
              Reject
            </button>
          </div>
        )}
      </div>
      
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </BaseNode>
  );
};

// Deployment Node
export const DeploymentNode: React.FC<NodeProps> = ({ data, selected }) => {
  const statusConfig = getStatusConfig(data.status);
  const StatusIcon = statusConfig.icon;

  return (
    <BaseNode status={data.status} selected={selected} className="border-green-200 dark:border-green-800">
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <CloudArrowUpIcon className="w-5 h-5 text-green-500" />
            <span className="font-semibold text-gray-900 dark:text-white">
              {data.label}
            </span>
          </div>
          <StatusIcon className={`w-4 h-4 ${statusConfig.textColor}`} />
        </div>
        
        <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
          <div>Environment: {data.environment}</div>
          <div>Strategy: {data.strategy}</div>
        </div>
        
        {data.replicas && (
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Replicas: {data.replicas}
          </div>
        )}
        
        {data.url && (
          <div className="mt-2">
            <a
              href={data.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-500 hover:text-blue-600 underline"
            >
              View Deployment
            </a>
          </div>
        )}
        
        {data.duration && (
          <div className="text-xs text-gray-500 mt-2">
            Duration: {data.duration}
          </div>
        )}
        
        {data.status === 'success' && data.environment === 'production' && (
          <div className="mt-2">
            <button className="px-2 py-1 bg-yellow-500 text-white text-xs rounded hover:bg-yellow-600">
              Rollback
            </button>
          </div>
        )}
      </div>
      
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </BaseNode>
  );
};

// Node Edit Modal Component
interface NodeEditModalProps {
  node: any;
  onSave: (node: any) => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onClose: () => void;
}

export const NodeEditModal: React.FC<NodeEditModalProps> = ({
  node,
  onSave,
  onDelete,
  onDuplicate,
  onClose,
}) => {
  const [formData, setFormData] = React.useState(node.data);

  const handleSave = () => {
    onSave({
      ...node,
      data: formData,
    });
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={onClose} />
        
        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Edit {node.type} Node
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Label
              </label>
              <input
                type="text"
                value={formData.label}
                onChange={(e) => setFormData({ ...formData, label: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            {node.type === 'stage' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Stage Type
                </label>
                <select
                  value={formData.stage}
                  onChange={(e) => setFormData({ ...formData, stage: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="build">Build</option>
                  <option value="test">Test</option>
                  <option value="deploy">Deploy</option>
                  <option value="release">Release</option>
                </select>
              </div>
            )}
            
            {node.type === 'deployment' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Environment
                  </label>
                  <select
                    value={formData.environment}
                    onChange={(e) => setFormData({ ...formData, environment: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="development">Development</option>
                    <option value="staging">Staging</option>
                    <option value="production">Production</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Strategy
                  </label>
                  <select
                    value={formData.strategy}
                    onChange={(e) => setFormData({ ...formData, strategy: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="rolling">Rolling</option>
                    <option value="blue-green">Blue-Green</option>
                    <option value="canary">Canary</option>
                  </select>
                </div>
              </>
            )}
          </div>
          
          <div className="flex justify-between mt-6">
            <div className="flex space-x-2">
              <button
                onClick={onDuplicate}
                className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
              >
                Duplicate
              </button>
              <button
                onClick={onDelete}
                className="px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900 rounded-md"
              >
                Delete
              </button>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={onClose}
                className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-3 py-2 text-sm bg-blue-600 text-white hover:bg-blue-700 rounded-md"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
