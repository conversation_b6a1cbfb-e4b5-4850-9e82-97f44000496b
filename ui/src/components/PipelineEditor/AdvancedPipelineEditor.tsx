import React, { useState, useCallback, useRef, useEffect } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  Panel,
  ReactFlowProvider,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  DocumentTextIcon,
  CodeBracketIcon,
  EyeIcon,
  CloudArrowUpIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  PlusIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Tabs } from '../ui/Tabs';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Textarea } from '../ui/Textarea';
import { Toggle } from '../ui/Toggle';
import { NodeEditModal } from './NodeEditModal';
import yaml from 'js-yaml';

// Import custom node components
import { StageNode, JobNode, ApprovalNode, DeploymentNode } from './NodeComponents';

// Custom node types
const nodeTypes = {
  stage: StageNode,
  job: JobNode,
  approval: ApprovalNode,
  deployment: DeploymentNode,
};

interface PipelineEditorProps {
  initialPipeline?: any;
  onSave?: (pipeline: any) => void;
  onRun?: (pipeline: any) => void;
  readonly?: boolean;
}

const AdvancedPipelineEditor: React.FC<PipelineEditorProps> = ({
  initialPipeline,
  onSave,
  onRun,
  readonly = false,
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [showNodeModal, setShowNodeModal] = useState(false);
  const [showYamlModal, setShowYamlModal] = useState(false);
  const [yamlContent, setYamlContent] = useState('');
  const [activeTab, setActiveTab] = useState('visual');
  const [pipelineConfig, setPipelineConfig] = useState({
    name: 'New Pipeline',
    description: '',
    triggers: ['push'],
    variables: {},
    timeout: '1h',
  });

  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { project } = useReactFlow();

  // Initialize with sample pipeline
  useEffect(() => {
    if (initialPipeline) {
      loadPipelineFromConfig(initialPipeline);
    } else {
      // Create default pipeline structure
      const defaultNodes = [
        {
          id: 'start',
          type: 'stage',
          position: { x: 100, y: 100 },
          data: { 
            label: 'Build',
            stage: 'build',
            jobs: ['compile', 'test'],
            status: 'pending'
          },
        },
        {
          id: 'deploy',
          type: 'deployment',
          position: { x: 400, y: 100 },
          data: { 
            label: 'Deploy',
            environment: 'staging',
            strategy: 'rolling',
            status: 'pending'
          },
        },
      ];
      
      const defaultEdges = [
        {
          id: 'e1-2',
          source: 'start',
          target: 'deploy',
          type: 'smoothstep',
        },
      ];

      setNodes(defaultNodes);
      setEdges(defaultEdges);
    }
  }, [initialPipeline]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    if (!readonly) {
      setSelectedNode(node);
      setShowNodeModal(true);
    }
  }, [readonly]);

  const addNewNode = useCallback((type: string) => {
    const newNode = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: getDefaultNodeData(type),
    };
    setNodes((nds) => [...nds, newNode]);
  }, [setNodes]);

  const deleteNode = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((n) => n.id !== nodeId));
    setEdges((eds) => eds.filter((e) => e.source !== nodeId && e.target !== nodeId));
  }, [setNodes, setEdges]);

  const duplicateNode = useCallback((node: Node) => {
    const newNode = {
      ...node,
      id: `${node.id}-copy-${Date.now()}`,
      position: { x: node.position.x + 50, y: node.position.y + 50 },
    };
    setNodes((nds) => [...nds, newNode]);
  }, [setNodes]);

  const generateYaml = useCallback(() => {
    const pipeline = {
      name: pipelineConfig.name,
      description: pipelineConfig.description,
      triggers: pipelineConfig.triggers,
      variables: pipelineConfig.variables,
      timeout: pipelineConfig.timeout,
      stages: nodes.map((node) => ({
        name: node.data.label,
        type: node.type,
        ...node.data,
      })),
      dependencies: edges.map((edge) => ({
        from: edge.source,
        to: edge.target,
      })),
    };

    return yaml.dump(pipeline, { indent: 2 });
  }, [nodes, edges, pipelineConfig]);

  const loadPipelineFromConfig = (config: any) => {
    // Convert config to nodes and edges
    // Implementation depends on your pipeline config structure
    setPipelineConfig(config);
  };

  const handleSave = () => {
    const pipelineData = {
      config: pipelineConfig,
      nodes,
      edges,
      yaml: generateYaml(),
    };
    onSave?.(pipelineData);
  };

  const handleRun = () => {
    const pipelineData = {
      config: pipelineConfig,
      nodes,
      edges,
      yaml: generateYaml(),
    };
    onRun?.(pipelineData);
  };

  const tabs = [
    {
      id: 'visual',
      label: 'Visual Editor',
      icon: <EyeIcon className="w-4 h-4" />,
      content: (
        <div className="h-[600px] border border-gray-200 dark:border-gray-700 rounded-lg">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            nodeTypes={nodeTypes}
            fitView
            className="bg-gray-50 dark:bg-gray-900"
          >
            <Controls />
            <MiniMap />
            <Background variant="dot" gap={12} size={1} />
            
            {/* Toolbar */}
            <Panel position="top-left">
              <div className="flex space-x-2 bg-white dark:bg-gray-800 p-2 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => addNewNode('stage')}
                  icon={<PlusIcon className="w-4 h-4" />}
                >
                  Stage
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => addNewNode('job')}
                  icon={<PlusIcon className="w-4 h-4" />}
                >
                  Job
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => addNewNode('approval')}
                  icon={<PlusIcon className="w-4 h-4" />}
                >
                  Approval
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => addNewNode('deployment')}
                  icon={<CloudArrowUpIcon className="w-4 h-4" />}
                >
                  Deploy
                </Button>
              </div>
            </Panel>

            {/* Actions */}
            <Panel position="top-right">
              <div className="flex space-x-2 bg-white dark:bg-gray-800 p-2 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={readonly}
                  icon={<DocumentTextIcon className="w-4 h-4" />}
                >
                  Save
                </Button>
                <Button
                  size="sm"
                  onClick={handleRun}
                  disabled={readonly}
                  icon={<PlayIcon className="w-4 h-4" />}
                >
                  Run
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setYamlContent(generateYaml());
                    setShowYamlModal(true);
                  }}
                  icon={<CodeBracketIcon className="w-4 h-4" />}
                >
                  YAML
                </Button>
              </div>
            </Panel>
          </ReactFlow>
        </div>
      ),
    },
    {
      id: 'yaml',
      label: 'YAML Editor',
      icon: <CodeBracketIcon className="w-4 h-4" />,
      content: (
        <div className="h-[600px]">
          <Textarea
            value={generateYaml()}
            onChange={(e) => setYamlContent(e.target.value)}
            className="h-full font-mono text-sm"
            placeholder="Pipeline YAML configuration..."
            readOnly={readonly}
          />
        </div>
      ),
    },
    {
      id: 'config',
      label: 'Configuration',
      icon: <DocumentTextIcon className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium mb-4">Pipeline Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Pipeline Name"
                value={pipelineConfig.name}
                onChange={(e) => setPipelineConfig({ ...pipelineConfig, name: e.target.value })}
                disabled={readonly}
              />
              <Input
                label="Timeout"
                value={pipelineConfig.timeout}
                onChange={(e) => setPipelineConfig({ ...pipelineConfig, timeout: e.target.value })}
                disabled={readonly}
              />
              <div className="md:col-span-2">
                <Textarea
                  label="Description"
                  value={pipelineConfig.description}
                  onChange={(e) => setPipelineConfig({ ...pipelineConfig, description: e.target.value })}
                  disabled={readonly}
                />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-medium mb-4">Triggers</h3>
            <div className="space-y-2">
              {['push', 'pull_request', 'tag', 'schedule', 'manual'].map((trigger) => (
                <label key={trigger} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={pipelineConfig.triggers.includes(trigger)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setPipelineConfig({
                          ...pipelineConfig,
                          triggers: [...pipelineConfig.triggers, trigger],
                        });
                      } else {
                        setPipelineConfig({
                          ...pipelineConfig,
                          triggers: pipelineConfig.triggers.filter((t) => t !== trigger),
                        });
                      }
                    }}
                    disabled={readonly}
                    className="mr-2"
                  />
                  <span className="text-sm capitalize">{trigger.replace('_', ' ')}</span>
                </label>
              ))}
            </div>
          </Card>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {readonly ? 'Pipeline Viewer' : 'Pipeline Editor'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {readonly ? 'View pipeline configuration' : 'Design your CI/CD pipeline visually'}
          </p>
        </div>
        
        {!readonly && (
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleSave}>
              Save Draft
            </Button>
            <Button onClick={handleRun} icon={<PlayIcon className="w-4 h-4" />}>
              Run Pipeline
            </Button>
          </div>
        )}
      </div>

      {/* Editor */}
      <Card className="p-6">
        <Tabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </Card>

      {/* Node Edit Modal */}
      {showNodeModal && selectedNode && (
        <NodeEditModal
          node={selectedNode}
          onSave={(updatedNode) => {
            setNodes((nds) =>
              nds.map((n) => (n.id === updatedNode.id ? updatedNode : n))
            );
            setShowNodeModal(false);
          }}
          onDelete={() => {
            deleteNode(selectedNode.id);
            setShowNodeModal(false);
          }}
          onDuplicate={() => {
            duplicateNode(selectedNode);
            setShowNodeModal(false);
          }}
          onClose={() => setShowNodeModal(false)}
        />
      )}

      {/* YAML Modal */}
      {showYamlModal && (
        <Modal
          isOpen={showYamlModal}
          onClose={() => setShowYamlModal(false)}
          title="Pipeline YAML"
          size="xl"
        >
          <div className="space-y-4">
            <Textarea
              value={yamlContent}
              onChange={(e) => setYamlContent(e.target.value)}
              className="h-96 font-mono text-sm"
              readOnly={readonly}
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowYamlModal(false)}>
                Close
              </Button>
              {!readonly && (
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(yamlContent);
                    setShowYamlModal(false);
                  }}
                  icon={<DocumentDuplicateIcon className="w-4 h-4" />}
                >
                  Copy to Clipboard
                </Button>
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

// Helper function to get default node data
function getDefaultNodeData(type: string) {
  switch (type) {
    case 'stage':
      return {
        label: 'New Stage',
        stage: 'build',
        jobs: [],
        status: 'pending',
      };
    case 'job':
      return {
        label: 'New Job',
        script: ['echo "Hello World"'],
        image: 'ubuntu:latest',
        status: 'pending',
      };
    case 'approval':
      return {
        label: 'Manual Approval',
        approvers: [],
        timeout: '24h',
        status: 'pending',
      };
    case 'deployment':
      return {
        label: 'Deploy',
        environment: 'staging',
        strategy: 'rolling',
        status: 'pending',
      };
    default:
      return { label: 'New Node' };
  }
}

// Wrapper component with ReactFlowProvider
const PipelineEditorWrapper: React.FC<PipelineEditorProps> = (props) => (
  <ReactFlowProvider>
    <AdvancedPipelineEditor {...props} />
  </ReactFlowProvider>
);

export default PipelineEditorWrapper;
