import React, { useState, useEffect } from 'react';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { Select } from '../ui/Select';
import { Button } from '../ui/Button';

interface NodeData {
  id: string;
  type: string;
  label: string;
  script?: string;
  image?: string;
  environment?: Record<string, string>;
}

interface NodeEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  node: NodeData | null;
  onSave: (updatedNode: NodeData) => void;
}

export const NodeEditModal: React.FC<NodeEditModalProps> = ({
  isOpen,
  onClose,
  node,
  onSave
}) => {
  const [formData, setFormData] = useState<NodeData>({
    id: '',
    type: 'build',
    label: '',
    script: '',
    image: '',
    environment: {}
  });

  useEffect(() => {
    if (node) {
      setFormData(node);
    }
  }, [node]);

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const nodeTypes = [
    { value: 'build', label: 'Build' },
    { value: 'test', label: 'Test' },
    { value: 'deploy', label: 'Deploy' },
    { value: 'approval', label: 'Manual Approval' },
    { value: 'notification', label: 'Notification' }
  ];

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Edit Node">
      <div className="space-y-4">
        <Input
          label="Node Name"
          value={formData.label}
          onChange={(e) => setFormData({ ...formData, label: e.target.value })}
          placeholder="Enter node name"
        />

        <Select
          label="Node Type"
          value={formData.type}
          onChange={(e) => setFormData({ ...formData, type: e.target.value })}
          options={nodeTypes}
        />

        <Input
          label="Docker Image"
          value={formData.image || ''}
          onChange={(e) => setFormData({ ...formData, image: e.target.value })}
          placeholder="e.g., node:18-alpine"
        />

        <Textarea
          label="Script"
          value={formData.script || ''}
          onChange={(e) => setFormData({ ...formData, script: e.target.value })}
          placeholder="Enter commands to run..."
          rows={6}
        />

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </div>
      </div>
    </Modal>
  );
};
