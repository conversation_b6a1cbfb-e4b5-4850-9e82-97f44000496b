@tailwind base;
@tailwind components;
@tailwind utilities;

/* CircleCI Exact Match Styles */
:root {
  --circleci-gray-50: #f8f9fa;
  --circleci-gray-100: #f1f3f4;
  --circleci-gray-200: #e8eaed;
  --circleci-gray-300: #dadce0;
  --circleci-gray-400: #9aa0a6;
  --circleci-gray-500: #5f6368;
  --circleci-gray-600: #3c4043;
  --circleci-gray-700: #202124;
  --circleci-blue: #1976d2;
  --circleci-green: #34a853;
  --circleci-red: #ea4335;
  --circleci-orange: #ff9800;
  --circleci-purple: #9c27b0;
}

@layer base {
  body {
    @apply bg-gray-50 text-gray-700 font-sans antialiased;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }
}

@layer components {
  /* CircleCI Status Badges - Exact Match */
  .status-running {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500 text-white;
  }

  .status-success {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500 text-white;
  }

  .status-failed {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-500 text-white;
  }

  .status-queued {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-500 text-white;
  }

  .status-needs_approval {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-500 text-white;
  }

  /* CircleCI Navigation Styles */
  .circleci-nav-item {
    @apply flex items-center px-4 py-3 text-sm text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors;
  }

  .circleci-nav-item-active {
    @apply bg-gray-200 text-gray-900 font-medium;
  }

  /* CircleCI Table Styles */
  .circleci-table-header {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50;
  }

  .circleci-table-cell {
    @apply px-6 py-4 text-sm text-gray-900;
  }

  .circleci-table-row {
    @apply hover:bg-gray-50 transition-colors border-b border-gray-100;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300;
  }

  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }

  /* Button Sizes */
  .btn-xs { @apply px-2 py-1 text-xs; }
  .btn-sm { @apply px-3 py-1.5 text-sm; }
  .btn-md { @apply px-4 py-2 text-sm; }
  .btn-lg { @apply px-6 py-3 text-base; }

  /* Status Badges */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-success { @apply bg-green-100 text-green-800; }
  .status-error { @apply bg-red-100 text-red-800; }
  .status-info { @apply bg-blue-100 text-blue-800; }
  .status-pending { @apply bg-gray-100 text-gray-800; }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --surface: #1e293b;
    --border: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --text-muted: #64748b;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
