@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ChainOps Design System */
:root {
  /* Primary Colors - ChainOps Brand */
  --chainops-primary: #0066cc;
  --chainops-primary-dark: #0052a3;
  --chainops-primary-light: #3385d6;

  /* Secondary Colors */
  --chainops-secondary: #6366f1;
  --chainops-accent: #10b981;

  /* Status Colors */
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;
  --status-pending: #6b7280;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Semantic Colors */
  --background: #ffffff;
  --surface: #f9fafb;
  --border: #e5e7eb;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-text-primary font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  code {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  }
}

@layer components {
  /* ChainOps Button Styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm;
  }

  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }

  /* Size variants */
  .btn-xs { @apply px-2 py-1 text-xs; }
  .btn-sm { @apply px-3 py-1.5 text-sm; }
  .btn-md { @apply px-4 py-2 text-sm; }
  .btn-lg { @apply px-6 py-3 text-base; }

  /* ChainOps Card Styles */
  .card {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Status Indicators */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-success { @apply bg-green-100 text-green-800; }
  .status-warning { @apply bg-yellow-100 text-yellow-800; }
  .status-error { @apply bg-red-100 text-red-800; }
  .status-info { @apply bg-blue-100 text-blue-800; }
  .status-pending { @apply bg-gray-100 text-gray-800; }

  /* Pipeline Status Icons */
  .pipeline-status {
    @apply w-3 h-3 rounded-full;
  }

  .pipeline-running { @apply bg-blue-500 animate-pulse; }
  .pipeline-success { @apply bg-green-500; }
  .pipeline-failed { @apply bg-red-500; }
  .pipeline-pending { @apply bg-gray-400; }
  .pipeline-cancelled { @apply bg-gray-500; }

  /* Navigation Styles */
  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors;
  }

  .nav-item-active {
    @apply bg-blue-50 text-blue-700 border-r-2 border-blue-600;
  }

  .nav-item-inactive {
    @apply text-gray-700 hover:bg-gray-100 hover:text-gray-900;
  }

  /* Form Styles */
  .form-input {
    @apply block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  /* Table Styles */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-row {
    @apply bg-white hover:bg-gray-50 transition-colors;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --surface: #1e293b;
    --border: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --text-muted: #64748b;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
