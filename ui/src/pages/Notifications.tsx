import React, { useState, useEffect } from 'react';
import {
  BellIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Input } from '../components/ui/Input';
import { Select } from '../components/ui/Select';
import { Modal } from '../components/ui/Modal';
import { Table } from '../components/ui/Table';
import { Badge } from '../components/ui/Badge';
import { Tabs } from '../components/ui/Tabs';
import { api } from '../services/api';
import toast from 'react-hot-toast';

interface NotificationRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  events: string[];
  providers: string[];
  recipients: string[];
  conditions: Record<string, string>;
}

interface NotificationProvider {
  id: string;
  type: string;
  name: string;
  enabled: boolean;
  config: Record<string, any>;
}

const Notifications: React.FC = () => {
  const [rules, setRules] = useState<NotificationRule[]>([]);
  const [providers, setProviders] = useState<NotificationProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRule, setSelectedRule] = useState<NotificationRule | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<NotificationProvider | null>(null);
  const [showRuleModal, setShowRuleModal] = useState(false);
  const [showProviderModal, setShowProviderModal] = useState(false);
  const [activeTab, setActiveTab] = useState('rules');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [rulesRes, providersRes] = await Promise.all([
        api.get('/notifications/rules'),
        api.get('/notifications/providers'),
      ]);
      setRules(rulesRes.data);
      setProviders(providersRes.data);
    } catch (error) {
      toast.error('Failed to load notification data');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRule = () => {
    setSelectedRule(null);
    setShowRuleModal(true);
  };

  const handleEditRule = (rule: NotificationRule) => {
    setSelectedRule(rule);
    setShowRuleModal(true);
  };

  const handleDeleteRule = async (ruleId: string) => {
    if (!confirm('Are you sure you want to delete this notification rule?')) return;

    try {
      await api.delete(`/notifications/rules/${ruleId}`);
      setRules(rules.filter(r => r.id !== ruleId));
      toast.success('Notification rule deleted successfully');
    } catch (error) {
      toast.error('Failed to delete notification rule');
      console.error(error);
    }
  };

  const handleCreateProvider = () => {
    setSelectedProvider(null);
    setShowProviderModal(true);
  };

  const handleEditProvider = (provider: NotificationProvider) => {
    setSelectedProvider(provider);
    setShowProviderModal(true);
  };

  const handleDeleteProvider = async (providerId: string) => {
    if (!confirm('Are you sure you want to delete this notification provider?')) return;

    try {
      await api.delete(`/notifications/providers/${providerId}`);
      setProviders(providers.filter(p => p.id !== providerId));
      toast.success('Notification provider deleted successfully');
    } catch (error) {
      toast.error('Failed to delete notification provider');
      console.error(error);
    }
  };

  const ruleColumns = [
    {
      key: 'name',
      label: 'Rule Name',
      render: (rule: NotificationRule) => (
        <div>
          <div className="font-medium">{rule.name}</div>
          <div className="text-sm text-gray-500">{rule.description}</div>
        </div>
      ),
    },
    {
      key: 'enabled',
      label: 'Status',
      render: (rule: NotificationRule) => (
        <Badge variant={rule.enabled ? 'success' : 'error'}>
          {rule.enabled ? 'Enabled' : 'Disabled'}
        </Badge>
      ),
    },
    {
      key: 'events',
      label: 'Events',
      render: (rule: NotificationRule) => (
        <div className="flex flex-wrap gap-1">
          {rule.events.slice(0, 3).map(event => (
            <Badge key={event} variant="outline">
              {event}
            </Badge>
          ))}
          {rule.events.length > 3 && (
            <Badge variant="outline">
              +{rule.events.length - 3} more
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'providers',
      label: 'Providers',
      render: (rule: NotificationRule) => (
        <div className="flex flex-wrap gap-1">
          {rule.providers.map(provider => (
            <Badge key={provider} variant="outline">
              {provider}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (rule: NotificationRule) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditRule(rule)}
            icon={<PencilIcon className="w-4 h-4" />}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteRule(rule.id)}
            icon={<TrashIcon className="w-4 h-4" />}
            className="text-red-600 hover:text-red-700"
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const providerColumns = [
    {
      key: 'name',
      label: 'Provider Name',
      render: (provider: NotificationProvider) => (
        <div>
          <div className="font-medium">{provider.name}</div>
          <div className="text-sm text-gray-500">{provider.type}</div>
        </div>
      ),
    },
    {
      key: 'type',
      label: 'Type',
      render: (provider: NotificationProvider) => (
        <Badge variant="outline">
          {provider.type}
        </Badge>
      ),
    },
    {
      key: 'enabled',
      label: 'Status',
      render: (provider: NotificationProvider) => (
        <Badge variant={provider.enabled ? 'success' : 'error'}>
          {provider.enabled ? 'Enabled' : 'Disabled'}
        </Badge>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (provider: NotificationProvider) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditProvider(provider)}
            icon={<PencilIcon className="w-4 h-4" />}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteProvider(provider.id)}
            icon={<TrashIcon className="w-4 h-4" />}
            className="text-red-600 hover:text-red-700"
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const tabs = [
    {
      id: 'rules',
      label: 'Notification Rules',
      icon: <BellIcon className="w-4 h-4" />,
      content: (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Notification Rules</h2>
            <Button
              onClick={handleCreateRule}
              icon={<PlusIcon className="w-4 h-4" />}
            >
              Add Rule
            </Button>
          </div>
          <Card>
            <Table
              data={rules}
              columns={ruleColumns}
              loading={loading}
              emptyMessage="No notification rules found"
            />
          </Card>
        </div>
      ),
    },
    {
      id: 'providers',
      label: 'Providers',
      icon: <CheckCircleIcon className="w-4 h-4" />,
      content: (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Notification Providers</h2>
            <Button
              onClick={handleCreateProvider}
              icon={<PlusIcon className="w-4 h-4" />}
            >
              Add Provider
            </Button>
          </div>
          <Card>
            <Table
              data={providers}
              columns={providerColumns}
              loading={loading}
              emptyMessage="No notification providers found"
            />
          </Card>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
        <p className="text-gray-600">Manage notification rules and providers</p>
      </div>

      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Rule Modal */}
      {showRuleModal && (
        <NotificationRuleModal
          rule={selectedRule}
          providers={providers}
          onSave={(rule) => {
            if (selectedRule) {
              setRules(rules.map(r => r.id === rule.id ? rule : r));
            } else {
              setRules([...rules, rule]);
            }
            setShowRuleModal(false);
            toast.success(`Notification rule ${selectedRule ? 'updated' : 'created'} successfully`);
          }}
          onClose={() => setShowRuleModal(false)}
        />
      )}

      {/* Provider Modal */}
      {showProviderModal && (
        <NotificationProviderModal
          provider={selectedProvider}
          onSave={(provider) => {
            if (selectedProvider) {
              setProviders(providers.map(p => p.id === provider.id ? provider : p));
            } else {
              setProviders([...providers, provider]);
            }
            setShowProviderModal(false);
            toast.success(`Notification provider ${selectedProvider ? 'updated' : 'created'} successfully`);
          }}
          onClose={() => setShowProviderModal(false)}
        />
      )}
    </div>
  );
};

// Notification Rule Modal Component
interface NotificationRuleModalProps {
  rule: NotificationRule | null;
  providers: NotificationProvider[];
  onSave: (rule: NotificationRule) => void;
  onClose: () => void;
}

const NotificationRuleModal: React.FC<NotificationRuleModalProps> = ({
  rule,
  providers,
  onSave,
  onClose,
}) => {
  const [formData, setFormData] = useState({
    name: rule?.name || '',
    description: rule?.description || '',
    enabled: rule?.enabled ?? true,
    events: rule?.events || [],
    providers: rule?.providers || [],
    recipients: rule?.recipients?.join(', ') || '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const payload = {
        ...formData,
        recipients: formData.recipients.split(',').map(r => r.trim()).filter(r => r),
      };

      let response;
      if (rule) {
        response = await api.put(`/notifications/rules/${rule.id}`, payload);
      } else {
        response = await api.post('/notifications/rules', payload);
      }

      onSave(response.data);
    } catch (error) {
      toast.error('Failed to save notification rule');
      console.error(error);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={rule ? 'Edit Notification Rule' : 'Create Notification Rule'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="Rule Name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />

        <Input
          label="Description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          required
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Events
          </label>
          <div className="space-y-2">
            {[
              'pipeline.started',
              'pipeline.completed',
              'pipeline.failed',
              'deployment.started',
              'deployment.success',
              'deployment.failed',
            ].map(event => (
              <label key={event} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.events.includes(event)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setFormData({
                        ...formData,
                        events: [...formData.events, event],
                      });
                    } else {
                      setFormData({
                        ...formData,
                        events: formData.events.filter(e => e !== event),
                      });
                    }
                  }}
                  className="mr-2"
                />
                <span className="text-sm">{event}</span>
              </label>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Providers
          </label>
          <div className="space-y-2">
            {providers.map(provider => (
              <label key={provider.id} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.providers.includes(provider.type)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setFormData({
                        ...formData,
                        providers: [...formData.providers, provider.type],
                      });
                    } else {
                      setFormData({
                        ...formData,
                        providers: formData.providers.filter(p => p !== provider.type),
                      });
                    }
                  }}
                  className="mr-2"
                />
                <span className="text-sm">{provider.name} ({provider.type})</span>
              </label>
            ))}
          </div>
        </div>

        <Input
          label="Recipients (comma-separated)"
          value={formData.recipients}
          onChange={(e) => setFormData({ ...formData, recipients: e.target.value })}
          placeholder="<EMAIL>, #channel, @user"
        />

        <div className="flex items-center">
          <input
            type="checkbox"
            checked={formData.enabled}
            onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
            className="mr-2"
          />
          <label className="text-sm">Enable this rule</label>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            {rule ? 'Update' : 'Create'} Rule
          </Button>
        </div>
      </form>
    </Modal>
  );
};

// Notification Provider Modal Component
interface NotificationProviderModalProps {
  provider: NotificationProvider | null;
  onSave: (provider: NotificationProvider) => void;
  onClose: () => void;
}

const NotificationProviderModal: React.FC<NotificationProviderModalProps> = ({
  provider,
  onSave,
  onClose,
}) => {
  const [formData, setFormData] = useState({
    name: provider?.name || '',
    type: provider?.type || 'slack',
    enabled: provider?.enabled ?? true,
    config: provider?.config || {},
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      let response;
      if (provider) {
        response = await api.put(`/notifications/providers/${provider.id}`, formData);
      } else {
        response = await api.post('/notifications/providers', formData);
      }

      onSave(response.data);
    } catch (error) {
      toast.error('Failed to save notification provider');
      console.error(error);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={provider ? 'Edit Notification Provider' : 'Create Notification Provider'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="Provider Name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />

        <Select
          label="Provider Type"
          value={formData.type}
          onChange={(value) => setFormData({ ...formData, type: value })}
          options={[
            { value: 'slack', label: 'Slack' },
            { value: 'email', label: 'Email' },
            { value: 'teams', label: 'Microsoft Teams' },
            { value: 'webhook', label: 'Webhook' },
            { value: 'discord', label: 'Discord' },
          ]}
        />

        {formData.type === 'slack' && (
          <Input
            label="Webhook URL"
            value={formData.config.webhook_url || ''}
            onChange={(e) => setFormData({
              ...formData,
              config: { ...formData.config, webhook_url: e.target.value }
            })}
            placeholder="https://hooks.slack.com/services/..."
            required
          />
        )}

        {formData.type === 'email' && (
          <>
            <Input
              label="SMTP Host"
              value={formData.config.smtp_host || ''}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, smtp_host: e.target.value }
              })}
              required
            />
            <Input
              label="SMTP Port"
              type="number"
              value={formData.config.smtp_port || 587}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, smtp_port: parseInt(e.target.value) }
              })}
              required
            />
          </>
        )}

        <div className="flex items-center">
          <input
            type="checkbox"
            checked={formData.enabled}
            onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
            className="mr-2"
          />
          <label className="text-sm">Enable this provider</label>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            {provider ? 'Update' : 'Create'} Provider
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default Notifications;
