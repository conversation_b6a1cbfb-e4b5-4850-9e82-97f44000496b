import React, { useState, useEffect } from 'react';
import { 
  PlusIcon,
  ServerIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

interface Runner {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'busy';
  platform: string;
  version: string;
  lastSeen: string;
  jobsCompleted: number;
  currentJob?: string;
}

export default function Runners() {
  const [runners, setRunners] = useState<Runner[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data for runners
    setTimeout(() => {
      setRunners([
        {
          id: '1',
          name: 'runner-prod-01',
          status: 'online',
          platform: 'Linux x64',
          version: '2.311.0',
          lastSeen: '2 minutes ago',
          jobsCompleted: 1247,
          currentJob: 'chatbot-ui #184719'
        },
        {
          id: '2',
          name: 'runner-prod-02',
          status: 'busy',
          platform: 'Linux x64',
          version: '2.311.0',
          lastSeen: '1 minute ago',
          jobsCompleted: 892,
          currentJob: 'app-backend #113811'
        },
        {
          id: '3',
          name: 'runner-dev-01',
          status: 'online',
          platform: 'macOS ARM64',
          version: '2.310.1',
          lastSeen: '5 minutes ago',
          jobsCompleted: 456
        },
        {
          id: '4',
          name: 'runner-dev-02',
          status: 'offline',
          platform: 'Windows x64',
          version: '2.309.0',
          lastSeen: '2 hours ago',
          jobsCompleted: 234
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'busy':
        return <ClockIcon className="w-5 h-5 text-yellow-500" />;
      case 'offline':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      default:
        return <XCircleIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
    switch (status) {
      case 'online':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'busy':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'offline':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Runners</h1>
          <p className="text-gray-600 mt-1">
            Manage your pipeline execution runners
          </p>
        </div>
        <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
          <PlusIcon className="w-4 h-4 mr-2" />
          Add Runner
        </button>
      </div>

      {/* Runners Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="grid grid-cols-12 gap-4 px-6 py-3 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
          <div className="col-span-3">Runner</div>
          <div className="col-span-2">Status</div>
          <div className="col-span-2">Platform</div>
          <div className="col-span-2">Version</div>
          <div className="col-span-2">Jobs Completed</div>
          <div className="col-span-1">Last Seen</div>
        </div>

        <div className="divide-y divide-gray-200">
          {runners.map((runner) => (
            <div key={runner.id} className="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors items-center">
              {/* Runner Column */}
              <div className="col-span-3">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(runner.status)}
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {runner.name}
                    </div>
                    {runner.currentJob && (
                      <div className="text-sm text-gray-500">
                        Running: {runner.currentJob}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Status Column */}
              <div className="col-span-2">
                <span className={getStatusBadge(runner.status)}>
                  {runner.status}
                </span>
              </div>

              {/* Platform Column */}
              <div className="col-span-2">
                <div className="flex items-center space-x-2">
                  <CpuChipIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-900">{runner.platform}</span>
                </div>
              </div>

              {/* Version Column */}
              <div className="col-span-2">
                <span className="text-sm text-gray-900">{runner.version}</span>
              </div>

              {/* Jobs Completed Column */}
              <div className="col-span-2">
                <span className="text-sm text-gray-900">{runner.jobsCompleted.toLocaleString()}</span>
              </div>

              {/* Last Seen Column */}
              <div className="col-span-1">
                <span className="text-sm text-gray-500">{runner.lastSeen}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {runners.length === 0 && !loading && (
        <div className="text-center py-12">
          <ServerIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No runners</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first runner.
          </p>
          <div className="mt-6">
            <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Runner
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
