import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  PlusIcon,
  Squares2X2Icon,
  StarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

interface Project {
  id: string;
  name: string;
  description: string;
  repository: string;
  lastActivity: string;
  status: 'active' | 'inactive';
  pipelineCount: number;
  starred: boolean;
}

export default function Projects() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data for projects
    setTimeout(() => {
      setProjects([
        {
          id: '1',
          name: 'chatbot-ui',
          description: 'Modern AI chatbot interface built with React',
          repository: 'github.com/chainops/chatbot-ui',
          lastActivity: '2 hours ago',
          status: 'active',
          pipelineCount: 3,
          starred: true
        },
        {
          id: '2',
          name: 'app-backend',
          description: 'Backend API service for the main application',
          repository: 'github.com/chainops/app-backend',
          lastActivity: '1 day ago',
          status: 'active',
          pipelineCount: 5,
          starred: false
        },
        {
          id: '3',
          name: 'web-ui',
          description: 'Main web application frontend',
          repository: 'github.com/chainops/web-ui',
          lastActivity: '3 days ago',
          status: 'active',
          pipelineCount: 2,
          starred: true
        },
        {
          id: '4',
          name: 'ai-chatbot',
          description: 'AI-powered chatbot service',
          repository: 'github.com/chainops/ai-chatbot',
          lastActivity: '1 week ago',
          status: 'inactive',
          pipelineCount: 1,
          starred: false
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const toggleStar = (projectId: string) => {
    setProjects(projects.map(project => 
      project.id === projectId 
        ? { ...project, starred: !project.starred }
        : project
    ));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600 mt-1">
            Manage your repositories and project configurations
          </p>
        </div>
        <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
          <PlusIcon className="w-4 h-4 mr-2" />
          Add Project
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.map((project) => (
          <div key={project.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Squares2X2Icon className="w-8 h-8 text-gray-400" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
                  <p className="text-sm text-gray-500">{project.repository}</p>
                </div>
              </div>
              <button
                onClick={() => toggleStar(project.id)}
                className={`p-1 rounded ${project.starred ? 'text-yellow-500' : 'text-gray-400 hover:text-yellow-500'}`}
              >
                <StarIcon className={`w-5 h-5 ${project.starred ? 'fill-current' : ''}`} />
              </button>
            </div>

            <p className="text-gray-600 mb-4 text-sm">{project.description}</p>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  project.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {project.status === 'active' ? (
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                  ) : (
                    <XCircleIcon className="w-3 h-3 mr-1" />
                  )}
                  {project.status}
                </span>
                <span className="text-gray-500">{project.pipelineCount} pipelines</span>
              </div>
              <div className="flex items-center text-gray-500">
                <ClockIcon className="w-4 h-4 mr-1" />
                {project.lastActivity}
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <Link
                to={`/projects/${project.id}`}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                View Details →
              </Link>
            </div>
          </div>
        ))}
      </div>

      {projects.length === 0 && !loading && (
        <div className="text-center py-12">
          <Squares2X2Icon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No projects</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first project.
          </p>
          <div className="mt-6">
            <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Project
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
