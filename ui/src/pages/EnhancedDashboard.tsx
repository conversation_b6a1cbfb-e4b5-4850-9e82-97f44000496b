import React, { useState, useEffect } from 'react';
import {
  RocketLaunchIcon,
  PlayIcon,
  PauseIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ChartBarIcon,
  UserGroupIcon,
  CloudArrowUpIcon,
  CommandLineIcon,
  ComputerDesktopIcon,
  EyeIcon,
  PlusIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Badge } from '../components/ui/Badge';
import { Tabs } from '../components/ui/Tabs';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { formatDistanceToNow } from 'date-fns';

interface DashboardProps {
  user?: any;
}

const EnhancedDashboard: React.FC<DashboardProps> = ({ user }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [recentExecutions, setRecentExecutions] = useState([]);
  const [metrics, setMetrics] = useState({
    totalPipelines: 0,
    activeExecutions: 0,
    successRate: 0,
    avgDuration: 0,
  });

  // Mock data for demonstration
  const mockExecutions = [
    {
      id: 'exec-1',
      pipeline: { name: 'Frontend Build', repository: 'chainops/frontend' },
      status: 'success',
      branch: 'main',
      commit: { sha: 'abc123', message: 'Update dependencies', author: 'John Doe' },
      startedAt: new Date(Date.now() - 300000),
      duration: 180,
      trigger: 'push',
    },
    {
      id: 'exec-2',
      pipeline: { name: 'Backend API', repository: 'chainops/backend' },
      status: 'running',
      branch: 'feature/auth',
      commit: { sha: 'def456', message: 'Add authentication', author: 'Jane Smith' },
      startedAt: new Date(Date.now() - 120000),
      duration: 120,
      trigger: 'pull_request',
    },
    {
      id: 'exec-3',
      pipeline: { name: 'Mobile App', repository: 'chainops/mobile' },
      status: 'failed',
      branch: 'main',
      commit: { sha: 'ghi789', message: 'Fix build issues', author: 'Bob Wilson' },
      startedAt: new Date(Date.now() - 600000),
      duration: 240,
      trigger: 'push',
    },
  ];

  const mockMetrics = {
    totalPipelines: 12,
    activeExecutions: 3,
    successRate: 94.2,
    avgDuration: 185,
  };

  const mockChartData = [
    { name: 'Mon', executions: 24, success: 22, failed: 2 },
    { name: 'Tue', executions: 18, success: 17, failed: 1 },
    { name: 'Wed', executions: 32, success: 30, failed: 2 },
    { name: 'Thu', executions: 28, success: 26, failed: 2 },
    { name: 'Fri', executions: 35, success: 33, failed: 2 },
    { name: 'Sat', executions: 12, success: 12, failed: 0 },
    { name: 'Sun', executions: 8, success: 8, failed: 0 },
  ];

  useEffect(() => {
    // Load dashboard data
    setRecentExecutions(mockExecutions);
    setMetrics(mockMetrics);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'running':
        return <PlayIcon className="w-5 h-5 text-blue-500 animate-pulse" />;
      case 'pending':
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="success">Success</Badge>;
      case 'failed':
        return <Badge variant="error">Failed</Badge>;
      case 'running':
        return <Badge variant="info">Running</Badge>;
      case 'pending':
        return <Badge variant="warning">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const overviewTab = (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome back, {user?.name || 'Developer'}! 👋
        </h1>
        <p className="text-blue-100">
          Your CI/CD pipelines are running smoothly. Here's what's happening today.
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Pipelines"
          value={metrics.totalPipelines.toString()}
          icon={<RocketLaunchIcon className="w-6 h-6" />}
          color="blue"
          trend={{ value: 12, direction: 'up' }}
        />
        <MetricCard
          title="Active Executions"
          value={metrics.activeExecutions.toString()}
          icon={<PlayIcon className="w-6 h-6" />}
          color="green"
          trend={{ value: 8, direction: 'up' }}
        />
        <MetricCard
          title="Success Rate"
          value={`${metrics.successRate}%`}
          icon={<CheckCircleIcon className="w-6 h-6" />}
          color="emerald"
          trend={{ value: 2.1, direction: 'up' }}
        />
        <MetricCard
          title="Avg Duration"
          value={formatDuration(metrics.avgDuration)}
          icon={<ClockIcon className="w-6 h-6" />}
          color="purple"
          trend={{ value: 15, direction: 'down' }}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Execution Trend */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Execution Trend (7 days)</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={mockChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="executions"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>

        {/* Success vs Failure */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Success vs Failure</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={mockChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="success" fill="#10B981" name="Success" />
              <Bar dataKey="failed" fill="#EF4444" name="Failed" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            className="h-20 flex-col space-y-2"
            variant="outline"
            onClick={() => window.location.href = '/pipelines/new'}
          >
            <PlusIcon className="w-6 h-6" />
            <span>Create Pipeline</span>
          </Button>
          
          <Button
            className="h-20 flex-col space-y-2"
            variant="outline"
            onClick={() => window.location.href = '/local-test'}
          >
            <ComputerDesktopIcon className="w-6 h-6" />
            <span>Test Locally</span>
          </Button>
          
          <Button
            className="h-20 flex-col space-y-2"
            variant="outline"
            onClick={() => window.location.href = '/analytics'}
          >
            <ChartBarIcon className="w-6 h-6" />
            <span>View Analytics</span>
          </Button>
        </div>
      </Card>
    </div>
  );

  const executionsTab = (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Recent Executions</h2>
        <Button
          onClick={() => window.location.href = '/executions'}
          variant="outline"
        >
          View All
        </Button>
      </div>

      <div className="space-y-4">
        {recentExecutions.map((execution: any) => (
          <Card key={execution.id} className="p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {getStatusIcon(execution.status)}
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    {execution.pipeline.name}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <span>{execution.pipeline.repository}</span>
                    <span>•</span>
                    <span>{execution.branch}</span>
                    <span>•</span>
                    <span>{execution.commit.sha.substring(0, 7)}</span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {execution.commit.message} by {execution.commit.author}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="text-right text-sm">
                  <div className="text-gray-600 dark:text-gray-400">
                    {formatDistanceToNow(execution.startedAt, { addSuffix: true })}
                  </div>
                  <div className="text-gray-500">
                    {formatDuration(execution.duration)}
                  </div>
                </div>
                {getStatusBadge(execution.status)}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.location.href = `/executions/${execution.id}`}
                  icon={<EyeIcon className="w-4 h-4" />}
                >
                  View
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const localTestingTab = (
    <div className="space-y-6">
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <ComputerDesktopIcon className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-blue-900 dark:text-blue-100">
            Local Pipeline Testing
          </h2>
        </div>
        <p className="text-blue-800 dark:text-blue-200 mb-4">
          Test your pipelines locally before pushing to production. No more waiting for CI/CD feedback!
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
            <h3 className="font-medium mb-2">🚀 Quick Start</h3>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <div>1. Install ChainOps CLI</div>
              <div>2. Run <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">chainops-local init</code></div>
              <div>3. Test with <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">chainops-local run</code></div>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
            <h3 className="font-medium mb-2">✨ Features</h3>
            <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <div>• Docker container support</div>
              <div>• Real-time log streaming</div>
              <div>• Environment variable injection</div>
              <div>• Artifact caching</div>
            </div>
          </div>
        </div>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Installation Guide</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">1. Download ChainOps CLI</h4>
            <div className="bg-gray-100 dark:bg-gray-800 rounded p-3 font-mono text-sm">
              curl -sSL https://get.chainops.dev | bash
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">2. Initialize Local Configuration</h4>
            <div className="bg-gray-100 dark:bg-gray-800 rounded p-3 font-mono text-sm">
              chainops-local init
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">3. Test Your Pipeline</h4>
            <div className="bg-gray-100 dark:bg-gray-800 rounded p-3 font-mono text-sm">
              chainops-local run --pipeline .chainops.yml
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <ChartBarIcon className="w-4 h-4" />,
      content: overviewTab,
    },
    {
      id: 'executions',
      label: 'Recent Executions',
      icon: <PlayIcon className="w-4 h-4" />,
      content: executionsTab,
    },
    {
      id: 'local-testing',
      label: 'Local Testing',
      icon: <ComputerDesktopIcon className="w-4 h-4" />,
      content: localTestingTab,
    },
  ];

  return (
    <div className="space-y-6">
      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </div>
  );
};

// Metric Card Component
interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'emerald' | 'purple' | 'red';
  trend?: {
    value: number;
    direction: 'up' | 'down';
  };
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, color, trend }) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-white',
    green: 'bg-green-500 text-white',
    emerald: 'bg-emerald-500 text-white',
    purple: 'bg-purple-500 text-white',
    red: 'bg-red-500 text-white',
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">{value}</p>
          {trend && (
            <div className="flex items-center mt-2">
              {trend.direction === 'up' ? (
                <ArrowTrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
              ) : (
                <ArrowTrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${trend.direction === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                {trend.value}%
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          {icon}
        </div>
      </div>
    </Card>
  );
};

export default EnhancedDashboard;
