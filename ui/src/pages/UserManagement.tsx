import React, { useState, useEffect } from 'react';
import {
  UserPlusIcon,
  PencilIcon,
  TrashIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  KeyIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Input } from '../components/ui/Input';
import { Select } from '../components/ui/Select';
import { Modal } from '../components/ui/Modal';
import { DataTable } from '../components/ui/Table';
import { Badge } from '../components/ui/Badge';
import { Tabs } from '../components/ui/Tabs';
import { api } from '../services/api';
import toast from 'react-hot-toast';

interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  status: 'active' | 'inactive' | 'suspended';
  roles: Role[];
  createdAt: string;
  lastLogin?: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
}

interface Permission {
  id: string;
  resource: string;
  action: string;
  description: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [activeTab, setActiveTab] = useState('users');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [usersRes, rolesRes, permissionsRes] = await Promise.all([
        api.get('/users'),
        api.get('/roles'),
        api.get('/permissions'),
      ]);
      setUsers(usersRes.data);
      setRoles(rolesRes.data);
      setPermissions(permissionsRes.data);
    } catch (error) {
      toast.error('Failed to load data');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = () => {
    setSelectedUser(null);
    setShowUserModal(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setShowUserModal(true);
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      await api.delete(`/users/${userId}`);
      setUsers(users.filter(u => u.id !== userId));
      toast.success('User deleted successfully');
    } catch (error) {
      toast.error('Failed to delete user');
      console.error(error);
    }
  };

  const handleCreateRole = () => {
    setSelectedRole(null);
    setShowRoleModal(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setShowRoleModal(true);
  };

  const handleDeleteRole = async (roleId: string) => {
    if (!confirm('Are you sure you want to delete this role?')) return;

    try {
      await api.delete(`/roles/${roleId}`);
      setRoles(roles.filter(r => r.id !== roleId));
      toast.success('Role deleted successfully');
    } catch (error) {
      toast.error('Failed to delete role');
      console.error(error);
    }
  };

  const userColumns = [
    {
      key: 'username',
      label: 'Username',
      render: (user: User) => (
        <div className="flex items-center">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
            {user.firstName?.[0] || user.username[0].toUpperCase()}
          </div>
          <div>
            <div className="font-medium">{user.username}</div>
            <div className="text-sm text-gray-500">{user.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'name',
      label: 'Full Name',
      render: (user: User) => `${user.firstName} ${user.lastName}`,
    },
    {
      key: 'status',
      label: 'Status',
      render: (user: User) => (
        <Badge
          variant={
            user.status === 'active' ? 'success' :
            user.status === 'inactive' ? 'warning' : 'error'
          }
        >
          {user.status}
        </Badge>
      ),
    },
    {
      key: 'roles',
      label: 'Roles',
      render: (user: User) => (
        <div className="flex flex-wrap gap-1">
          {user.roles.map(role => (
            <Badge key={role.id} variant="outline">
              {role.name}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: 'lastLogin',
      label: 'Last Login',
      render: (user: User) => user.lastLogin ? 
        new Date(user.lastLogin).toLocaleDateString() : 'Never',
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (user: User) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditUser(user)}
            icon={<PencilIcon className="w-4 h-4" />}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteUser(user.id)}
            icon={<TrashIcon className="w-4 h-4" />}
            className="text-red-600 hover:text-red-700"
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const roleColumns = [
    {
      key: 'name',
      label: 'Role Name',
      render: (role: Role) => (
        <div>
          <div className="font-medium">{role.name}</div>
          <div className="text-sm text-gray-500">{role.description}</div>
        </div>
      ),
    },
    {
      key: 'permissions',
      label: 'Permissions',
      render: (role: Role) => (
        <div className="flex flex-wrap gap-1">
          {role.permissions.slice(0, 3).map(permission => (
            <Badge key={permission.id} variant="outline">
              {permission.resource}:{permission.action}
            </Badge>
          ))}
          {role.permissions.length > 3 && (
            <Badge variant="outline">
              +{role.permissions.length - 3} more
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'userCount',
      label: 'Users',
      render: (role: Role) => {
        const userCount = users.filter(u => u.roles.some(r => r.id === role.id)).length;
        return `${userCount} users`;
      },
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (role: Role) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditRole(role)}
            icon={<PencilIcon className="w-4 h-4" />}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteRole(role.id)}
            icon={<TrashIcon className="w-4 h-4" />}
            className="text-red-600 hover:text-red-700"
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const permissionColumns = [
    {
      key: 'resource',
      label: 'Resource',
      render: (permission: Permission) => permission.resource,
    },
    {
      key: 'action',
      label: 'Action',
      render: (permission: Permission) => permission.action,
    },
    {
      key: 'description',
      label: 'Description',
      render: (permission: Permission) => permission.description,
    },
  ];

  const tabs = [
    {
      id: 'users',
      label: 'Users',
      icon: <UserGroupIcon className="w-4 h-4" />,
      content: (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Users</h2>
            <Button
              onClick={handleCreateUser}
              icon={<UserPlusIcon className="w-4 h-4" />}
            >
              Add User
            </Button>
          </div>
          <Card>
            <DataTable
              data={users}
              columns={userColumns}
              loading={loading}
              emptyMessage="No users found"
            />
          </Card>
        </div>
      ),
    },
    {
      id: 'roles',
      label: 'Roles',
      icon: <ShieldCheckIcon className="w-4 h-4" />,
      content: (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Roles</h2>
            <Button
              onClick={handleCreateRole}
              icon={<UserPlusIcon className="w-4 h-4" />}
            >
              Add Role
            </Button>
          </div>
          <Card>
            <DataTable
              data={roles}
              columns={roleColumns}
              loading={loading}
              emptyMessage="No roles found"
            />
          </Card>
        </div>
      ),
    },
    {
      id: 'permissions',
      label: 'Permissions',
      icon: <KeyIcon className="w-4 h-4" />,
      content: (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Permissions</h2>
          </div>
          <Card>
            <DataTable
              data={permissions}
              columns={permissionColumns}
              loading={loading}
              emptyMessage="No permissions found"
            />
          </Card>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
        <p className="text-gray-600">Manage users, roles, and permissions</p>
      </div>

      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* User Modal */}
      {showUserModal && (
        <UserModal
          user={selectedUser}
          roles={roles}
          onSave={(user) => {
            if (selectedUser) {
              setUsers(users.map(u => u.id === user.id ? user : u));
            } else {
              setUsers([...users, user]);
            }
            setShowUserModal(false);
            toast.success(`User ${selectedUser ? 'updated' : 'created'} successfully`);
          }}
          onClose={() => setShowUserModal(false)}
        />
      )}

      {/* Role Modal */}
      {showRoleModal && (
        <RoleModal
          role={selectedRole}
          permissions={permissions}
          onSave={(role) => {
            if (selectedRole) {
              setRoles(roles.map(r => r.id === role.id ? role : r));
            } else {
              setRoles([...roles, role]);
            }
            setShowRoleModal(false);
            toast.success(`Role ${selectedRole ? 'updated' : 'created'} successfully`);
          }}
          onClose={() => setShowRoleModal(false)}
        />
      )}
    </div>
  );
};

// User Modal Component
interface UserModalProps {
  user: User | null;
  roles: Role[];
  onSave: (user: User) => void;
  onClose: () => void;
}

const UserModal: React.FC<UserModalProps> = ({ user, roles, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    status: user?.status || 'active',
    roleIds: user?.roles.map(r => r.id) || [],
    password: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const payload = {
        ...formData,
        roles: roles.filter(r => formData.roleIds.includes(r.id)),
      };

      let response;
      if (user) {
        response = await api.put(`/users/${user.id}`, payload);
      } else {
        response = await api.post('/users', payload);
      }

      onSave(response.data);
    } catch (error) {
      toast.error('Failed to save user');
      console.error(error);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={user ? 'Edit User' : 'Create User'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Input
            label="Username"
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            required
          />
          <Input
            label="Email"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Input
            label="First Name"
            value={formData.firstName}
            onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
            required
          />
          <Input
            label="Last Name"
            value={formData.lastName}
            onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
            required
          />
        </div>

        {!user && (
          <Input
            label="Password"
            type="password"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            required
          />
        )}

        <Select
          label="Status"
          value={formData.status}
          onChange={(value) => setFormData({ ...formData, status: value as any })}
          options={[
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' },
            { value: 'suspended', label: 'Suspended' },
          ]}
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Roles
          </label>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {roles.map(role => (
              <label key={role.id} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.roleIds.includes(role.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setFormData({
                        ...formData,
                        roleIds: [...formData.roleIds, role.id],
                      });
                    } else {
                      setFormData({
                        ...formData,
                        roleIds: formData.roleIds.filter(id => id !== role.id),
                      });
                    }
                  }}
                  className="mr-2"
                />
                <span className="text-sm">{role.name}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            {user ? 'Update' : 'Create'} User
          </Button>
        </div>
      </form>
    </Modal>
  );
};

// Role Modal Component
interface RoleModalProps {
  role: Role | null;
  permissions: Permission[];
  onSave: (role: Role) => void;
  onClose: () => void;
}

const RoleModal: React.FC<RoleModalProps> = ({ role, permissions, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    name: role?.name || '',
    description: role?.description || '',
    permissionIds: role?.permissions.map(p => p.id) || [],
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const payload = {
        ...formData,
        permissions: permissions.filter(p => formData.permissionIds.includes(p.id)),
      };

      let response;
      if (role) {
        response = await api.put(`/roles/${role.id}`, payload);
      } else {
        response = await api.post('/roles', payload);
      }

      onSave(response.data);
    } catch (error) {
      toast.error('Failed to save role');
      console.error(error);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={role ? 'Edit Role' : 'Create Role'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="Role Name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />

        <Input
          label="Description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          required
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Permissions
          </label>
          <div className="space-y-2 max-h-60 overflow-y-auto border border-gray-200 rounded-md p-3">
            {permissions.map(permission => (
              <label key={permission.id} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.permissionIds.includes(permission.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setFormData({
                        ...formData,
                        permissionIds: [...formData.permissionIds, permission.id],
                      });
                    } else {
                      setFormData({
                        ...formData,
                        permissionIds: formData.permissionIds.filter(id => id !== permission.id),
                      });
                    }
                  }}
                  className="mr-2"
                />
                <div>
                  <span className="text-sm font-medium">
                    {permission.resource}:{permission.action}
                  </span>
                  <p className="text-xs text-gray-500">{permission.description}</p>
                </div>
              </label>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            {role ? 'Update' : 'Create'} Role
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default UserManagement;
