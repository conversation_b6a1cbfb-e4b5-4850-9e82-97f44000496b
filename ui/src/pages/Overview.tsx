import React from 'react';
import { Link } from 'react-router-dom';

const Overview: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">Welcome to CircleCI</h1>
      <p className="text-gray-600 mb-6">Your CI/CD platform dashboard</p>

      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Pipelines</h3>
        <p className="text-3xl font-bold text-blue-600">2,847</p>
      </div>

      <div className="mt-8">
        <Link to="/pipelines" className="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm px-4 py-2 text-sm">
          View All Pipelines
        </Link>
      </div>
    </div>
  );
};

export default Overview;
