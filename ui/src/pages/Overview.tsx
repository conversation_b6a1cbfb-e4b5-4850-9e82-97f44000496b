import React from 'react';
import { Link } from 'react-router-dom';
import {
  RocketLaunchIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ChartBarIcon,
  ServerIcon,
  CloudArrowUpIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  PlayIcon,
  PlusIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

const Overview: React.FC = () => {
  // Mock data for dashboard
  const stats = [
    {
      name: 'Total Pipelines',
      value: '2,847',
      change: '+12%',
      trend: 'up',
      icon: RocketLaunchIcon,
      color: 'blue'
    },
    {
      name: 'Success Rate',
      value: '94.2%',
      change: '+2.1%',
      trend: 'up',
      icon: CheckCircleIcon,
      color: 'green'
    },
    {
      name: 'Active Runners',
      value: '24',
      change: '+3',
      trend: 'up',
      icon: ServerIcon,
      color: 'purple'
    },
    {
      name: 'Deployments Today',
      value: '156',
      change: '+8%',
      trend: 'up',
      icon: CloudArrowUpIcon,
      color: 'orange'
    }
  ];

  const recentPipelines = [
    {
      id: '1',
      name: 'chainops-backend',
      status: 'running',
      workflow: 'ci-cd-pipeline',
      branch: 'main',
      author: 'john.doe',
      startedAt: '2 minutes ago'
    },
    {
      id: '2',
      name: 'chainops-ui',
      status: 'success',
      workflow: 'build-test-deploy',
      branch: 'main',
      author: 'jane.smith',
      startedAt: '15 minutes ago'
    },
    {
      id: '3',
      name: 'chainops-runner',
      status: 'failed',
      workflow: 'security-scan',
      branch: 'develop',
      author: 'mike.wilson',
      startedAt: '1 hour ago'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <div className="w-3 h-3 bg-green-500 rounded-full" />;
      case 'failed':
        return <div className="w-3 h-3 bg-red-500 rounded-full" />;
      case 'running':
        return <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" />;
      default:
        return <div className="w-3 h-3 bg-gray-400 rounded-full" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return 'status-badge status-success';
      case 'failed':
        return 'status-badge status-error';
      case 'running':
        return 'status-badge status-info';
      default:
        return 'status-badge status-pending';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome to ChainOps</h1>
        <p className="text-lg text-gray-600">
          Your enterprise CI/CD platform dashboard
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-3xl font-bold text-gray-900 mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg bg-${stat.color}-100`}>
                  <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                {stat.trend === 'up' ? (
                  <ArrowTrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
                ) : (
                  <ArrowTrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${
                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500 ml-1">vs last month</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Pipelines */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Recent Pipeline Runs</h3>
                <Link to="/pipelines" className="btn btn-ghost btn-sm">
                  <EyeIcon className="w-4 h-4 mr-2" />
                  View All
                </Link>
              </div>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                {recentPipelines.map((pipeline) => (
                  <div key={pipeline.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(pipeline.status)}
                      <div>
                        <Link
                          to={`/pipelines/${pipeline.id}`}
                          className="text-sm font-medium text-blue-600 hover:text-blue-800"
                        >
                          {pipeline.name}
                        </Link>
                        <div className="text-xs text-gray-500">
                          {pipeline.workflow} • {pipeline.branch} • {pipeline.author}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={getStatusBadge(pipeline.status)}>
                        {pipeline.status}
                      </span>
                      <span className="text-xs text-gray-500">{pipeline.startedAt}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
            </div>
            <div className="card-body space-y-3">
              <Link to="/pipelines/new" className="btn btn-primary w-full">
                <PlusIcon className="w-4 h-4 mr-2" />
                Create Pipeline
              </Link>
              <Link to="/projects" className="btn btn-secondary w-full">
                <RocketLaunchIcon className="w-4 h-4 mr-2" />
                Manage Projects
              </Link>
              <Link to="/runners" className="btn btn-secondary w-full">
                <ServerIcon className="w-4 h-4 mr-2" />
                View Runners
              </Link>
              <Link to="/analytics" className="btn btn-secondary w-full">
                <ChartBarIcon className="w-4 h-4 mr-2" />
                View Analytics
              </Link>
            </div>
          </div>

          {/* System Status */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">System Status</h3>
            </div>
            <div className="card-body space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Pipeline Engine</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-600">Operational</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Runner Pool</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-600">Healthy</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Deployment Service</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-yellow-600">Degraded</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Overview;
