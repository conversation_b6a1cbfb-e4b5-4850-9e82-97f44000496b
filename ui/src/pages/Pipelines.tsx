import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PauseIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { api } from '../lib/api';

interface Pipeline {
  id: string;
  name: string;
  number: string;
  status: 'running' | 'success' | 'failed' | 'queued' | 'needs_approval';
  workflow: string;
  triggerEvent: string;
  author: {
    name: string;
    avatar?: string;
  };
  branch: string;
  commit: string;
  duration?: number;
  startedAt: string;
}

export default function Pipelines() {
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPipelines = async () => {
      try {
        setLoading(true);
        const response = await api.get('/pipelines');
        setPipelines(response.data.pipelines || []);
      } catch (err) {
        console.error('Failed to fetch pipelines:', err);
        setError('Failed to load pipelines');
        // Mock data matching CircleCI style
        setPipelines([
          {
            id: '1',
            name: 'chatbot-ui',
            number: '184719',
            status: 'running',
            workflow: 'lint',
            triggerEvent: 'fix-favicon',
            author: { name: 'fix-favicon', avatar: '/api/placeholder/32/32' },
            branch: 'main',
            commit: '2639',
            startedAt: '2024-01-15T10:30:00Z'
          },
          {
            id: '2',
            name: 'app-backend',
            number: '113811',
            status: 'success',
            workflow: 'security-scans',
            triggerEvent: 'add-new',
            author: { name: 'add-new', avatar: '/api/placeholder/32/32' },
            branch: 'main',
            commit: '8292',
            duration: 180,
            startedAt: '2024-01-15T09:15:00Z'
          },
          {
            id: '3',
            name: 'web-ui',
            number: '918047',
            status: 'queued',
            workflow: 'build-test-deploy',
            triggerEvent: 'resize-layout',
            author: { name: 'resize-layout', avatar: '/api/placeholder/32/32' },
            branch: 'main',
            commit: '2639',
            startedAt: '2024-01-15T11:00:00Z'
          },
          {
            id: '4',
            name: 'ai-chatbot',
            number: '123450',
            status: 'needs_approval',
            workflow: 'dataset-update',
            triggerEvent: 'Use parallel',
            author: { name: 'jonnyc', avatar: '/api/placeholder/32/32' },
            branch: 'main',
            commit: '2638',
            startedAt: '2024-01-15T08:45:00Z'
          },
          {
            id: '5',
            name: 'ai-chatbot',
            number: '123450',
            status: 'failed',
            workflow: 'build-test-deploy',
            triggerEvent: 'Use parallel',
            author: { name: 'jonnyc', avatar: '/api/placeholder/32/32' },
            branch: 'main',
            commit: '2638',
            duration: 95,
            startedAt: '2024-01-15T08:30:00Z'
          },
          {
            id: '6',
            name: 'web-ui',
            number: '123450',
            status: 'success',
            workflow: 'build-test-deploy',
            triggerEvent: 'Add new',
            author: { name: 'add-new', avatar: '/api/placeholder/32/32' },
            branch: 'main',
            commit: '2639',
            duration: 142,
            startedAt: '2024-01-15T08:00:00Z'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchPipelines();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-4 h-4 text-red-500" />;
      case 'running':
        return <PlayIcon className="w-4 h-4 text-blue-500" />;
      case 'queued':
        return <ClockIcon className="w-4 h-4 text-gray-500" />;
      case 'needs_approval':
        return <PauseIcon className="w-4 h-4 text-purple-500" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = 'inline-flex items-center px-3 py-1 rounded-full text-xs font-medium';
    switch (status) {
      case 'success':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'failed':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'running':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'queued':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      case 'needs_approval':
        return `${baseClasses} bg-purple-100 text-purple-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return 'Success';
      case 'failed':
        return 'Failed';
      case 'running':
        return 'Running';
      case 'queued':
        return 'Queued';
      case 'needs_approval':
        return 'Needs Approval';
      default:
        return status;
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '';
    return `${seconds}s`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="p-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Pipeline Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Table Header */}
        <div className="grid grid-cols-12 gap-4 px-6 py-3 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
          <div className="col-span-3">Project</div>
          <div className="col-span-2">Status</div>
          <div className="col-span-2">Workflow</div>
          <div className="col-span-5">Trigger Event</div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {pipelines.map((pipeline) => (
            <Link
              key={pipeline.id}
              to={`/pipelines/${pipeline.id}`}
              className="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors items-center"
            >
              {/* Project Column */}
              <div className="col-span-3">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(pipeline.status)}
                  <div>
                    <div className="text-sm font-medium text-blue-600 hover:text-blue-800">
                      {pipeline.name} {pipeline.number}
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Column */}
              <div className="col-span-2">
                <span className={getStatusBadge(pipeline.status)}>
                  {getStatusText(pipeline.status)}
                </span>
              </div>

              {/* Workflow Column */}
              <div className="col-span-2">
                <span className="text-sm text-blue-600 hover:text-blue-800">
                  {pipeline.workflow}
                </span>
              </div>

              {/* Trigger Event Column */}
              <div className="col-span-5">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <UserIcon className="w-4 h-4 text-gray-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {pipeline.triggerEvent}
                      </div>
                      <div className="text-sm text-gray-500">
                        {pipeline.commit} / {pipeline.branch}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    {pipeline.duration && (
                      <div className="text-sm text-gray-500">
                        {formatDuration(pipeline.duration)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {pipelines.length === 0 && !loading && (
        <div className="text-center py-12">
          <PlayIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No pipelines</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first pipeline.
          </p>
        </div>
      )}
    </div>
  );
}
