import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PauseIcon,
  UserIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

interface Pipeline {
  id: string;
  name: string;
  number: string;
  status: 'running' | 'success' | 'failed' | 'queued' | 'needs_approval';
  workflow: string;
  triggerEvent: string;
  author: {
    name: string;
    avatar?: string;
  };
  branch: string;
  commit: string;
  duration?: number;
  startedAt: string;
}

export default function Pipelines() {
  const [loading, setLoading] = useState(true);

  // Mock data matching CircleCI style exactly
  const pipelines: Pipeline[] = [
    {
      id: '1',
      name: 'chatbot-ui',
      number: '184719',
      status: 'running',
      workflow: 'lint',
      triggerEvent: 'fix-favicon',
      author: { name: 'fix-favicon' },
      branch: 'main',
      commit: '2639',
      startedAt: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      name: 'app-backend',
      number: '113811',
      status: 'success',
      workflow: 'security-scans',
      triggerEvent: 'add-new',
      author: { name: 'add-new' },
      branch: 'main',
      commit: '8292',
      duration: 180,
      startedAt: '2024-01-15T09:15:00Z'
    },
    {
      id: '3',
      name: 'web-ui',
      number: '918047',
      status: 'queued',
      workflow: 'build-test-deploy',
      triggerEvent: 'resize-layout',
      author: { name: 'resize-layout' },
      branch: 'main',
      commit: '2639',
      startedAt: '2024-01-15T11:00:00Z'
    },
    {
      id: '4',
      name: 'ai-chatbot',
      number: '123450',
      status: 'needs_approval',
      workflow: 'dataset-update',
      triggerEvent: 'Use parallel',
      author: { name: 'jonnyc' },
      branch: 'main',
      commit: '2638',
      startedAt: '2024-01-15T08:45:00Z'
    },
    {
      id: '5',
      name: 'ai-chatbot',
      number: '123450',
      status: 'failed',
      workflow: 'build-test-deploy',
      triggerEvent: 'Use parallel',
      author: { name: 'jonnyc' },
      branch: 'main',
      commit: '2638',
      duration: 95,
      startedAt: '2024-01-15T08:30:00Z'
    },
    {
      id: '6',
      name: 'web-ui',
      number: '123450',
      status: 'success',
      workflow: 'build-test-deploy',
      triggerEvent: 'Add new',
      author: { name: 'add-new' },
      branch: 'main',
      commit: '2639',
      duration: 142,
      startedAt: '2024-01-15T08:00:00Z'
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-4 h-4 text-red-500" />;
      case 'running':
        return <PlayIcon className="w-4 h-4 text-blue-500" />;
      case 'queued':
        return <ClockIcon className="w-4 h-4 text-gray-500" />;
      case 'needs_approval':
        return <PauseIcon className="w-4 h-4 text-purple-500" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = 'inline-flex items-center px-2 py-1 rounded text-xs font-medium';
    switch (status) {
      case 'success':
        return `${baseClasses} bg-green-500 text-white`;
      case 'failed':
        return `${baseClasses} bg-red-500 text-white`;
      case 'running':
        return `${baseClasses} bg-blue-500 text-white`;
      case 'queued':
        return `${baseClasses} bg-gray-500 text-white`;
      case 'needs_approval':
        return `${baseClasses} bg-purple-500 text-white`;
      default:
        return `${baseClasses} bg-gray-500 text-white`;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return 'Success';
      case 'failed':
        return 'Failed';
      case 'running':
        return 'Running';
      case 'queued':
        return 'Queued';
      case 'needs_approval':
        return 'Needs Approval';
      default:
        return status;
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '';
    return `${seconds}s`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* CircleCI-style Pipeline Table */}
      <div className="bg-white">
        {/* Table Header - Exact CircleCI style */}
        <div className="grid grid-cols-12 gap-6 px-6 py-4 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-600">
          <div className="col-span-3">Project</div>
          <div className="col-span-2">Status</div>
          <div className="col-span-3">Workflow</div>
          <div className="col-span-4">Trigger Event</div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-100">
          {pipelines.map((pipeline) => (
            <div
              key={pipeline.id}
              className="grid grid-cols-12 gap-6 px-6 py-4 hover:bg-gray-50 transition-colors items-center cursor-pointer"
            >
              {/* Project Column */}
              <div className="col-span-3">
                <div className="flex items-center space-x-3">
                  <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                  <div>
                    <div className="text-sm font-medium text-blue-600 hover:text-blue-800">
                      {pipeline.name} {pipeline.number}
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Column */}
              <div className="col-span-2">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(pipeline.status)}
                  <span className={getStatusBadge(pipeline.status)}>
                    {getStatusText(pipeline.status)}
                  </span>
                </div>
              </div>

              {/* Workflow Column */}
              <div className="col-span-3">
                <span className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                  {pipeline.workflow}
                </span>
              </div>

              {/* Trigger Event Column */}
              <div className="col-span-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">
                        {pipeline.author.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {pipeline.author.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {pipeline.commit} / {pipeline.triggerEvent}
                      </div>
                    </div>
                  </div>
                  <div className="text-right text-sm text-gray-500">
                    {pipeline.duration ? `${formatDuration(pipeline.duration)}` : 'Running...'}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {pipelines.length === 0 && !loading && (
        <div className="text-center py-12 bg-white">
          <PlayIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No pipelines</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first pipeline.
          </p>
        </div>
      )}
    </div>
  );
}
