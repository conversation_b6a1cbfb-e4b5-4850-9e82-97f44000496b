import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PauseIcon,
  UserIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';

interface Pipeline {
  id: string;
  project: string;
  number: string;
  status: 'running' | 'success' | 'failed' | 'queued' | 'needs_approval';
  workflow: string;
  triggerEvent: string;
  author: {
    name: string;
    avatar?: string;
  };
  branch: string;
  commit: {
    sha: string;
    message: string;
  };
}

export default function Pipelines() {
  // CircleCI exact match data
  const pipelines: Pipeline[] = [
    {
      id: '1',
      project: 'chatbot-ui',
      number: '184719',
      status: 'running',
      workflow: 'lint',
      triggerEvent: 'fix-favicon',
      author: { name: 'fix-favicon' },
      branch: 'main',
      commit: { sha: '2639', message: 'Update' }
    },
    {
      id: '2',
      project: 'app-backend',
      number: '113811',
      status: 'success',
      workflow: 'security-scans',
      triggerEvent: 'add-new',
      author: { name: 'add-new' },
      branch: 'main',
      commit: { sha: '8292', message: 'Add new' }
    },
    {
      id: '3',
      project: 'web-ui',
      number: '918047',
      status: 'queued',
      workflow: 'build-test-deploy',
      triggerEvent: 'resize-images',
      author: { name: 'resize-images' },
      branch: 'main',
      commit: { sha: '2639', message: 'Resized' }
    },
    {
      id: '4',
      project: 'ai-chatbot',
      number: '123450',
      status: 'needs_approval',
      workflow: 'dataset-update',
      triggerEvent: 'jonnyc',
      author: { name: 'jonnyc' },
      branch: 'main',
      commit: { sha: '2638', message: 'Use parallel' }
    },
    {
      id: '5',
      project: 'ai-chatbot',
      number: '123450',
      status: 'failed',
      workflow: 'build-test-deploy',
      triggerEvent: 'jonnyc',
      author: { name: 'jonnyc' },
      branch: 'main',
      commit: { sha: '2638', message: 'Use parallel' }
    },
    {
      id: '6',
      project: 'web-ui',
      number: '123450',
      status: 'success',
      workflow: 'build-test-deploy',
      triggerEvent: 'add-new',
      author: { name: 'add-new' },
      branch: 'main',
      commit: { sha: '2639', message: 'Add new' }
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-3 h-3 text-white" />;
      case 'failed':
        return <XCircleIcon className="w-3 h-3 text-white" />;
      case 'running':
        return <PlayIcon className="w-3 h-3 text-white" />;
      case 'queued':
        return <ClockIcon className="w-3 h-3 text-white" />;
      case 'needs_approval':
        return <PauseIcon className="w-3 h-3 text-white" />;
      default:
        return <ClockIcon className="w-3 h-3 text-white" />;
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Top Filter Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <UserIcon className="w-5 h-5 text-gray-400" />
              <span className="text-sm text-gray-600">Everyone's Pipelines</span>
            </div>
            <div className="flex items-center space-x-2 bg-gray-100 px-3 py-1 rounded border">
              <span className="text-sm text-gray-700">project-name</span>
              <ChevronDownIcon className="w-4 h-4 text-gray-400" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Main</span>
          </div>
        </div>
      </div>

      {/* Table Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-3">
          <div className="grid grid-cols-4 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
            <div>Project</div>
            <div>Status</div>
            <div>Workflow</div>
            <div>Trigger Event</div>
          </div>
        </div>
      </div>

      {/* Pipeline Rows */}
      <div className="bg-white">
        {pipelines.map((pipeline, index) => (
          <div key={pipeline.id} className={`circleci-table-row px-6 py-4 ${index < pipelines.length - 1 ? 'border-b border-gray-100' : ''}`}>
            <div className="grid grid-cols-4 gap-4 items-center">
              {/* Project Column */}
              <div className="flex items-center space-x-3">
                <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                <Link
                  to={`/pipelines/${pipeline.id}`}
                  className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                >
                  {pipeline.project} {pipeline.number}
                </Link>
              </div>

              {/* Status Column */}
              <div>
                <span className={`status-${pipeline.status}`}>
                  {getStatusIcon(pipeline.status)}
                  <span className="ml-1">
                    {pipeline.status === 'running' && 'Running'}
                    {pipeline.status === 'success' && 'Success'}
                    {pipeline.status === 'failed' && 'Failed'}
                    {pipeline.status === 'queued' && 'Queued'}
                    {pipeline.status === 'needs_approval' && 'Needs Approval'}
                  </span>
                </span>
              </div>

              {/* Workflow Column */}
              <div>
                <Link
                  to={`/pipelines/${pipeline.id}`}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  {pipeline.workflow}
                </Link>
              </div>

              {/* Trigger Event Column */}
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gray-800 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-medium">
                    {pipeline.author.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">{pipeline.triggerEvent}</div>
                  <div className="text-xs text-gray-500">
                    {pipeline.commit.sha} / {pipeline.commit.message}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
