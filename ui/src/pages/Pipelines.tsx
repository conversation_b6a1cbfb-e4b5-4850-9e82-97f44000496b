import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PauseIcon,
  UserIcon,
  ChevronRightIcon,
  PlusIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  EllipsisVerticalIcon,
  CalendarIcon,
  CodeBracketIcon
} from '@heroicons/react/24/outline';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

interface Pipeline {
  id: string;
  name: string;
  number: string;
  status: 'running' | 'success' | 'failed' | 'queued' | 'needs_approval' | 'cancelled';
  workflow: string;
  triggerEvent: string;
  author: {
    name: string;
    avatar?: string;
  };
  branch: string;
  commit: {
    sha: string;
    message: string;
  };
  duration?: number;
  startedAt: string;
  completedAt?: string;
  project: {
    name: string;
    repository: string;
  };
  environment?: string;
  tags?: string[];
}

export default function Pipelines() {
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [branchFilter, setBranchFilter] = useState('all');

  // Enhanced mock data for ChainOps
  const pipelines: Pipeline[] = [
    {
      id: '1',
      name: 'chainops-backend',
      number: '1847',
      status: 'running',
      workflow: 'ci-cd-pipeline',
      triggerEvent: 'feat: add pipeline engine',
      author: { name: 'john.doe', avatar: '/avatars/john.jpg' },
      branch: 'main',
      commit: {
        sha: 'a1b2c3d',
        message: 'feat: add pipeline engine with kubernetes support'
      },
      startedAt: '2024-01-15T10:30:00Z',
      project: {
        name: 'chainops-backend',
        repository: 'github.com/chainops/chainops-backend'
      },
      environment: 'production',
      tags: ['backend', 'go', 'kubernetes']
    },
    {
      id: '2',
      name: 'chainops-ui',
      number: '1138',
      status: 'success',
      workflow: 'build-test-deploy',
      triggerEvent: 'fix: update dashboard layout',
      author: { name: 'jane.smith', avatar: '/avatars/jane.jpg' },
      branch: 'main',
      commit: {
        sha: 'e4f5g6h',
        message: 'fix: update dashboard layout for better UX'
      },
      duration: 180,
      startedAt: '2024-01-15T09:15:00Z',
      completedAt: '2024-01-15T09:18:00Z',
      project: {
        name: 'chainops-ui',
        repository: 'github.com/chainops/chainops-ui'
      },
      environment: 'staging',
      tags: ['frontend', 'react', 'typescript']
    },
    {
      id: '3',
      name: 'chainops-runner',
      number: '918',
      status: 'queued',
      workflow: 'security-scan',
      triggerEvent: 'chore: update dependencies',
      author: { name: 'mike.wilson', avatar: '/avatars/mike.jpg' },
      branch: 'develop',
      commit: {
        sha: 'i7j8k9l',
        message: 'chore: update dependencies and security patches'
      },
      startedAt: '2024-01-15T11:00:00Z',
      project: {
        name: 'chainops-runner',
        repository: 'github.com/chainops/chainops-runner'
      },
      environment: 'development',
      tags: ['runner', 'docker', 'security']
    },
    {
      id: '4',
      name: 'chainops-docs',
      number: '234',
      status: 'needs_approval',
      workflow: 'deploy-docs',
      triggerEvent: 'docs: add API documentation',
      author: { name: 'sarah.connor', avatar: '/avatars/sarah.jpg' },
      branch: 'feature/api-docs',
      commit: {
        sha: 'm1n2o3p',
        message: 'docs: add comprehensive API documentation'
      },
      startedAt: '2024-01-15T08:45:00Z',
      project: {
        name: 'chainops-docs',
        repository: 'github.com/chainops/chainops-docs'
      },
      environment: 'production',
      tags: ['docs', 'markdown', 'api']
    },
    {
      id: '5',
      name: 'chainops-plugins',
      number: '567',
      status: 'failed',
      workflow: 'test-and-build',
      triggerEvent: 'feat: add slack integration',
      author: { name: 'alex.turner', avatar: '/avatars/alex.jpg' },
      branch: 'feature/slack-plugin',
      commit: {
        sha: 'q4r5s6t',
        message: 'feat: add slack integration plugin'
      },
      duration: 95,
      startedAt: '2024-01-15T08:30:00Z',
      completedAt: '2024-01-15T08:31:35Z',
      project: {
        name: 'chainops-plugins',
        repository: 'github.com/chainops/chainops-plugins'
      },
      environment: 'staging',
      tags: ['plugins', 'integrations', 'slack']
    },
    {
      id: '6',
      name: 'chainops-cli',
      number: '789',
      status: 'success',
      workflow: 'release-pipeline',
      triggerEvent: 'release: v1.2.0',
      author: { name: 'david.chen', avatar: '/avatars/david.jpg' },
      branch: 'main',
      commit: {
        sha: 'u7v8w9x',
        message: 'release: v1.2.0 with new features and bug fixes'
      },
      duration: 142,
      startedAt: '2024-01-15T08:00:00Z',
      completedAt: '2024-01-15T08:02:22Z',
      project: {
        name: 'chainops-cli',
        repository: 'github.com/chainops/chainops-cli'
      },
      environment: 'production',
      tags: ['cli', 'go', 'release']
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 800);
    return () => clearTimeout(timer);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-4 h-4 text-red-500" />;
      case 'running':
        return <PlayIcon className="w-4 h-4 text-blue-500" />;
      case 'queued':
        return <ClockIcon className="w-4 h-4 text-gray-500" />;
      case 'needs_approval':
        return <PauseIcon className="w-4 h-4 text-purple-500" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = 'inline-flex items-center px-2 py-1 rounded text-xs font-medium';
    switch (status) {
      case 'success':
        return `${baseClasses} bg-green-500 text-white`;
      case 'failed':
        return `${baseClasses} bg-red-500 text-white`;
      case 'running':
        return `${baseClasses} bg-blue-500 text-white`;
      case 'queued':
        return `${baseClasses} bg-gray-500 text-white`;
      case 'needs_approval':
        return `${baseClasses} bg-purple-500 text-white`;
      default:
        return `${baseClasses} bg-gray-500 text-white`;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return 'Success';
      case 'failed':
        return 'Failed';
      case 'running':
        return 'Running';
      case 'queued':
        return 'Queued';
      case 'needs_approval':
        return 'Needs Approval';
      default:
        return status;
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const getEnvironmentColor = (env?: string) => {
    switch (env) {
      case 'production':
        return 'bg-red-100 text-red-800';
      case 'staging':
        return 'bg-yellow-100 text-yellow-800';
      case 'development':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Filter pipelines based on search and filters
  const filteredPipelines = pipelines.filter(pipeline => {
    const matchesSearch = searchQuery === '' ||
      pipeline.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pipeline.commit.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pipeline.author.name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || pipeline.status === statusFilter;
    const matchesBranch = branchFilter === 'all' || pipeline.branch === branchFilter;

    return matchesSearch && matchesStatus && matchesBranch;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Recent Pipeline Runs</h2>
          <p className="text-sm text-gray-600">Monitor and manage your CI/CD pipeline executions</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="btn btn-secondary btn-sm">
            <ArrowPathIcon className="w-4 h-4 mr-2" />
            Refresh
          </button>
          <Link to="/pipelines/new" className="btn btn-primary btn-sm">
            <PlusIcon className="w-4 h-4 mr-2" />
            New Pipeline
          </Link>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search pipelines, commits, or authors..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="form-input pl-10 w-full"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="form-input w-full"
              >
                <option value="all">All Statuses</option>
                <option value="running">Running</option>
                <option value="success">Success</option>
                <option value="failed">Failed</option>
                <option value="queued">Queued</option>
                <option value="needs_approval">Needs Approval</option>
              </select>
            </div>

            {/* Branch Filter */}
            <div className="sm:w-48">
              <select
                value={branchFilter}
                onChange={(e) => setBranchFilter(e.target.value)}
                className="form-input w-full"
              >
                <option value="all">All Branches</option>
                <option value="main">main</option>
                <option value="develop">develop</option>
                <option value="feature/api-docs">feature/api-docs</option>
                <option value="feature/slack-plugin">feature/slack-plugin</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Pipeline Table */}
      <div className="card">
        <div className="overflow-hidden">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">Pipeline</th>
                <th className="table-header-cell">Status</th>
                <th className="table-header-cell">Workflow</th>
                <th className="table-header-cell">Commit</th>
                <th className="table-header-cell">Environment</th>
                <th className="table-header-cell">Duration</th>
                <th className="table-header-cell">Started</th>
                <th className="table-header-cell"></th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredPipelines.map((pipeline) => (
                <tr key={pipeline.id} className="table-row">
                  {/* Pipeline Column */}
                  <td className="table-cell">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(pipeline.status)}
                      <div>
                        <Link
                          to={`/pipelines/${pipeline.id}`}
                          className="text-sm font-medium text-blue-600 hover:text-blue-800"
                        >
                          {pipeline.project.name} #{pipeline.number}
                        </Link>
                        <div className="text-xs text-gray-500">{pipeline.project.repository}</div>
                      </div>
                    </div>
                  </td>

                  {/* Status Column */}
                  <td className="table-cell">
                    <span className={getStatusBadge(pipeline.status)}>
                      {getStatusText(pipeline.status)}
                    </span>
                  </td>

                  {/* Workflow Column */}
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      <CodeBracketIcon className="w-4 h-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-900">{pipeline.workflow}</span>
                    </div>
                  </td>

                  {/* Commit Column */}
                  <td className="table-cell">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-xs font-medium">
                        {pipeline.author.name.split('.')[0].charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                          {pipeline.commit.message}
                        </div>
                        <div className="text-xs text-gray-500">
                          {pipeline.commit.sha} • {pipeline.branch} • {pipeline.author.name}
                        </div>
                      </div>
                    </div>
                  </td>

                  {/* Environment Column */}
                  <td className="table-cell">
                    {pipeline.environment && (
                      <span className={`status-badge ${getEnvironmentColor(pipeline.environment)}`}>
                        {pipeline.environment}
                      </span>
                    )}
                  </td>

                  {/* Duration Column */}
                  <td className="table-cell">
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <ClockIcon className="w-4 h-4" />
                      <span>
                        {pipeline.duration ? formatDuration(pipeline.duration) : 'Running...'}
                      </span>
                    </div>
                  </td>

                  {/* Started Column */}
                  <td className="table-cell">
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <CalendarIcon className="w-4 h-4" />
                      <span>{formatTimeAgo(pipeline.startedAt)}</span>
                    </div>
                  </td>

                  {/* Actions Column */}
                  <td className="table-cell">
                    <button className="p-1 text-gray-400 hover:text-gray-600 rounded">
                      <EllipsisVerticalIcon className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {filteredPipelines.length === 0 && !loading && (
        <div className="card">
          <div className="card-body text-center py-12">
            <PlayIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No pipelines found</h3>
            <p className="text-gray-500 mb-6">
              {searchQuery || statusFilter !== 'all' || branchFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first pipeline'}
            </p>
            <Link to="/pipelines/new" className="btn btn-primary">
              <PlusIcon className="w-4 h-4 mr-2" />
              Create Pipeline
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
