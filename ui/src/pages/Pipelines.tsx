import React from 'react';
import { Link } from 'react-router-dom';
import {
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PauseIcon,
  UserIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';

interface Pipeline {
  id: string;
  project: string;
  number: string;
  status: 'running' | 'success' | 'failed' | 'queued' | 'needs_approval';
  workflow: string;
  triggerEvent: string;
  author: {
    name: string;
    avatar?: string;
  };
  branch: string;
  commit: {
    sha: string;
    message: string;
  };
}

export default function Pipelines() {
  // Pipeline data matching specifications
  const pipelines: Pipeline[] = [
    {
      id: '1',
      project: 'chainops-frontend',
      number: '#184719',
      status: 'running',
      workflow: 'build-test-deploy',
      triggerEvent: 'john.doe',
      author: { name: 'john.doe' },
      branch: 'main',
      commit: { sha: 'a1b2c3d', message: 'Fix navigation bug in sidebar component' }
    },
    {
      id: '2',
      project: 'chainops-backend',
      number: '#113811',
      status: 'success',
      workflow: 'lint',
      triggerEvent: 'jane.smith',
      author: { name: 'jane.smith' },
      branch: 'main',
      commit: { sha: 'e4f5g6h', message: 'Add new API endpoints for user management' }
    },
    {
      id: '3',
      project: 'chainops-ui',
      number: '#918047',
      status: 'failed',
      workflow: 'security-scan',
      triggerEvent: 'mike.wilson',
      author: { name: 'mike.wilson' },
      branch: 'develop',
      commit: { sha: 'i7j8k9l', message: 'Update dependencies to latest versions' }
    },
    {
      id: '4',
      project: 'chainops-docs',
      number: '#123450',
      status: 'needs_approval',
      workflow: 'deploy-docs',
      triggerEvent: 'sarah.connor',
      author: { name: 'sarah.connor' },
      branch: 'feature/new-docs',
      commit: { sha: 'm2n3o4p', message: 'Update documentation for new features' }
    },
    {
      id: '5',
      project: 'chainops-runner',
      number: '#567890',
      status: 'queued',
      workflow: 'integration-tests',
      triggerEvent: 'alex.johnson',
      author: { name: 'alex.johnson' },
      branch: 'main',
      commit: { sha: 'q5r6s7t', message: 'Refactor runner architecture' }
    },
    {
      id: '6',
      project: 'chainops-api',
      number: '#234567',
      status: 'success',
      workflow: 'build-test-deploy',
      triggerEvent: 'emma.davis',
      author: { name: 'emma.davis' },
      branch: 'main',
      commit: { sha: 'u8v9w0x', message: 'Add comprehensive integration tests' }
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-3 h-3 text-white" />;
      case 'failed':
        return <XCircleIcon className="w-3 h-3 text-white" />;
      case 'running':
        return <PlayIcon className="w-3 h-3 text-white" />;
      case 'queued':
        return <ClockIcon className="w-3 h-3 text-white" />;
      case 'needs_approval':
        return <PauseIcon className="w-3 h-3 text-white" />;
      default:
        return <ClockIcon className="w-3 h-3 text-white" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">All Pipelines</h1>
        <p className="text-gray-600 dark:text-gray-300">Monitor and manage your CI/CD pipeline runs</p>
      </div>

      <div className="bg-gray-50 min-h-screen">
        {/* Top Filter Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <UserIcon className="w-5 h-5 text-gray-400" />
              <select className="text-sm text-gray-600 bg-transparent border-none focus:outline-none">
                <option>Everyone's Pipelines</option>
                <option>My Pipelines</option>
              </select>
            </div>
            <div className="flex items-center space-x-2 bg-gray-100 px-3 py-1 rounded border">
              <input
                type="text"
                placeholder="Project Name"
                className="text-sm text-gray-700 bg-transparent border-none focus:outline-none w-32"
              />
              <ChevronDownIcon className="w-4 h-4 text-gray-400" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <select className="text-sm text-gray-600 bg-transparent border-none focus:outline-none">
              <option>Main</option>
              <option>Develop</option>
              <option>Feature branches</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-3">
          <div className="grid grid-cols-4 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
            <div>Project name with ID</div>
            <div>Status</div>
            <div>Workflow</div>
            <div>Triggered by</div>
          </div>
        </div>
      </div>

      {/* Pipeline Rows */}
      <div className="bg-white">
        {pipelines.map((pipeline, index) => (
          <div key={pipeline.id} className={`circleci-table-row px-6 py-4 ${index < pipelines.length - 1 ? 'border-b border-gray-100' : ''}`}>
            <div className="grid grid-cols-4 gap-4 items-center">
              {/* Project Column */}
              <div className="flex items-center space-x-3">
                <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                <Link
                  to={`/pipelines/${pipeline.id}`}
                  className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                >
                  {pipeline.project} {pipeline.number}
                </Link>
              </div>

              {/* Status Column */}
              <div>
                <span className={`status-${pipeline.status}`}>
                  {getStatusIcon(pipeline.status)}
                  <span className="ml-1">
                    {pipeline.status === 'running' && 'Running'}
                    {pipeline.status === 'success' && 'Success'}
                    {pipeline.status === 'failed' && 'Failed'}
                    {pipeline.status === 'queued' && 'Queued'}
                    {pipeline.status === 'needs_approval' && 'Needs Approval'}
                  </span>
                </span>
              </div>

              {/* Workflow Column */}
              <div>
                <Link
                  to={`/pipelines/${pipeline.id}`}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  {pipeline.workflow}
                </Link>
              </div>

              {/* Triggered by Column */}
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gray-800 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-medium">
                    {pipeline.author.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">{pipeline.triggerEvent}</div>
                  <div className="text-xs text-gray-500">
                    {pipeline.commit.message} • {pipeline.commit.sha}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      </div>
    </div>
  );
}
