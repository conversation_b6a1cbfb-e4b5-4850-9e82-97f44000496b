import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import {
  PlayIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  PlusIcon,
  RocketLaunchIcon,
  KeyIcon,
  BellIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  CpuChipIcon,
  ServerIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { api } from '../services/api';

interface DashboardStats {
  totalPipelines: number;
  totalExecutions: number;
  runningExecutions: number;
  successRate: number;
  totalDeployments: number;
  activeDeployments: number;
  totalSecrets: number;
  systemHealth: number;
}

interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: number;
  activeAlerts: number;
}

interface Alert {
  id: string;
  title: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
}

interface RecentExecution {
  id: string;
  pipeline_name: string;
  status: string;
  created_at: string;
  duration?: number;
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalPipelines: 0,
    totalExecutions: 0,
    runningExecutions: 0,
    successRate: 0,
    totalDeployments: 0,
    activeDeployments: 0,
    totalSecrets: 0,
    systemHealth: 0,
  });
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    cpuUsage: 65,
    memoryUsage: 78,
    activeAlerts: 2,
  });
  const [alerts, setAlerts] = useState<Alert[]>([
    {
      id: '1',
      title: 'High CPU Usage',
      severity: 'high',
      timestamp: '2024-01-15T11:30:00Z',
    },
    {
      id: '2',
      title: 'Pipeline Failure Rate Increased',
      severity: 'medium',
      timestamp: '2024-01-15T11:15:00Z',
    },
  ]);
  const [recentExecutions, setRecentExecutions] = useState<RecentExecution[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Use mock data for demonstration
      setStats({
        totalPipelines: 12,
        totalExecutions: 1247,
        runningExecutions: 3,
        successRate: 95.3,
        totalDeployments: 342,
        activeDeployments: 5,
        totalSecrets: 28,
        systemHealth: 98.5,
      });

      // Mock recent executions
      setRecentExecutions([
        {
          id: '1',
          pipeline_name: 'Frontend Build & Deploy',
          status: 'success',
          created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
          duration: 180,
          commit_sha: 'a1b2c3d',
          commit_message: 'Add new dashboard features',
          branch: 'main'
        },
        {
          id: '2',
          pipeline_name: 'Backend API Tests',
          status: 'running',
          created_at: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
          duration: 0,
          commit_sha: 'e4f5g6h',
          commit_message: 'Fix authentication bug',
          branch: 'develop'
        },
        {
          id: '3',
          pipeline_name: 'Security Scan',
          status: 'success',
          created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          duration: 95,
          commit_sha: 'i7j8k9l',
          commit_message: 'Update dependencies',
          branch: 'main'
        },
        {
          id: '4',
          pipeline_name: 'Local Testing Demo',
          status: 'success',
          created_at: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
          duration: 12,
          commit_sha: 'local',
          commit_message: 'Local pipeline execution test',
          branch: 'local'
        }
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'failure':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'running':
        return <PlayIcon className="w-5 h-5 text-blue-500" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failure':
        return 'bg-red-100 text-red-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Welcome to ChainOps</h1>
            <p className="text-gray-600 mt-1">
              Manage your CI/CD pipelines with ease
            </p>
          </div>
          <Link
            to="/pipelines/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            Create Pipeline
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <PlayIcon className="w-5 h-5 text-blue-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Pipelines</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalPipelines}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <CheckCircleIcon className="w-5 h-5 text-green-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Executions</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalExecutions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                <ClockIcon className="w-5 h-5 text-yellow-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Running</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.runningExecutions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                <CheckCircleIcon className="w-5 h-5 text-purple-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Success Rate</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.successRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-100 rounded-md flex items-center justify-center">
                <RocketLaunchIcon className="w-5 h-5 text-orange-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Deployments</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalDeployments}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                <RocketLaunchIcon className="w-5 h-5 text-red-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Deployments</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.activeDeployments}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-indigo-100 rounded-md flex items-center justify-center">
                <KeyIcon className="w-5 h-5 text-indigo-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Secrets</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalSecrets}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <ArrowTrendingUpIcon className="w-5 h-5 text-green-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">System Health</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.systemHealth.toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* System Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CpuChipIcon className="w-8 h-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">CPU Usage</p>
                <p className="text-2xl font-bold text-gray-900">{systemMetrics.cpuUsage}%</p>
              </div>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full"
                style={{ width: `${systemMetrics.cpuUsage}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ServerIcon className="w-8 h-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Memory Usage</p>
                <p className="text-2xl font-bold text-gray-900">{systemMetrics.memoryUsage}%</p>
              </div>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full"
                style={{ width: `${systemMetrics.memoryUsage}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="w-8 h-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                <p className="text-2xl font-bold text-gray-900">{systemMetrics.activeAlerts}</p>
              </div>
            </div>
            <Link
              to="/monitoring"
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              View All
            </Link>
          </div>
        </div>
      </div>

      {/* Active Alerts */}
      {alerts.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Active Alerts</h3>
              <Link
                to="/monitoring"
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                View All
              </Link>
            </div>
          </div>
          <div className="divide-y divide-gray-200">
            {alerts.slice(0, 3).map((alert) => (
              <div key={alert.id} className="px-6 py-4">
                <div className="flex items-center space-x-3">
                  <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(alert.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                    alert.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                    alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {alert.severity}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Executions */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Executions</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {recentExecutions.length === 0 ? (
            <div className="px-6 py-8 text-center">
              <p className="text-gray-500">No executions yet</p>
              <p className="text-sm text-gray-400 mt-1">
                Create a pipeline and trigger an execution to see it here
              </p>
            </div>
          ) : (
            recentExecutions.map((execution) => (
              <div key={execution.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {getStatusIcon(execution.status)}
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">
                        {execution.pipeline_name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(execution.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                        execution.status
                      )}`}
                    >
                      {execution.status}
                    </span>
                    {execution.duration && (
                      <span className="text-sm text-gray-500">
                        {Math.round(execution.duration)}s
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Link
            to="/pipelines/new"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <PlusIcon className="w-6 h-6 text-blue-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">Create Pipeline</p>
              <p className="text-sm text-gray-500">Set up a new CI/CD pipeline</p>
            </div>
          </Link>

          <Link
            to="/pipelines"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <PlayIcon className="w-6 h-6 text-green-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">View Pipelines</p>
              <p className="text-sm text-gray-500">Manage existing pipelines</p>
            </div>
          </Link>

          <Link
            to="/executions"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <ClockIcon className="w-6 h-6 text-purple-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">View Executions</p>
              <p className="text-sm text-gray-500">Monitor pipeline runs</p>
            </div>
          </Link>

          <Link
            to="/deployments"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <RocketLaunchIcon className="w-6 h-6 text-orange-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">Deployments</p>
              <p className="text-sm text-gray-500">Manage deployments</p>
            </div>
          </Link>

          <Link
            to="/secrets"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <KeyIcon className="w-6 h-6 text-indigo-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">Secrets</p>
              <p className="text-sm text-gray-500">Manage secrets</p>
            </div>
          </Link>

          <Link
            to="/monitoring"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <ChartBarIcon className="w-6 h-6 text-red-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">Monitoring</p>
              <p className="text-sm text-gray-500">System metrics</p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
