import React from 'react';
import { 
  CheckIcon,
  XMarkIcon,
  C<PERSON>rencyDollarIcon,
  StarIcon
} from '@heroicons/react/24/outline';

interface PlanFeature {
  name: string;
  included: boolean;
}

interface Plan {
  name: string;
  price: string;
  description: string;
  features: PlanFeature[];
  popular?: boolean;
  current?: boolean;
}

export default function Plan() {
  const plans: Plan[] = [
    {
      name: 'Free',
      price: '$0',
      description: 'Perfect for getting started with CI/CD',
      current: true,
      features: [
        { name: '1,000 build minutes/month', included: true },
        { name: 'Up to 5 projects', included: true },
        { name: 'Basic support', included: true },
        { name: 'Community templates', included: true },
        { name: 'Advanced security features', included: false },
        { name: 'Priority support', included: false },
        { name: 'Custom runners', included: false },
        { name: 'Advanced analytics', included: false }
      ]
    },
    {
      name: 'Performance',
      price: '$30',
      description: 'For teams that need more power and flexibility',
      popular: true,
      features: [
        { name: '25,000 build minutes/month', included: true },
        { name: 'Unlimited projects', included: true },
        { name: 'Priority support', included: true },
        { name: 'Community templates', included: true },
        { name: 'Advanced security features', included: true },
        { name: 'Custom runners', included: true },
        { name: 'Advanced analytics', included: false },
        { name: 'SLA guarantee', included: false }
      ]
    },
    {
      name: 'Scale',
      price: '$200',
      description: 'For large teams and enterprise needs',
      features: [
        { name: '100,000 build minutes/month', included: true },
        { name: 'Unlimited projects', included: true },
        { name: 'Premium support', included: true },
        { name: 'Community templates', included: true },
        { name: 'Advanced security features', included: true },
        { name: 'Custom runners', included: true },
        { name: 'Advanced analytics', included: true },
        { name: 'SLA guarantee', included: true }
      ]
    }
  ];

  return (
    <div className="p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Scale your CI/CD workflows with plans designed for teams of all sizes
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {plans.map((plan) => (
          <div
            key={plan.name}
            className={`relative bg-white rounded-lg shadow-sm border-2 p-8 ${
              plan.popular 
                ? 'border-blue-500 ring-2 ring-blue-200' 
                : plan.current
                ? 'border-green-500'
                : 'border-gray-200'
            }`}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500 text-white">
                  <StarIcon className="w-3 h-3 mr-1" />
                  Most Popular
                </span>
              </div>
            )}

            {plan.current && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500 text-white">
                  <CheckIcon className="w-3 h-3 mr-1" />
                  Current Plan
                </span>
              </div>
            )}

            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
              <div className="flex items-center justify-center mb-2">
                <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                <span className="text-gray-500 ml-2">/month</span>
              </div>
              <p className="text-gray-600">{plan.description}</p>
            </div>

            <ul className="space-y-3 mb-8">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-center">
                  {feature.included ? (
                    <CheckIcon className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                  ) : (
                    <XMarkIcon className="w-5 h-5 text-gray-300 mr-3 flex-shrink-0" />
                  )}
                  <span className={feature.included ? 'text-gray-900' : 'text-gray-400'}>
                    {feature.name}
                  </span>
                </li>
              ))}
            </ul>

            <button
              className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                plan.current
                  ? 'bg-green-100 text-green-800 cursor-default'
                  : plan.popular
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
              }`}
              disabled={plan.current}
            >
              {plan.current ? 'Current Plan' : 'Upgrade'}
            </button>
          </div>
        ))}
      </div>

      <div className="mt-12 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Need something custom?</h2>
        <p className="text-gray-600 mb-6">
          Contact our sales team for enterprise pricing and custom solutions
        </p>
        <button className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800">
          <CurrencyDollarIcon className="w-5 h-5 mr-2" />
          Contact Sales
        </button>
      </div>

      <div className="mt-12 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Frequently Asked Questions</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900">What happens if I exceed my build minutes?</h4>
            <p className="text-gray-600 text-sm mt-1">
              You'll be charged $0.008 per additional minute. You can set spending limits in your account settings.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900">Can I change plans anytime?</h4>
            <p className="text-gray-600 text-sm mt-1">
              Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900">Do you offer annual discounts?</h4>
            <p className="text-gray-600 text-sm mt-1">
              Yes, save 20% when you pay annually. Contact sales for more details.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
