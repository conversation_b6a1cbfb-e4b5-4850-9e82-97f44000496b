import React, { useState, useEffect } from 'react';
import {
  LinkIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Input } from '../components/ui/Input';
import { Select } from '../components/ui/Select';
import { DataTable } from '../components/ui/Table';
import { Modal } from '../components/ui/Modal';
import { Badge } from '../components/ui/Badge';
import { Tabs } from '../components/ui/Tabs';
import { api } from '../services/api';
import toast from 'react-hot-toast';

interface Integration {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  config: Record<string, any>;
  status: 'connected' | 'disconnected' | 'error';
  lastSync?: string;
  webhookUrl?: string;
}

interface WebhookEvent {
  id: string;
  integration_id: string;
  event_type: string;
  payload: Record<string, any>;
  status: 'processed' | 'failed' | 'pending';
  created_at: string;
  processed_at?: string;
  error_message?: string;
}

const Integrations: React.FC = () => {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [webhookEvents, setWebhookEvents] = useState<WebhookEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);
  const [showIntegrationModal, setShowIntegrationModal] = useState(false);
  const [activeTab, setActiveTab] = useState('integrations');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [integrationsRes, webhookEventsRes] = await Promise.all([
        api.get('/integrations'),
        api.get('/integrations/webhook-events'),
      ]);
      setIntegrations(integrationsRes.data);
      setWebhookEvents(webhookEventsRes.data);
    } catch (error) {
      toast.error('Failed to load integration data');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateIntegration = () => {
    setSelectedIntegration(null);
    setShowIntegrationModal(true);
  };

  const handleEditIntegration = (integration: Integration) => {
    setSelectedIntegration(integration);
    setShowIntegrationModal(true);
  };

  const handleDeleteIntegration = async (integrationId: string) => {
    if (!confirm('Are you sure you want to delete this integration?')) return;

    try {
      await api.delete(`/integrations/${integrationId}`);
      setIntegrations(integrations.filter(i => i.id !== integrationId));
      toast.success('Integration deleted successfully');
    } catch (error) {
      toast.error('Failed to delete integration');
      console.error(error);
    }
  };

  const handleTestIntegration = async (integrationId: string) => {
    try {
      await api.post(`/integrations/${integrationId}/test`);
      toast.success('Integration test successful');
      loadData(); // Refresh to get updated status
    } catch (error) {
      toast.error('Integration test failed');
      console.error(error);
    }
  };

  const handleSyncIntegration = async (integrationId: string) => {
    try {
      await api.post(`/integrations/${integrationId}/sync`);
      toast.success('Integration sync started');
      loadData(); // Refresh to get updated status
    } catch (error) {
      toast.error('Failed to sync integration');
      console.error(error);
    }
  };

  const integrationColumns = [
    {
      key: 'name',
      label: 'Integration',
      render: (integration: Integration) => (
        <div className="flex items-center">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
            {integration.type === 'github' ? '🐙' : 
             integration.type === 'gitlab' ? '🦊' : 
             integration.type === 'bitbucket' ? '🪣' : '🔗'}
          </div>
          <div>
            <div className="font-medium">{integration.name}</div>
            <div className="text-sm text-gray-500">{integration.type}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (integration: Integration) => (
        <Badge
          variant={
            integration.status === 'connected' ? 'success' :
            integration.status === 'disconnected' ? 'warning' : 'error'
          }
        >
          {integration.status}
        </Badge>
      ),
    },
    {
      key: 'enabled',
      label: 'Enabled',
      render: (integration: Integration) => (
        <Badge variant={integration.enabled ? 'success' : 'error'}>
          {integration.enabled ? 'Yes' : 'No'}
        </Badge>
      ),
    },
    {
      key: 'lastSync',
      label: 'Last Sync',
      render: (integration: Integration) => 
        integration.lastSync ? new Date(integration.lastSync).toLocaleString() : 'Never',
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (integration: Integration) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTestIntegration(integration.id)}
            icon={<CheckCircleIcon className="w-4 h-4" />}
          >
            Test
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSyncIntegration(integration.id)}
            icon={<LinkIcon className="w-4 h-4" />}
          >
            Sync
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditIntegration(integration)}
            icon={<PencilIcon className="w-4 h-4" />}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteIntegration(integration.id)}
            icon={<TrashIcon className="w-4 h-4" />}
            className="text-red-600 hover:text-red-700"
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const webhookEventColumns = [
    {
      key: 'event_type',
      label: 'Event Type',
      render: (event: WebhookEvent) => (
        <Badge variant="outline">
          {event.event_type}
        </Badge>
      ),
    },
    {
      key: 'integration',
      label: 'Integration',
      render: (event: WebhookEvent) => {
        const integration = integrations.find(i => i.id === event.integration_id);
        return integration ? integration.name : 'Unknown';
      },
    },
    {
      key: 'status',
      label: 'Status',
      render: (event: WebhookEvent) => (
        <Badge
          variant={
            event.status === 'processed' ? 'success' :
            event.status === 'failed' ? 'error' : 'warning'
          }
        >
          {event.status}
        </Badge>
      ),
    },
    {
      key: 'created_at',
      label: 'Received',
      render: (event: WebhookEvent) => new Date(event.created_at).toLocaleString(),
    },
    {
      key: 'processed_at',
      label: 'Processed',
      render: (event: WebhookEvent) => 
        event.processed_at ? new Date(event.processed_at).toLocaleString() : '-',
    },
    {
      key: 'error_message',
      label: 'Error',
      render: (event: WebhookEvent) => 
        event.error_message ? (
          <span className="text-red-600 text-sm">{event.error_message}</span>
        ) : '-',
    },
  ];

  const tabs = [
    {
      id: 'integrations',
      label: 'Integrations',
      icon: <LinkIcon className="w-4 h-4" />,
      content: (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Git Integrations</h2>
            <Button
              onClick={handleCreateIntegration}
              icon={<PlusIcon className="w-4 h-4" />}
            >
              Add Integration
            </Button>
          </div>
          <Card>
            <DataTable
              data={integrations}
              columns={integrationColumns}
              loading={loading}
              emptyMessage="No integrations found"
            />
          </Card>
        </div>
      ),
    },
    {
      id: 'webhooks',
      label: 'Webhook Events',
      icon: <CodeBracketIcon className="w-4 h-4" />,
      content: (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Webhook Events</h2>
            <Button onClick={loadData} variant="outline">
              Refresh
            </Button>
          </div>
          <Card>
            <DataTable
              data={webhookEvents}
              columns={webhookEventColumns}
              loading={loading}
              emptyMessage="No webhook events found"
            />
          </Card>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Integrations</h1>
        <p className="text-gray-600">Manage Git integrations and webhook events</p>
      </div>

      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Integration Modal */}
      {showIntegrationModal && (
        <IntegrationModal
          integration={selectedIntegration}
          onSave={(integration) => {
            if (selectedIntegration) {
              setIntegrations(integrations.map(i => i.id === integration.id ? integration : i));
            } else {
              setIntegrations([...integrations, integration]);
            }
            setShowIntegrationModal(false);
            toast.success(`Integration ${selectedIntegration ? 'updated' : 'created'} successfully`);
          }}
          onClose={() => setShowIntegrationModal(false)}
        />
      )}
    </div>
  );
};

// Integration Modal Component
interface IntegrationModalProps {
  integration: Integration | null;
  onSave: (integration: Integration) => void;
  onClose: () => void;
}

const IntegrationModal: React.FC<IntegrationModalProps> = ({
  integration,
  onSave,
  onClose,
}) => {
  const [formData, setFormData] = useState({
    name: integration?.name || '',
    type: integration?.type || 'github',
    enabled: integration?.enabled ?? true,
    config: integration?.config || {},
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      let response;
      if (integration) {
        response = await api.put(`/integrations/${integration.id}`, formData);
      } else {
        response = await api.post('/integrations', formData);
      }

      onSave(response.data);
    } catch (error) {
      toast.error('Failed to save integration');
      console.error(error);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={integration ? 'Edit Integration' : 'Create Integration'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="Integration Name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />

        <Select
          label="Integration Type"
          value={formData.type}
          onChange={(e) => setFormData({ ...formData, type: e.target.value })}
          options={[
            { value: 'github', label: 'GitHub' },
            { value: 'gitlab', label: 'GitLab' },
            { value: 'bitbucket', label: 'Bitbucket' },
          ]}
        />

        {formData.type === 'github' && (
          <>
            <Input
              label="GitHub Token"
              type="password"
              value={formData.config.token || ''}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, token: e.target.value }
              })}
              placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
              required
            />
            <Input
              label="Base URL (optional)"
              value={formData.config.base_url || ''}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, base_url: e.target.value }
              })}
              placeholder="https://api.github.com (leave empty for GitHub.com)"
            />
            <Input
              label="Webhook Secret (optional)"
              type="password"
              value={formData.config.webhook_secret || ''}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, webhook_secret: e.target.value }
              })}
              placeholder="Webhook secret for validating payloads"
            />
          </>
        )}

        {formData.type === 'gitlab' && (
          <>
            <Input
              label="GitLab Token"
              type="password"
              value={formData.config.token || ''}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, token: e.target.value }
              })}
              placeholder="glpat-xxxxxxxxxxxxxxxxxxxx"
              required
            />
            <Input
              label="GitLab URL"
              value={formData.config.base_url || ''}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, base_url: e.target.value }
              })}
              placeholder="https://gitlab.com"
              required
            />
          </>
        )}

        {formData.type === 'bitbucket' && (
          <>
            <Input
              label="Bitbucket Username"
              value={formData.config.username || ''}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, username: e.target.value }
              })}
              required
            />
            <Input
              label="App Password"
              type="password"
              value={formData.config.password || ''}
              onChange={(e) => setFormData({
                ...formData,
                config: { ...formData.config, password: e.target.value }
              })}
              required
            />
          </>
        )}

        <div className="flex items-center">
          <input
            type="checkbox"
            checked={formData.enabled}
            onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
            className="mr-2"
          />
          <label className="text-sm">Enable this integration</label>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            {integration ? 'Update' : 'Create'} Integration
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default Integrations;
