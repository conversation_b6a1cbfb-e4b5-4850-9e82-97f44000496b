import React from 'react';
import {
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CpuChipIcon,
  ServerIcon,
  CloudArrowUpIcon
} from '@heroicons/react/24/outline';

const Insights: React.FC = () => {
  const metrics = [
    {
      name: 'Total Builds',
      value: '2,847',
      change: '+12%',
      trend: 'up',
      icon: ChartBarIcon
    },
    {
      name: 'Success Rate',
      value: '94.2%',
      change: '+2.1%',
      trend: 'up',
      icon: CheckCircleIcon
    },
    {
      name: 'Avg Build Time',
      value: '4m 32s',
      change: '-8%',
      trend: 'down',
      icon: ClockIcon
    },
    {
      name: 'Failed Builds',
      value: '164',
      change: '-15%',
      trend: 'down',
      icon: XCircleIcon
    }
  ];

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Analytics & Insights</h1>
          <p className="text-gray-600 mt-1">
            Monitor your pipeline performance and team productivity
          </p>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {metrics.map((metric) => (
            <div key={metric.name} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.name}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{metric.value}</p>
                </div>
                <metric.icon className="w-8 h-8 text-gray-400" />
              </div>
              <div className="mt-4 flex items-center">
                {metric.trend === 'up' ? (
                  <ArrowTrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
                ) : (
                  <ArrowTrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${
                  metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {metric.change}
                </span>
                <span className="text-sm text-gray-500 ml-1">vs last month</span>
              </div>
            </div>
          ))}
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Build Success Rate</h3>
            <div className="h-64 flex items-center justify-center text-gray-500">
              Chart placeholder - Build success rate over time
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Build Duration Trends</h3>
            <div className="h-64 flex items-center justify-center text-gray-500">
              Chart placeholder - Average build duration trends
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Insights;
