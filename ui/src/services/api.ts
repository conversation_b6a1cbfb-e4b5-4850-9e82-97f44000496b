import axios from 'axios';

// Create axios instance with base configuration
export const api = axios.create({
  baseURL: (import.meta as any).env?.VITE_API_URL || 'http://localhost:8080/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API Types
export interface Pipeline {
  id: string;
  name: string;
  repository: string;
  branch: string;
  config_path: string;
  yaml_content: string;
  is_active: boolean;
  variables: Record<string, string>;
  created_at: string;
  updated_at: string;
}

export interface PipelineExecution {
  id: string;
  pipeline_id: string;
  status: 'pending' | 'queued' | 'running' | 'success' | 'failure' | 'cancelled';
  trigger_type: 'git' | 'manual' | 'scheduled' | 'api';
  trigger_data: Record<string, any>;
  variables: Record<string, string>;
  commit_sha?: string;
  commit_message?: string;
  branch?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
}

export interface Job {
  id: string;
  execution_id: string;
  name: string;
  stage: string;
  status: 'pending' | 'queued' | 'running' | 'success' | 'failure' | 'cancelled';
  runner_type: 'docker' | 'kubernetes';
  image?: string;
  commands: string[];
  environment: Record<string, string>;
  artifacts: Record<string, any>;
  logs_url?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
}

export interface CreatePipelineRequest {
  name: string;
  repository: string;
  branch?: string;
  config_path?: string;
  yaml_content: string;
  variables?: Record<string, string>;
}

export interface ExecutePipelineRequest {
  trigger_type: 'manual';
  variables?: Record<string, string>;
  branch?: string;
  commit_sha?: string;
  commit_message?: string;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends APIResponse<T> {
  pagination: {
    page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}

// Pipeline API
export const pipelineAPI = {
  // Get all pipelines
  list: (page = 1, perPage = 20): Promise<PaginatedResponse<Pipeline[]>> =>
    api.get(`/pipelines?page=${page}&per_page=${perPage}`).then(res => res.data),

  // Get pipeline by ID
  get: (id: string): Promise<APIResponse<Pipeline>> =>
    api.get(`/pipelines/${id}`).then(res => res.data),

  // Create new pipeline
  create: (data: CreatePipelineRequest): Promise<APIResponse<Pipeline>> =>
    api.post('/pipelines', data).then(res => res.data),

  // Update pipeline
  update: (id: string, data: Partial<CreatePipelineRequest>): Promise<APIResponse<Pipeline>> =>
    api.put(`/pipelines/${id}`, data).then(res => res.data),

  // Delete pipeline
  delete: (id: string): Promise<APIResponse> =>
    api.delete(`/pipelines/${id}`).then(res => res.data),

  // Execute pipeline
  execute: (id: string, data: ExecutePipelineRequest): Promise<APIResponse<PipelineExecution>> =>
    api.post(`/pipelines/${id}/execute`, data).then(res => res.data),

  // Get pipeline executions
  executions: (id: string, page = 1, perPage = 20): Promise<PaginatedResponse<PipelineExecution[]>> =>
    api.get(`/pipelines/${id}/executions?page=${page}&per_page=${perPage}`).then(res => res.data),

  // Validate pipeline YAML
  validate: (yamlContent: string): Promise<APIResponse<{ valid: boolean; errors: string[] }>> =>
    api.post('/pipelines/validate', { yaml_content: yamlContent }).then(res => res.data),
};

// Execution API
export const executionAPI = {
  // Get execution by ID
  get: (id: string): Promise<APIResponse<PipelineExecution>> =>
    api.get(`/executions/${id}`).then(res => res.data),

  // Cancel execution
  cancel: (id: string): Promise<APIResponse> =>
    api.post(`/executions/${id}/cancel`).then(res => res.data),

  // Get execution jobs
  jobs: (id: string): Promise<APIResponse<Job[]>> =>
    api.get(`/executions/${id}/jobs`).then(res => res.data),
};

// Job API
export const jobAPI = {
  // Get job by ID
  get: (id: string): Promise<APIResponse<Job>> =>
    api.get(`/jobs/${id}`).then(res => res.data),

  // Get job logs
  logs: (id: string): Promise<APIResponse<{ job_id: string; logs: string[] }>> =>
    api.get(`/jobs/${id}/logs`).then(res => res.data),

  // Cancel job
  cancel: (id: string): Promise<APIResponse> =>
    api.post(`/jobs/${id}/cancel`).then(res => res.data),

  // Retry job
  retry: (id: string): Promise<APIResponse> =>
    api.post(`/jobs/${id}/retry`).then(res => res.data),
};

// Trigger API
export const triggerAPI = {
  // Manual trigger
  manual: (data: {
    pipeline_id: string;
    user_id: string;
    reason?: string;
    variables?: Record<string, string>;
    branch?: string;
    commit_sha?: string;
    commit_message?: string;
  }): Promise<APIResponse<PipelineExecution>> =>
    api.post('/triggers/manual', data).then(res => res.data),
};

// Health check
export const healthAPI = {
  check: (): Promise<APIResponse> =>
    api.get('/health').then(res => res.data),
};

export default api;
