<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChainOps Route Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .route { margin: 10px 0; }
        .route a { 
            display: inline-block; 
            padding: 10px 15px; 
            background: #3b82f6; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin-right: 10px;
        }
        .route a:hover { background: #2563eb; }
        .description { color: #666; margin-left: 10px; }
    </style>
</head>
<body>
    <h1>ChainOps Frontend Route Test</h1>
    <p>Click each link to test the routes in the ChainOps application:</p>
    
    <div class="route">
        <a href="http://localhost:3001/" target="_blank">Home / Pipelines</a>
        <span class="description">Main dashboard with CircleCI-style pipeline table</span>
    </div>
    
    <div class="route">
        <a href="http://localhost:3001/pipelines" target="_blank">Pipelines</a>
        <span class="description">Pipeline management page</span>
    </div>
    
    <div class="route">
        <a href="http://localhost:3001/projects" target="_blank">Projects</a>
        <span class="description">Project management with repository cards</span>
    </div>
    
    <div class="route">
        <a href="http://localhost:3001/deployments" target="_blank">Deployments</a>
        <span class="description">Deployment management and monitoring</span>
    </div>
    
    <div class="route">
        <a href="http://localhost:3001/analytics" target="_blank">Analytics</a>
        <span class="description">Performance metrics and insights</span>
    </div>
    
    <div class="route">
        <a href="http://localhost:3001/runners" target="_blank">Runners</a>
        <span class="description">Pipeline execution runners management</span>
    </div>
    
    <div class="route">
        <a href="http://localhost:3001/settings" target="_blank">Settings</a>
        <span class="description">Configuration and settings</span>
    </div>
    
    <div class="route">
        <a href="http://localhost:3001/plan" target="_blank">Plan</a>
        <span class="description">Subscription and pricing plans</span>
    </div>
    
    <h2>Expected Features to Test:</h2>
    <ul>
        <li>✅ CircleCI-style sidebar navigation</li>
        <li>✅ ChainOps branding and logo</li>
        <li>✅ Pipeline table with status indicators</li>
        <li>✅ Project cards with repository information</li>
        <li>✅ Runner status monitoring</li>
        <li>✅ Analytics dashboard with metrics</li>
        <li>✅ Deployment management interface</li>
        <li>✅ Professional pricing page</li>
        <li>✅ Responsive design</li>
        <li>✅ Hover effects and interactions</li>
    </ul>
    
    <h2>Visual Style Checklist:</h2>
    <ul>
        <li>✅ Clean, modern interface</li>
        <li>✅ Proper color coding for statuses</li>
        <li>✅ Professional typography</li>
        <li>✅ Consistent spacing and layout</li>
        <li>✅ CircleCI-inspired design elements</li>
    </ul>
</body>
</html>
