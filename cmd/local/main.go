package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/chainops/chainops/internal/testing"
	"github.com/chainops/chainops/pkg/types"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"gopkg.in/yaml.v3"
)

var (
	configFile   string
	pipelineFile string
	workDir      string
	verbose      bool
	dryRun       bool
	outputFormat string
	watchMode    bool
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "chainops-local",
		Short: "ChainOps Local Pipeline Runner",
		Long: `ChainOps Local Pipeline Runner allows you to test your CI/CD pipelines locally
before pushing them to your repository. It supports Docker containers and
provides detailed execution logs.`,
		Run: runPipeline,
	}

	rootCmd.Flags().StringVarP(&configFile, "config", "c", "", "Local runner configuration file")
	rootCmd.Flags().StringVarP(&pipelineFile, "pipeline", "p", ".chainops.yml", "Pipeline configuration file")
	rootCmd.Flags().StringVarP(&workDir, "workdir", "w", "", "Working directory (default: current directory)")
	rootCmd.Flags().BoolVarP(&verbose, "verbose", "v", false, "Enable verbose logging")
	rootCmd.Flags().BoolVarP(&dryRun, "dry-run", "d", false, "Show what would be executed without running")
	rootCmd.Flags().StringVarP(&outputFormat, "output", "o", "text", "Output format (text, json, yaml)")
	rootCmd.Flags().BoolVarP(&watchMode, "watch", "", false, "Watch for file changes and re-run")

	// Add subcommands
	rootCmd.AddCommand(initCmd())
	rootCmd.AddCommand(validateCmd())
	rootCmd.AddCommand(listCmd())
	rootCmd.AddCommand(cleanCmd())

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func runPipeline(cmd *cobra.Command, args []string) {
	// Setup logger
	logger := logrus.New()
	if verbose {
		logger.SetLevel(logrus.DebugLevel)
	}

	// Load configuration
	config, err := loadConfig()
	if err != nil {
		logger.WithError(err).Fatal("Failed to load configuration")
	}

	// Set working directory
	if workDir != "" {
		config.WorkspaceDir = workDir
	} else if config.WorkspaceDir == "" {
		wd, _ := os.Getwd()
		config.WorkspaceDir = wd
	}

	// Load pipeline configuration
	pipelineConfig, err := loadPipelineConfig()
	if err != nil {
		logger.WithError(err).Fatal("Failed to load pipeline configuration")
	}

	if dryRun {
		showDryRun(pipelineConfig, config)
		return
	}

	// Create local runner
	runner, err := testing.NewLocalRunner(config, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to create local runner")
	}

	// Run pipeline
	ctx := context.Background()
	execution, err := runner.RunPipeline(ctx, pipelineConfig)
	if err != nil {
		logger.WithError(err).Fatal("Pipeline execution failed")
	}

	// Output results
	outputResults(execution, logger)

	// Exit with appropriate code
	if execution.Status == types.StatusFailure {
		os.Exit(1)
	}
}

func initCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "init",
		Short: "Initialize local runner configuration",
		Run: func(cmd *cobra.Command, args []string) {
			config := testing.GetDefaultConfig()

			configPath := ".chainops-local.yml"
			if configFile != "" {
				configPath = configFile
			}

			data, err := yaml.Marshal(config)
			if err != nil {
				fmt.Fprintf(os.Stderr, "Error marshaling config: %v\n", err)
				os.Exit(1)
			}

			if err := os.WriteFile(configPath, data, 0644); err != nil {
				fmt.Fprintf(os.Stderr, "Error writing config file: %v\n", err)
				os.Exit(1)
			}

			fmt.Printf("Configuration file created: %s\n", configPath)
			fmt.Println("Edit the file to customize your local runner settings.")
		},
	}

	return cmd
}

func validateCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "validate",
		Short: "Validate pipeline configuration",
		Run: func(cmd *cobra.Command, args []string) {
			pipelineConfig, err := loadPipelineConfig()
			if err != nil {
				fmt.Fprintf(os.Stderr, "Error loading pipeline: %v\n", err)
				os.Exit(1)
			}

			// Basic validation
			if pipelineConfig.Name == "" {
				fmt.Fprintf(os.Stderr, "Error: Pipeline name is required\n")
				os.Exit(1)
			}

			if len(pipelineConfig.Stages) == 0 {
				fmt.Fprintf(os.Stderr, "Error: Pipeline must have at least one stage\n")
				os.Exit(1)
			}

			for i, stage := range pipelineConfig.Stages {
				if stage.Name == "" {
					fmt.Fprintf(os.Stderr, "Error: Stage %d is missing a name\n", i+1)
					os.Exit(1)
				}

				if len(stage.Steps) == 0 {
					fmt.Fprintf(os.Stderr, "Error: Stage '%s' must have at least one step\n", stage.Name)
					os.Exit(1)
				}

				for j, step := range stage.Steps {
					if step.Name == "" {
						fmt.Fprintf(os.Stderr, "Error: Step %d in stage '%s' is missing a name\n", j+1, stage.Name)
						os.Exit(1)
					}

					if step.Run == "" && step.Uses == "" {
						fmt.Fprintf(os.Stderr, "Error: Step '%s' must have either 'run' or 'uses'\n", step.Name)
						os.Exit(1)
					}
				}
			}

			fmt.Println("✓ Pipeline configuration is valid")
		},
	}

	return cmd
}

func listCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list",
		Short: "List pipeline stages and steps",
		Run: func(cmd *cobra.Command, args []string) {
			pipelineConfig, err := loadPipelineConfig()
			if err != nil {
				fmt.Fprintf(os.Stderr, "Error loading pipeline: %v\n", err)
				os.Exit(1)
			}

			fmt.Printf("Pipeline: %s\n", pipelineConfig.Name)
			if pipelineConfig.Timeout != "" {
				fmt.Printf("Timeout: %s\n", pipelineConfig.Timeout)
			}
			fmt.Println()

			for i, stage := range pipelineConfig.Stages {
				fmt.Printf("%d. Stage: %s\n", i+1, stage.Name)
				for j, step := range stage.Steps {
					fmt.Printf("   %d.%d. %s", i+1, j+1, step.Name)
					if step.Uses != "" {
						fmt.Printf(" (uses: %s)", step.Uses)
					} else if step.Run != "" {
						fmt.Printf(" (run: %s)", truncateString(step.Run, 50))
					}
					fmt.Println()
				}
				fmt.Println()
			}
		},
	}

	return cmd
}

func cleanCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "clean",
		Short: "Clean up local runner workspace",
		Run: func(cmd *cobra.Command, args []string) {
			config, err := loadConfig()
			if err != nil {
				fmt.Fprintf(os.Stderr, "Error loading config: %v\n", err)
				os.Exit(1)
			}

			if err := os.RemoveAll(config.WorkspaceDir); err != nil {
				fmt.Fprintf(os.Stderr, "Error cleaning workspace: %v\n", err)
				os.Exit(1)
			}

			if err := os.RemoveAll(config.CacheDir); err != nil {
				fmt.Fprintf(os.Stderr, "Error cleaning cache: %v\n", err)
				os.Exit(1)
			}

			fmt.Println("✓ Workspace and cache cleaned")
		},
	}

	return cmd
}

func loadConfig() (*testing.LocalRunnerConfig, error) {
	config := testing.GetDefaultConfig()

	configPath := ".chainops-local.yml"
	if configFile != "" {
		configPath = configFile
	}

	if _, err := os.Stat(configPath); err == nil {
		data, err := os.ReadFile(configPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}

		if err := yaml.Unmarshal(data, config); err != nil {
			return nil, fmt.Errorf("failed to parse config file: %w", err)
		}
	}

	return config, nil
}

func loadPipelineConfig() (*types.PipelineConfig, error) {
	// Try to find pipeline file
	possibleFiles := []string{pipelineFile, ".chainops.yml", ".chainops.yaml", "chainops.yml", "chainops.yaml"}

	var pipelineFile string
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			pipelineFile = file
			break
		}
	}

	if pipelineFile == "" {
		return nil, fmt.Errorf("no pipeline configuration file found")
	}

	data, err := os.ReadFile(pipelineFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read pipeline file: %w", err)
	}

	var config types.PipelineConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse pipeline file: %w", err)
	}

	return &config, nil
}

func showDryRun(pipelineConfig *types.PipelineConfig, config *testing.LocalRunnerConfig) {
	fmt.Printf("🔍 Dry Run - Pipeline: %s\n", pipelineConfig.Name)
	fmt.Printf("📁 Workspace: %s\n", config.WorkspaceDir)
	fmt.Printf("🐳 Docker: %v\n", config.EnableDocker)
	fmt.Println()

	fmt.Println("📋 Execution Plan:")
	for i, stage := range pipelineConfig.Stages {
		fmt.Printf("  %d. Stage: %s\n", i+1, stage.Name)
		for j, step := range stage.Steps {
			fmt.Printf("     %d.%d. %s", i+1, j+1, step.Name)
			if step.Uses != "" {
				fmt.Printf(" → Plugin: %s", step.Uses)
			} else if step.Run != "" {
				fmt.Printf(" → Command: %s", truncateString(step.Run, 50))
			}
			fmt.Println()
		}
	}
}

func outputResults(execution *testing.LocalExecution, logger *logrus.Logger) {
	switch outputFormat {
	case "json":
		data, _ := json.MarshalIndent(execution, "", "  ")
		fmt.Println(string(data))
	case "yaml":
		data, _ := yaml.Marshal(execution)
		fmt.Println(string(data))
	default:
		outputTextResults(execution, logger)
	}
}

func outputTextResults(execution *testing.LocalExecution, logger *logrus.Logger) {
	// Status icon
	statusIcon := "✓"
	if execution.Status == types.StatusFailure {
		statusIcon = "✗"
	}

	fmt.Printf("\n%s Pipeline: %s\n", statusIcon, execution.PipelineID)
	fmt.Printf("📊 Status: %s\n", execution.Status)
	fmt.Printf("⏱️  Duration: %s\n", time.Since(execution.StartedAt).Round(time.Second))
	fmt.Printf("📁 Workspace: %s\n", execution.WorkDir)
	fmt.Println()

	// Job results
	fmt.Println("📋 Jobs:")
	for _, job := range execution.Jobs {
		jobIcon := "✓"
		if job.Status == types.StatusFailure {
			jobIcon = "✗"
		}

		fmt.Printf("  %s %s (%s)\n", jobIcon, job.Name, job.Duration.Round(time.Second))

		if verbose {
			for _, step := range job.Steps {
				stepIcon := "✓"
				if step.Status == types.StatusFailure {
					stepIcon = "✗"
				}
				fmt.Printf("    %s %s (%s)\n", stepIcon, step.Name, step.Duration.Round(time.Second))

				if step.Output != "" && len(step.Output) < 200 {
					fmt.Printf("      Output: %s\n", truncateString(step.Output, 100))
				}

				if step.Error != "" {
					fmt.Printf("      Error: %s\n", step.Error)
				}
			}
		}
	}

	if execution.Status == types.StatusFailure {
		fmt.Println("\n❌ Pipeline failed. Use --verbose for detailed logs.")
	} else {
		fmt.Println("\n🎉 Pipeline completed successfully!")
	}
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
