package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/chainops/chainops/internal/config"
	"github.com/chainops/chainops/internal/storage"
)

func main() {
	var (
		configFile = flag.String("config", "config/development.yaml", "Configuration file path")
		steps      = flag.Int("steps", 0, "Number of migration steps (0 for all)")
	)
	flag.Parse()

	// Set config file if provided
	if *configFile != "" {
		os.Setenv("CHAINOPS_CONFIG_FILE", *configFile)
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := storage.NewDatabase(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Get command from args
	args := flag.Args()
	if len(args) == 0 {
		printUsage()
		os.Exit(1)
	}

	command := args[0]

	switch command {
	case "up":
		if err := runMigrationsUp(db, *steps); err != nil {
			log.Fatalf("Migration up failed: %v", err)
		}
		fmt.Println("✅ Migrations completed successfully")

	case "down":
		if *steps == 0 {
			*steps = 1 // Default to 1 step down
		}
		if err := runMigrationsDown(db, *steps); err != nil {
			log.Fatalf("Migration down failed: %v", err)
		}
		fmt.Printf("✅ Rolled back %d migration(s) successfully\n", *steps)

	case "status":
		if err := showMigrationStatus(db); err != nil {
			log.Fatalf("Failed to show migration status: %v", err)
		}

	case "create":
		if len(args) < 2 {
			log.Fatal("Migration name is required")
		}
		migrationName := args[1]
		if err := createMigration(migrationName); err != nil {
			log.Fatalf("Failed to create migration: %v", err)
		}
		fmt.Printf("✅ Created migration: %s\n", migrationName)

	case "reset":
		if err := resetDatabase(db); err != nil {
			log.Fatalf("Failed to reset database: %v", err)
		}
		fmt.Println("✅ Database reset successfully")

	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func runMigrationsUp(db *storage.Database, steps int) error {
	fmt.Println("Running database migrations...")

	// For now, just run the auto-migration
	// In a production system, you'd implement proper migration files
	if err := db.Migrate(); err != nil {
		return fmt.Errorf("auto-migration failed: %w", err)
	}

	fmt.Println("Auto-migration completed")
	return nil
}

func runMigrationsDown(db *storage.Database, steps int) error {
	fmt.Printf("Rolling back %d migration(s)...\n", steps)

	// For now, just log that rollback would happen
	// In a production system, you'd implement proper rollback logic
	fmt.Printf("Rollback of %d steps would be performed here\n", steps)
	fmt.Println("Note: Auto-migration doesn't support rollback. Use proper migration files for production.")

	return nil
}

func showMigrationStatus(db *storage.Database) error {
	fmt.Println("Migration Status:")
	fmt.Println("================")

	// Check database health
	if err := db.Health(); err != nil {
		fmt.Printf("❌ Database connection: FAILED (%v)\n", err)
		return err
	}

	fmt.Println("✅ Database connection: OK")

	// For now, just show that auto-migration is used
	fmt.Println("📋 Migration system: Auto-migration (GORM)")
	fmt.Println("📝 Note: For production, implement proper migration files")

	return nil
}

func createMigration(name string) error {
	fmt.Printf("Creating migration: %s\n", name)

	// For now, just log that migration would be created
	// In a production system, you'd create actual migration files
	fmt.Println("Note: Auto-migration is used. For production, implement proper migration file creation.")

	return nil
}

func resetDatabase(db *storage.Database) error {
	fmt.Println("Resetting database...")

	// For now, just run migrations again
	// In a production system, you'd drop and recreate tables
	if err := db.Migrate(); err != nil {
		return fmt.Errorf("failed to reset database: %w", err)
	}

	fmt.Println("Note: Auto-migration doesn't drop tables. For production, implement proper reset logic.")

	return nil
}

func printUsage() {
	fmt.Println("ChainOps Database Migration Tool")
	fmt.Println("================================")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  migrate [flags] <command>")
	fmt.Println()
	fmt.Println("Commands:")
	fmt.Println("  up      Run pending migrations")
	fmt.Println("  down    Rollback migrations")
	fmt.Println("  status  Show migration status")
	fmt.Println("  create  Create a new migration")
	fmt.Println("  reset   Reset database")
	fmt.Println()
	fmt.Println("Flags:")
	fmt.Println("  -config string")
	fmt.Println("        Configuration file path (default \"config/development.yaml\")")
	fmt.Println("  -steps int")
	fmt.Println("        Number of migration steps (default 0 for all)")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  migrate up")
	fmt.Println("  migrate down -steps 1")
	fmt.Println("  migrate status")
	fmt.Println("  migrate create add_user_table")
	fmt.Println("  migrate reset")
}
