server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"
  tls:
    enabled: false
  cors:
    allow_origins: 
      - "https://staging.chainops.dev"
      - "http://localhost:3000"
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["*"]
    allow_credentials: true
    max_age: 86400

database:
  host: "postgres"
  port: 5432
  user: "chainops"
  password: "${CHAINOPS_DATABASE_PASSWORD}"
  name: "chainops_staging"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

redis:
  host: "redis"
  port: 6379
  password: "${CHAINOPS_REDIS_PASSWORD}"
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

jwt:
  secret: "${CHAINOPS_JWT_SECRET}"
  expiration: "24h"
  refresh_expiration: "168h"
  issuer: "chainops-staging"
  algorithm: "HS256"

logger:
  level: "info"
  format: "json"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true

security:
  rate_limiting:
    enabled: true
    requests: 100
    window: "1m"
    skip_paths:
      - "/health"
      - "/metrics"
    skip_ips: []
  csrf:
    enabled: true
    secret: "${CHAINOPS_CSRF_SECRET}"
    token_name: "csrf_token"
    header_name: "X-CSRF-Token"
  headers:
    content_type_nosniff: true
    frame_deny: true
    content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    referrer_policy: "strict-origin-when-cross-origin"

pipeline:
  max_concurrent_executions: 20
  default_timeout: "1h"
  max_timeout: "6h"
  workspace_dir: "/app/data/workspaces"
  artifacts_dir: "/app/data/artifacts"
  logs_dir: "/app/data/logs"

storage:
  type: "local"
  local:
    base_path: "/app/data/storage"
  # Uncomment for S3 storage
  # s3:
  #   region: "us-west-2"
  #   bucket: "chainops-staging-storage"
  #   access_key_id: "${AWS_ACCESS_KEY_ID}"
  #   secret_access_key: "${AWS_SECRET_ACCESS_KEY}"
  #   use_ssl: true

monitoring:
  enabled: true
  prometheus:
    enabled: true
    path: "/metrics"
  jaeger:
    enabled: false
    endpoint: "http://jaeger:14268/api/traces"
    service_name: "chainops-staging"

features:
  blue_green_deployment: true
  canary_deployment: true
  security_scanning: true
  multi_tenancy: false
  visual_editor: true
