server:
  host: "localhost"
  port: 8081
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  shutdown_timeout: "10s"
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["*"]
    allow_credentials: true

database:
  host: "localhost"
  port: 5432
  user: "chainops_test"
  password: "test_password"
  name: "chainops_test"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 2
  conn_max_lifetime: "5m"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 1
  pool_size: 5
  min_idle_conns: 2

jwt:
  secret: "test-jwt-secret-key-for-testing-only"
  expiration: "1h"
  refresh_expiration: "24h"
  issuer: "chainops-test"

logger:
  level: "debug"
  format: "json"
  output: "stdout"

security:
  rate_limiting:
    enabled: true
    requests: 1000
    window: "1m"
  csrf:
    enabled: false
  headers:
    content_type_nosniff: true
    frame_deny: true

pipeline:
  max_concurrent_executions: 5
  default_timeout: "30m"
  max_timeout: "2h"
  workspace_dir: "/tmp/chainops-test/workspaces"
  artifacts_dir: "/tmp/chainops-test/artifacts"
  logs_dir: "/tmp/chainops-test/logs"

storage:
  type: "local"
  local:
    base_path: "/tmp/chainops-test/storage"

monitoring:
  enabled: true
  prometheus:
    enabled: true
    path: "/metrics"
  jaeger:
    enabled: false

features:
  blue_green_deployment: true
  canary_deployment: true
  security_scanning: true
  multi_tenancy: false
  visual_editor: true
