package rest

import (
	"net/http"
	"strconv"
	"time"

	"github.com/chainops/chainops/internal/core/logging"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// WebSocketHandler handles WebSocket connections for real-time features
type WebSocketHandler struct {
	streamManager *logging.StreamManager
	logger        *logrus.Logger
	upgrader      websocket.Upgrader
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(streamManager *logging.StreamManager, logger *logrus.Logger) *WebSocketHandler {
	return &WebSocketHandler{
		streamManager: streamManager,
		logger:        logger,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// In production, implement proper origin checking
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}
}

// RegisterRoutes registers WebSocket routes
func (h *WebSocketHandler) RegisterRoutes(router *gin.RouterGroup) {
	ws := router.Group("/ws")
	{
		ws.GET("/logs/:stream_id", h.handleLogStream)
		ws.GET("/pipeline/:execution_id", h.handlePipelineStream)
		ws.GET("/job/:execution_id/:job_id", h.handleJobStream)
		ws.GET("/step/:execution_id/:job_id/:step_id", h.handleStepStream)
	}
}

// handleLogStream handles WebSocket connections for log streaming
func (h *WebSocketHandler) handleLogStream(c *gin.Context) {
	streamID := c.Param("stream_id")
	
	// Upgrade HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upgrade WebSocket connection")
		return
	}
	defer conn.Close()
	
	// Check if stream exists
	stream, exists := h.streamManager.GetStream(streamID)
	if !exists {
		h.logger.WithField("stream_id", streamID).Error("Stream not found")
		conn.WriteJSON(map[string]string{"error": "Stream not found"})
		return
	}
	
	h.logger.WithFields(logrus.Fields{
		"stream_id":    streamID,
		"execution_id": stream.ExecutionID,
		"remote_addr":  c.Request.RemoteAddr,
	}).Info("WebSocket connection established for log stream")
	
	// Add connection to stream manager
	if err := h.streamManager.AddConnection(streamID, conn); err != nil {
		h.logger.WithError(err).Error("Failed to add WebSocket connection to stream")
		return
	}
	defer h.streamManager.RemoveConnection(streamID, conn)
	
	// Handle WebSocket messages (for client commands like filtering, etc.)
	h.handleWebSocketMessages(conn, streamID)
}

// handlePipelineStream handles WebSocket connections for pipeline-level streaming
func (h *WebSocketHandler) handlePipelineStream(c *gin.Context) {
	executionID := c.Param("execution_id")
	streamID := executionID
	
	h.handleStreamConnection(c, streamID, "pipeline", executionID)
}

// handleJobStream handles WebSocket connections for job-level streaming
func (h *WebSocketHandler) handleJobStream(c *gin.Context) {
	executionID := c.Param("execution_id")
	jobID := c.Param("job_id")
	streamID := executionID + "-" + jobID
	
	h.handleStreamConnection(c, streamID, "job", executionID)
}

// handleStepStream handles WebSocket connections for step-level streaming
func (h *WebSocketHandler) handleStepStream(c *gin.Context) {
	executionID := c.Param("execution_id")
	jobID := c.Param("job_id")
	stepID := c.Param("step_id")
	streamID := executionID + "-" + jobID + "-" + stepID
	
	h.handleStreamConnection(c, streamID, "step", executionID)
}

// handleStreamConnection handles a generic stream connection
func (h *WebSocketHandler) handleStreamConnection(c *gin.Context, streamID, streamType, executionID string) {
	// Upgrade HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upgrade WebSocket connection")
		return
	}
	defer conn.Close()
	
	// Check if stream exists, create if it doesn't
	stream, exists := h.streamManager.GetStream(streamID)
	if !exists {
		// Create stream if it doesn't exist
		jobID := c.Param("job_id")
		stepID := c.Param("step_id")
		stream = h.streamManager.CreateStream(executionID, jobID, stepID)
	}
	
	h.logger.WithFields(logrus.Fields{
		"stream_id":    streamID,
		"stream_type":  streamType,
		"execution_id": executionID,
		"remote_addr":  c.Request.RemoteAddr,
	}).Info("WebSocket connection established for stream")
	
	// Add connection to stream manager
	if err := h.streamManager.AddConnection(streamID, conn); err != nil {
		h.logger.WithError(err).Error("Failed to add WebSocket connection to stream")
		return
	}
	defer h.streamManager.RemoveConnection(streamID, conn)
	
	// Handle WebSocket messages
	h.handleWebSocketMessages(conn, streamID)
}

// handleWebSocketMessages handles incoming WebSocket messages
func (h *WebSocketHandler) handleWebSocketMessages(conn *websocket.Conn, streamID string) {
	// Set read deadline
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	
	// Set pong handler to handle ping/pong for connection keep-alive
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})
	
	// Start ping ticker
	ticker := time.NewTicker(54 * time.Second)
	defer ticker.Stop()
	
	// Handle messages in a goroutine
	go func() {
		for {
			select {
			case <-ticker.C:
				conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
				if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
					return
				}
			}
		}
	}()
	
	for {
		var message map[string]interface{}
		err := conn.ReadJSON(&message)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				h.logger.WithError(err).Error("WebSocket error")
			}
			break
		}
		
		// Handle different message types
		if msgType, ok := message["type"].(string); ok {
			switch msgType {
			case "get_logs":
				h.handleGetLogsMessage(conn, streamID, message)
			case "filter":
				h.handleFilterMessage(conn, streamID, message)
			case "subscribe":
				h.handleSubscribeMessage(conn, streamID, message)
			default:
				h.logger.WithField("message_type", msgType).Warn("Unknown WebSocket message type")
			}
		}
	}
}

// handleGetLogsMessage handles requests for historical logs
func (h *WebSocketHandler) handleGetLogsMessage(conn *websocket.Conn, streamID string, message map[string]interface{}) {
	limit := 100
	offset := 0
	
	if l, ok := message["limit"].(float64); ok {
		limit = int(l)
	}
	if o, ok := message["offset"].(float64); ok {
		offset = int(o)
	}
	
	logs, err := h.streamManager.GetLogs(streamID, limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get logs")
		conn.WriteJSON(map[string]string{"error": "Failed to get logs"})
		return
	}
	
	response := map[string]interface{}{
		"type": "logs_response",
		"logs": logs,
	}
	
	conn.WriteJSON(response)
}

// handleFilterMessage handles log filtering requests
func (h *WebSocketHandler) handleFilterMessage(conn *websocket.Conn, streamID string, message map[string]interface{}) {
	// TODO: Implement log filtering
	// This would allow clients to filter logs by level, source, etc.
	h.logger.Info("Log filtering not implemented yet")
}

// handleSubscribeMessage handles subscription requests
func (h *WebSocketHandler) handleSubscribeMessage(conn *websocket.Conn, streamID string, message map[string]interface{}) {
	// TODO: Implement selective subscription
	// This would allow clients to subscribe to specific log levels or sources
	h.logger.Info("Selective subscription not implemented yet")
}

// REST endpoints for log management

// getLogStreams gets all log streams
func (h *WebSocketHandler) getLogStreams(c *gin.Context) {
	streams := h.streamManager.ListStreams()
	c.JSON(http.StatusOK, gin.H{"streams": streams})
}

// getLogStream gets a specific log stream
func (h *WebSocketHandler) getLogStream(c *gin.Context) {
	streamID := c.Param("stream_id")
	
	stream, exists := h.streamManager.GetStream(streamID)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Stream not found"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"stream": stream})
}

// getStreamLogs gets logs for a stream
func (h *WebSocketHandler) getStreamLogs(c *gin.Context) {
	streamID := c.Param("stream_id")
	
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "100")
	offsetStr := c.DefaultQuery("offset", "0")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}
	
	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}
	
	logs, err := h.streamManager.GetLogs(streamID, limit, offset)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"logs": logs})
}

// RegisterLogRoutes registers REST routes for log management
func (h *WebSocketHandler) RegisterLogRoutes(router *gin.RouterGroup) {
	logs := router.Group("/logs")
	{
		logs.GET("/streams", h.getLogStreams)
		logs.GET("/streams/:stream_id", h.getLogStream)
		logs.GET("/streams/:stream_id/logs", h.getStreamLogs)
	}
}
