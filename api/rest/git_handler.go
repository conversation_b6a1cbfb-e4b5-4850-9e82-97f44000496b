package rest

import (
	"net/http"
	"strconv"

	"github.com/chainops/chainops/internal/core/git"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// GitHandler handles Git provider operations
type GitHandler struct {
	gitManager *git.Manager
	logger     *logrus.Logger
}

// NewGitHandler creates a new Git handler
func NewGitHandler(gitManager *git.Manager, logger *logrus.Logger) *GitHandler {
	return &GitHandler{
		gitManager: gitManager,
		logger:     logger,
	}
}

// RegisterRoutes registers Git-related routes
func (h *GitHandler) RegisterRoutes(router *gin.RouterGroup) {
	gitGroup := router.Group("/git")
	{
		// Provider management
		gitGroup.POST("/providers", h.registerProvider)
		gitGroup.GET("/providers", h.listProviders)
		gitGroup.GET("/providers/:type", h.getProvider)
		gitGroup.POST("/providers/:type/auth", h.authenticateProvider)
		
		// Repository operations
		gitGroup.GET("/repositories", h.listRepositories)
		gitGroup.GET("/repositories/:owner/:repo", h.getRepository)
		gitGroup.GET("/repositories/:owner/:repo/branches", h.listBranches)
		gitGroup.GET("/repositories/:owner/:repo/branches/:branch", h.getBranch)
		gitGroup.GET("/repositories/:owner/:repo/commits/:sha", h.getCommit)
		gitGroup.GET("/repositories/:owner/:repo/contents/*path", h.getFileContent)
		
		// Pull/Merge requests
		gitGroup.GET("/repositories/:owner/:repo/pulls", h.listPullRequests)
		gitGroup.GET("/repositories/:owner/:repo/pulls/:number", h.getPullRequest)
		gitGroup.POST("/repositories/:owner/:repo/pulls/:number/status", h.updatePullRequestStatus)
		
		// Webhooks
		gitGroup.POST("/repositories/:owner/:repo/webhooks", h.createWebhook)
		gitGroup.GET("/repositories/:owner/:repo/webhooks", h.listWebhooks)
		gitGroup.DELETE("/repositories/:owner/:repo/webhooks/:webhook_id", h.deleteWebhook)
		gitGroup.POST("/webhooks/:provider", h.handleWebhook)
		
		// Releases
		gitGroup.GET("/repositories/:owner/:repo/releases", h.listReleases)
		gitGroup.GET("/repositories/:owner/:repo/releases/:tag", h.getRelease)
		gitGroup.POST("/repositories/:owner/:repo/releases", h.createRelease)
	}
}

// registerProvider registers a new Git provider
func (h *GitHandler) registerProvider(c *gin.Context) {
	var req struct {
		Type         git.ProviderType `json:"type" binding:"required"`
		BaseURL      string           `json:"base_url,omitempty"`
		ClientID     string           `json:"client_id,omitempty"`
		ClientSecret string           `json:"client_secret,omitempty"`
		WebhookSecret string          `json:"webhook_secret,omitempty"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	config := &git.ProviderConfig{
		Type:         req.Type,
		BaseURL:      req.BaseURL,
		ClientID:     req.ClientID,
		ClientSecret: req.ClientSecret,
		WebhookSecret: req.WebhookSecret,
	}
	
	if err := h.gitManager.RegisterProvider(req.Type, config); err != nil {
		h.logger.WithError(err).Error("Failed to register Git provider")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to register provider"})
		return
	}
	
	h.logger.WithField("provider_type", req.Type).Info("Git provider registered successfully")
	c.JSON(http.StatusCreated, gin.H{"message": "Provider registered successfully"})
}

// listProviders lists all registered Git providers
func (h *GitHandler) listProviders(c *gin.Context) {
	providers := h.gitManager.ListProviders()
	c.JSON(http.StatusOK, gin.H{"providers": providers})
}

// getProvider gets information about a specific provider
func (h *GitHandler) getProvider(c *gin.Context) {
	providerType := git.ProviderType(c.Param("type"))
	
	config, err := h.gitManager.GetProviderConfig(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	// Don't expose sensitive information
	safeConfig := map[string]interface{}{
		"type":     config.Type,
		"base_url": config.BaseURL,
	}
	
	c.JSON(http.StatusOK, gin.H{"provider": safeConfig})
}

// authenticateProvider authenticates with a Git provider
func (h *GitHandler) authenticateProvider(c *gin.Context) {
	providerType := git.ProviderType(c.Param("type"))
	
	var req struct {
		Token string `json:"token" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	if err := h.gitManager.AuthenticateProvider(c.Request.Context(), providerType, req.Token); err != nil {
		h.logger.WithError(err).Error("Failed to authenticate with Git provider")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication failed"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Authentication successful"})
}

// listRepositories lists repositories for a provider
func (h *GitHandler) listRepositories(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "30"))
	sort := c.DefaultQuery("sort", "updated")
	order := c.DefaultQuery("order", "desc")
	
	opts := &git.ListOptions{
		Page:    page,
		PerPage: perPage,
		Sort:    sort,
		Order:   order,
	}
	
	repositories, err := provider.ListRepositories(c.Request.Context(), opts)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list repositories")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list repositories"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"repositories": repositories})
}

// getRepository gets a specific repository
func (h *GitHandler) getRepository(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	repository, err := provider.GetRepository(c.Request.Context(), owner, repo)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get repository")
		c.JSON(http.StatusNotFound, gin.H{"error": "Repository not found"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"repository": repository})
}

// listBranches lists branches for a repository
func (h *GitHandler) listBranches(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	branches, err := provider.ListBranches(c.Request.Context(), owner, repo)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list branches")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list branches"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"branches": branches})
}

// getBranch gets a specific branch
func (h *GitHandler) getBranch(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	branch := c.Param("branch")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	branchInfo, err := provider.GetBranch(c.Request.Context(), owner, repo, branch)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get branch")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"branch": branchInfo})
}

// getCommit gets a specific commit
func (h *GitHandler) getCommit(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	sha := c.Param("sha")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	commit, err := provider.GetCommit(c.Request.Context(), owner, repo, sha)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get commit")
		c.JSON(http.StatusNotFound, gin.H{"error": "Commit not found"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"commit": commit})
}

// getFileContent gets file content from a repository
func (h *GitHandler) getFileContent(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	path := c.Param("path")
	ref := c.DefaultQuery("ref", "main")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	content, err := provider.GetFileContent(c.Request.Context(), owner, repo, path, ref)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get file content")
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"content": string(content)})
}

// listPullRequests lists pull requests for a repository
func (h *GitHandler) listPullRequests(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "30"))
	state := c.DefaultQuery("state", "open")
	sort := c.DefaultQuery("sort", "created")
	order := c.DefaultQuery("order", "desc")
	
	opts := &git.ListOptions{
		Page:    page,
		PerPage: perPage,
		State:   state,
		Sort:    sort,
		Order:   order,
	}
	
	pullRequests, err := provider.ListPullRequests(c.Request.Context(), owner, repo, opts)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list pull requests")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list pull requests"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"pull_requests": pullRequests})
}

// getPullRequest gets a specific pull request
func (h *GitHandler) getPullRequest(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	number, err := strconv.Atoi(c.Param("number"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid pull request number"})
		return
	}
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	pullRequest, err := provider.GetPullRequest(c.Request.Context(), owner, repo, number)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get pull request")
		c.JSON(http.StatusNotFound, gin.H{"error": "Pull request not found"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"pull_request": pullRequest})
}

// updatePullRequestStatus updates the status of a pull request
func (h *GitHandler) updatePullRequestStatus(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	number, err := strconv.Atoi(c.Param("number"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid pull request number"})
		return
	}
	
	var req struct {
		State       string `json:"state" binding:"required"`
		TargetURL   string `json:"target_url,omitempty"`
		Description string `json:"description,omitempty"`
		Context     string `json:"context,omitempty"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	status := &git.Status{
		State:       req.State,
		TargetURL:   req.TargetURL,
		Description: req.Description,
		Context:     req.Context,
	}
	
	if err := provider.UpdatePullRequestStatus(c.Request.Context(), owner, repo, number, status); err != nil {
		h.logger.WithError(err).Error("Failed to update pull request status")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update status"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Status updated successfully"})
}

// createWebhook creates a webhook for a repository
func (h *GitHandler) createWebhook(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	
	var req struct {
		URL         string   `json:"url" binding:"required"`
		ContentType string   `json:"content_type"`
		Secret      string   `json:"secret"`
		Events      []string `json:"events" binding:"required"`
		Active      bool     `json:"active"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	webhookConfig := &git.WebhookConfig{
		URL:         req.URL,
		ContentType: req.ContentType,
		Secret:      req.Secret,
		Events:      req.Events,
		Active:      req.Active,
	}
	
	webhook, err := provider.CreateWebhook(c.Request.Context(), owner, repo, webhookConfig)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create webhook")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create webhook"})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{"webhook": webhook})
}

// listWebhooks lists webhooks for a repository
func (h *GitHandler) listWebhooks(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	webhooks, err := provider.ListWebhooks(c.Request.Context(), owner, repo)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list webhooks")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list webhooks"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"webhooks": webhooks})
}

// deleteWebhook deletes a webhook
func (h *GitHandler) deleteWebhook(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	webhookID := c.Param("webhook_id")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	if err := provider.DeleteWebhook(c.Request.Context(), owner, repo, webhookID); err != nil {
		h.logger.WithError(err).Error("Failed to delete webhook")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete webhook"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Webhook deleted successfully"})
}

// handleWebhook handles incoming webhooks
func (h *GitHandler) handleWebhook(c *gin.Context) {
	providerType := git.ProviderType(c.Param("provider"))
	
	// Read the payload
	payload, err := c.GetRawData()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read payload"})
		return
	}
	
	// Get signature from headers (implementation depends on provider)
	signature := c.GetHeader("X-Hub-Signature-256") // GitHub
	if signature == "" {
		signature = c.GetHeader("X-GitLab-Token") // GitLab
	}
	
	// Create webhook handler
	webhookHandler := git.NewWebhookHandler(h.gitManager)
	
	// Parse the webhook event
	event, err := webhookHandler.HandleWebhook(c.Request.Context(), providerType, payload, signature)
	if err != nil {
		h.logger.WithError(err).Error("Failed to handle webhook")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to process webhook"})
		return
	}
	
	h.logger.WithFields(logrus.Fields{
		"provider": providerType,
		"event_type": event.Type,
		"repository": event.Repository.FullName,
	}).Info("Webhook processed successfully")
	
	// TODO: Trigger pipeline execution based on webhook event
	
	c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
}

// listReleases lists releases for a repository
func (h *GitHandler) listReleases(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	releases, err := provider.ListReleases(c.Request.Context(), owner, repo)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list releases")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list releases"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"releases": releases})
}

// getRelease gets a specific release
func (h *GitHandler) getRelease(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	tag := c.Param("tag")
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	release, err := provider.GetRelease(c.Request.Context(), owner, repo, tag)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get release")
		c.JSON(http.StatusNotFound, gin.H{"error": "Release not found"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"release": release})
}

// createRelease creates a new release
func (h *GitHandler) createRelease(c *gin.Context) {
	providerType := git.ProviderType(c.Query("provider"))
	owner := c.Param("owner")
	repo := c.Param("repo")
	
	var req struct {
		TagName    string `json:"tag_name" binding:"required"`
		Name       string `json:"name"`
		Body       string `json:"body"`
		Draft      bool   `json:"draft"`
		Prerelease bool   `json:"prerelease"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	if providerType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Provider type is required"})
		return
	}
	
	provider, err := h.gitManager.GetProvider(providerType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		return
	}
	
	release := &git.Release{
		TagName:    req.TagName,
		Name:       req.Name,
		Body:       req.Body,
		Draft:      req.Draft,
		Prerelease: req.Prerelease,
	}
	
	createdRelease, err := provider.CreateRelease(c.Request.Context(), owner, repo, release)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create release")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create release"})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{"release": createdRelease})
}
