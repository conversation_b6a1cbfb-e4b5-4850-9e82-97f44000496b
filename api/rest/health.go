package rest

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	logger *logrus.Logger
	db     DatabaseHealthChecker
	redis  RedisHealthChecker
}

// DatabaseHealthChecker interface for database health checks
type DatabaseHealthChecker interface {
	Health() error
}

// RedisHealthChecker interface for Redis health checks
type RedisHealthChecker interface {
	Ping() error
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(logger *logrus.Logger, db DatabaseHealthChecker, redis RedisHealthChecker) *HealthHandler {
	return &HealthHandler{
		logger: logger,
		db:     db,
		redis:  redis,
	}
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Version   string                 `json:"version"`
	Services  map[string]ServiceInfo `json:"services"`
	Uptime    string                 `json:"uptime"`
}

// ServiceInfo represents individual service health
type ServiceInfo struct {
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
}

var startTime = time.Now()

// Health returns the overall health status
func (h *HealthHandler) Health(c *gin.Context) {
	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now(),
		Version:   "0.1.0", // TODO: Get from build info
		Services:  make(map[string]ServiceInfo),
		Uptime:    time.Since(startTime).String(),
	}

	// Check database health
	if h.db != nil {
		if err := h.db.Health(); err != nil {
			response.Services["database"] = ServiceInfo{
				Status:  "unhealthy",
				Message: err.Error(),
			}
			response.Status = "degraded"
		} else {
			response.Services["database"] = ServiceInfo{
				Status: "healthy",
			}
		}
	}

	// Check Redis health
	if h.redis != nil {
		if err := h.redis.Ping(); err != nil {
			response.Services["redis"] = ServiceInfo{
				Status:  "unhealthy",
				Message: err.Error(),
			}
			if response.Status == "healthy" {
				response.Status = "degraded"
			}
		} else {
			response.Services["redis"] = ServiceInfo{
				Status: "healthy",
			}
		}
	}

	// Set HTTP status based on overall health
	statusCode := http.StatusOK
	if response.Status == "degraded" {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, response)
}

// Liveness returns a simple liveness probe
func (h *HealthHandler) Liveness(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "alive",
		"timestamp": time.Now(),
	})
}

// Readiness returns readiness probe with dependency checks
func (h *HealthHandler) Readiness(c *gin.Context) {
	ready := true
	services := make(map[string]ServiceInfo)

	// Check database readiness
	if h.db != nil {
		if err := h.db.Health(); err != nil {
			services["database"] = ServiceInfo{
				Status:  "not_ready",
				Message: err.Error(),
			}
			ready = false
		} else {
			services["database"] = ServiceInfo{
				Status: "ready",
			}
		}
	}

	// Check Redis readiness
	if h.redis != nil {
		if err := h.redis.Ping(); err != nil {
			services["redis"] = ServiceInfo{
				Status:  "not_ready",
				Message: err.Error(),
			}
			ready = false
		} else {
			services["redis"] = ServiceInfo{
				Status: "ready",
			}
		}
	}

	status := "ready"
	statusCode := http.StatusOK
	if !ready {
		status = "not_ready"
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, gin.H{
		"status":    status,
		"timestamp": time.Now(),
		"services":  services,
	})
}

// Metrics returns basic application metrics
func (h *HealthHandler) Metrics(c *gin.Context) {
	// This is a simple metrics endpoint
	// In production, you'd use Prometheus metrics
	c.JSON(http.StatusOK, gin.H{
		"uptime":    time.Since(startTime).String(),
		"timestamp": time.Now(),
		"version":   "0.1.0",
		"go_version": "1.21",
	})
}

// RegisterRoutes registers health check routes
func (h *HealthHandler) RegisterRoutes(router *gin.RouterGroup) {
	health := router.Group("/health")
	{
		health.GET("", h.Health)
		health.GET("/live", h.Liveness)
		health.GET("/ready", h.Readiness)
	}

	// Metrics endpoint
	router.GET("/metrics", h.Metrics)
}
