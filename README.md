# ChainOps - Enterprise CI/CD Platform

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Go Version](https://img.shields.io/badge/Go-1.21+-00ADD8?logo=go)](https://golang.org/)

[![Docker](https://img.shields.io/badge/Docker-24+-2496ED?logo=docker)](https://docker.com/)
[![Kubernetes](https://img.shields.io/badge/Kubernetes-1.28+-326CE5?logo=kubernetes)](https://kubernetes.io/)

ChainOps is a modern, open-source CI/CD automation platform that combines the best features from GitLab CI/CD, GitHub Actions, and Spinnaker. Built with cloud-native principles, it provides enterprise-grade pipeline automation with advanced deployment strategies, GitOps workflows, and comprehensive security features.

## 🚀 Key Features

- **YAML-Driven Pipelines**: Declarative pipeline configuration
- **Cloud-Native Architecture**: Kubernetes-native with horizontal scaling
- **Advanced Deployment Strategies**: Canary, blue-green, rolling updates, A/B testing
- **GitOps Integration**: Native ArgoCD compatibility and Git-based workflows
- **Enterprise Security**: RBAC, secret management, audit logging, compliance
- **Plugin Ecosystem**: Extensible architecture with marketplace
- **Multi-Cloud Support**: Deploy to any cloud provider or on-premises
- **Real-time Monitoring**: Comprehensive observability and analytics

## 🏗️ Architecture

ChainOps follows a microservices architecture with the following core components:

- **Pipeline Engine**: YAML parsing and execution orchestration
- **Runner System**: Containerized job execution (Docker/Kubernetes)
- **Trigger System**: Git webhooks, scheduled, and manual triggers
- **Workflow Orchestrator**: Dependency management and state transitions
- **Deployment Engine**: Multi-strategy deployment automation
- **Plugin System**: Extensible step execution framework

- **API Gateway**: REST and gRPC APIs with authentication

## 🛠️ Technology Stack

### Backend
- **Language**: Go 1.21+ (performance, concurrency, K8s ecosystem)
- **Database**: PostgreSQL 15+ with connection pooling
- **Message Queue**: NATS JetStream (cloud-native messaging)
- **Cache**: Redis 7+ for session management
- **Storage**: MinIO (S3-compatible) for artifacts

### Infrastructure
- **Container Runtime**: Docker + containerd
- **Orchestration**: Kubernetes 1.28+ with CRDs
- **Secrets**: HashiCorp Vault integration
- **Monitoring**: Prometheus + Grafana + Loki
- **Tracing**: Jaeger/OpenTelemetry

## 🚀 Quick Start

### Prerequisites

- Go 1.21+
- Docker 24+
- Docker Compose
- Make

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/chainops/chainops.git
   cd chainops
   ```

2. **Install dependencies**
   ```bash
   make install-deps
   ```

3. **Start development infrastructure**
   ```bash
   make dev-up
   ```

4. **Run database migrations**
   ```bash
   make migrate-up
   ```

5. **Start development server**
   ```bash
   # Backend server
   make dev-server
   ```

6. **Access the application**
   - API: http://localhost:8080
   - Grafana: http://localhost:3001 (admin/admin)
   - MinIO: http://localhost:9000 (minioadmin/minioadmin)

### Example Pipeline

Create a `.chainops.yml` file in your repository:

```yaml
pipeline:
  name: build-and-deploy
  
  triggers:
    - on: [push, pull_request]
      branches: [main, develop]
  
  variables:
    NODE_VERSION: "18"
    DOCKER_REGISTRY: "registry.example.com"
  
  stages:
    - name: build
      steps:
        - name: setup
          uses: setup-node@v1
          with:
            version: ${{ variables.NODE_VERSION }}
        
        - name: install
          run: npm ci
        
        - name: build
          run: npm run build
        
        - name: test
          run: npm test
          
    - name: docker
      condition: branch == "main"
      steps:
        - name: build-image
          uses: docker-build@v1
          with:
            context: .
            dockerfile: Dockerfile
            tags: |
              ${{ variables.DOCKER_REGISTRY }}/myapp:latest
              ${{ variables.DOCKER_REGISTRY }}/myapp:${{ git.sha }}
            push: true
    
    - name: deploy
      condition: branch == "main"
      environment: production
      steps:
        - name: deploy-k8s
          uses: kubernetes-deploy@v1
          with:
            manifests: k8s/
            namespace: production
            strategy: canary
            canary_percentage: 10
```

## 📚 Documentation

- [Technical Blueprint](./TECHNICAL_BLUEPRINT.md) - Comprehensive implementation plan
- [API Documentation](./docs/api/) - REST and gRPC API reference
- [Plugin Development](./docs/plugins/) - Creating custom plugins
- [Deployment Guide](./docs/deployment/) - Production deployment
- [Contributing](./CONTRIBUTING.md) - How to contribute

## 🧪 Testing

```bash
# Run all tests
make test

# Run specific test suites
make test-go          # Go unit tests
make test-ui          # Frontend tests
make test-integration # Integration tests
make test-e2e         # End-to-end tests

# Generate coverage report
make test-coverage
```

## 🔧 Development Commands

```bash
# Infrastructure management
make dev-up           # Start development infrastructure
make dev-down         # Stop development infrastructure
make dev-clean        # Clean infrastructure (removes volumes)

# Database management
make migrate-up       # Run migrations
make migrate-down     # Rollback migrations
make migrate-create NAME=migration_name  # Create new migration

# Code quality
make lint             # Run linters
make fmt              # Format code
make security-scan    # Run security scans

# Building
make build            # Build application
make build-docker     # Build Docker image
make release          # Build release binaries

# Kubernetes/Helm
make k8s-deploy       # Deploy to local Kubernetes
make helm-install     # Install with Helm
```

## 🌐 Community & Support

- **GitHub**: [Issues](https://github.com/chainops/chainops/issues) and [Discussions](https://github.com/chainops/chainops/discussions)
- **Discord**: [Join our community](https://discord.gg/chainops)
- **Documentation**: [docs.chainops.dev](https://docs.chainops.dev)
- **Blog**: [blog.chainops.dev](https://blog.chainops.dev)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

### Ways to Contribute

- 🐛 **Bug Reports**: Report issues and bugs
- 💡 **Feature Requests**: Suggest new features
- 📝 **Documentation**: Improve docs and tutorials
- 🔧 **Code**: Submit pull requests
- 🎨 **Design**: UI/UX improvements
- 🧪 **Testing**: Add test cases and scenarios

## 📄 License

ChainOps is licensed under the [Apache License 2.0](./LICENSE).

## 🎯 Roadmap

### Phase 1: Foundation (Q1 2024)
- [x] Core pipeline engine
- [x] Basic runner system
- [x] API foundation
- [ ] Docker-based execution
- [ ] Git webhook triggers

### Phase 2: Core Features (Q2 2024)
- [ ] Kubernetes runner
- [ ] Advanced pipeline features
- [ ] Plugin system
- [ ] Artifact management
- [ ] Basic deployment strategies

### Phase 3: Enterprise Features (Q3 2024)
- [ ] RBAC and security
- [ ] GitOps integration
- [ ] Advanced deployments
- [ ] Monitoring and observability
- [ ] Multi-tenancy

### Phase 4: Ecosystem (Q4 2024)
- [ ] Plugin marketplace
- [ ] Enterprise integrations
- [ ] Advanced analytics
- [ ] Certification program
- [ ] Community growth

## 📊 Status

- **Development Status**: Alpha
- **API Stability**: Experimental
- **Production Ready**: No (use at your own risk)

---

**Built with ❤️ by the ChainOps community**
