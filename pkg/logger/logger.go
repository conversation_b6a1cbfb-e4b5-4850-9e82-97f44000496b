package logger

import (
	"context"
	"io"
	"os"
	"time"

	"github.com/sirupsen/logrus"
)

// Lo<PERSON> wraps logrus.Logger with additional functionality
type Logger struct {
	*logrus.Logger
}

// Config holds logger configuration
type Config struct {
	Level      string `yaml:"level" env:"LOG_LEVEL" default:"info"`
	Format     string `yaml:"format" env:"LOG_FORMAT" default:"json"`
	Output     string `yaml:"output" env:"LOG_OUTPUT" default:"stdout"`
	File       string `yaml:"file" env:"LOG_FILE"`
	MaxSize    int    `yaml:"max_size" env:"LOG_MAX_SIZE" default:"100"`
	MaxBackups int    `yaml:"max_backups" env:"LOG_MAX_BACKUPS" default:"3"`
	MaxAge     int    `yaml:"max_age" env:"LOG_MAX_AGE" default:"28"`
	Compress   bool   `yaml:"compress" env:"LOG_COMPRESS" default:"true"`
}

// Context<PERSON>ey represents keys for context values
type ContextKey string

const (
	// Context keys for logging
	ContextKeyRequestID   ContextKey = "request_id"
	ContextKeyUserID      ContextKey = "user_id"
	ContextKeyPipelineID  ContextKey = "pipeline_id"
	ContextKeyExecutionID ContextKey = "execution_id"
	ContextKeyJobID       ContextKey = "job_id"
	ContextKeyOperation   ContextKey = "operation"
	ContextKeyComponent   ContextKey = "component"
)

// New creates a new logger instance
func New(config *Config) (*Logger, error) {
	logger := logrus.New()

	// Set log level
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		return nil, err
	}
	logger.SetLevel(level)

	// Set formatter
	switch config.Format {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "function",
			},
		})
	case "text":
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: time.RFC3339,
			FullTimestamp:   true,
		})
	default:
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	}

	// Set output
	switch config.Output {
	case "stdout":
		logger.SetOutput(os.Stdout)
	case "stderr":
		logger.SetOutput(os.Stderr)
	case "file":
		if config.File == "" {
			config.File = "chainops.log"
		}
		file, err := os.OpenFile(config.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, err
		}
		logger.SetOutput(file)
	case "both":
		if config.File == "" {
			config.File = "chainops.log"
		}
		file, err := os.OpenFile(config.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, err
		}
		logger.SetOutput(io.MultiWriter(os.Stdout, file))
	default:
		logger.SetOutput(os.Stdout)
	}

	return &Logger{Logger: logger}, nil
}

// WithContext creates a logger with context values
func (l *Logger) WithContext(ctx context.Context) *logrus.Entry {
	entry := l.Logger.WithFields(logrus.Fields{})

	// Extract common context values
	if requestID := ctx.Value(ContextKeyRequestID); requestID != nil {
		entry = entry.WithField("request_id", requestID)
	}
	if userID := ctx.Value(ContextKeyUserID); userID != nil {
		entry = entry.WithField("user_id", userID)
	}
	if pipelineID := ctx.Value(ContextKeyPipelineID); pipelineID != nil {
		entry = entry.WithField("pipeline_id", pipelineID)
	}
	if executionID := ctx.Value(ContextKeyExecutionID); executionID != nil {
		entry = entry.WithField("execution_id", executionID)
	}
	if jobID := ctx.Value(ContextKeyJobID); jobID != nil {
		entry = entry.WithField("job_id", jobID)
	}
	if operation := ctx.Value(ContextKeyOperation); operation != nil {
		entry = entry.WithField("operation", operation)
	}
	if component := ctx.Value(ContextKeyComponent); component != nil {
		entry = entry.WithField("component", component)
	}

	return entry
}

// WithComponent creates a logger with component information
func (l *Logger) WithComponent(component string) *logrus.Entry {
	return l.Logger.WithField("component", component)
}

// WithOperation creates a logger with operation information
func (l *Logger) WithOperation(operation string) *logrus.Entry {
	return l.Logger.WithField("operation", operation)
}

// WithPipeline creates a logger with pipeline context
func (l *Logger) WithPipeline(pipelineID string) *logrus.Entry {
	return l.Logger.WithField("pipeline_id", pipelineID)
}

// WithExecution creates a logger with execution context
func (l *Logger) WithExecution(executionID string) *logrus.Entry {
	return l.Logger.WithField("execution_id", executionID)
}

// WithJob creates a logger with job context
func (l *Logger) WithJob(jobID string) *logrus.Entry {
	return l.Logger.WithField("job_id", jobID)
}

// WithUser creates a logger with user context
func (l *Logger) WithUser(userID string) *logrus.Entry {
	return l.Logger.WithField("user_id", userID)
}

// WithRequest creates a logger with request context
func (l *Logger) WithRequest(requestID string) *logrus.Entry {
	return l.Logger.WithField("request_id", requestID)
}

// WithError creates a logger with error information
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

// LogPipelineEvent logs pipeline-related events
func (l *Logger) LogPipelineEvent(ctx context.Context, event string, pipelineID string, details map[string]interface{}) {
	entry := l.WithContext(ctx).
		WithField("event", event).
		WithField("pipeline_id", pipelineID).
		WithField("event_type", "pipeline")

	for key, value := range details {
		entry = entry.WithField(key, value)
	}

	entry.Info("Pipeline event")
}

// LogExecutionEvent logs execution-related events
func (l *Logger) LogExecutionEvent(ctx context.Context, event string, executionID string, details map[string]interface{}) {
	entry := l.WithContext(ctx).
		WithField("event", event).
		WithField("execution_id", executionID).
		WithField("event_type", "execution")

	for key, value := range details {
		entry = entry.WithField(key, value)
	}

	entry.Info("Execution event")
}

// LogJobEvent logs job-related events
func (l *Logger) LogJobEvent(ctx context.Context, event string, jobID string, details map[string]interface{}) {
	entry := l.WithContext(ctx).
		WithField("event", event).
		WithField("job_id", jobID).
		WithField("event_type", "job")

	for key, value := range details {
		entry = entry.WithField(key, value)
	}

	entry.Info("Job event")
}

// LogSecurityEvent logs security-related events
func (l *Logger) LogSecurityEvent(ctx context.Context, event string, details map[string]interface{}) {
	entry := l.WithContext(ctx).
		WithField("event", event).
		WithField("event_type", "security")

	for key, value := range details {
		entry = entry.WithField(key, value)
	}

	entry.Warn("Security event")
}

// LogAPIRequest logs API request information
func (l *Logger) LogAPIRequest(ctx context.Context, method, path string, statusCode int, duration time.Duration, details map[string]interface{}) {
	entry := l.WithContext(ctx).
		WithField("method", method).
		WithField("path", path).
		WithField("status_code", statusCode).
		WithField("duration_ms", duration.Milliseconds()).
		WithField("event_type", "api_request")

	for key, value := range details {
		entry = entry.WithField(key, value)
	}

	if statusCode >= 400 {
		entry.Warn("API request completed with error")
	} else {
		entry.Info("API request completed")
	}
}

// LogDatabaseOperation logs database operations
func (l *Logger) LogDatabaseOperation(ctx context.Context, operation string, table string, duration time.Duration, err error) {
	entry := l.WithContext(ctx).
		WithField("operation", operation).
		WithField("table", table).
		WithField("duration_ms", duration.Milliseconds()).
		WithField("event_type", "database")

	if err != nil {
		entry.WithError(err).Error("Database operation failed")
	} else {
		entry.Debug("Database operation completed")
	}
}

// Default logger instance
var defaultLogger *Logger

// InitDefault initializes the default logger
func InitDefault(config *Config) error {
	logger, err := New(config)
	if err != nil {
		return err
	}
	defaultLogger = logger
	return nil
}

// Default returns the default logger
func Default() *Logger {
	if defaultLogger == nil {
		// Create a basic logger if none exists
		config := &Config{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		}
		logger, _ := New(config)
		defaultLogger = logger
	}
	return defaultLogger
}
