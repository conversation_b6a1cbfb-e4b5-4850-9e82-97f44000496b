package auth

import (
	"context"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// TokenType represents the type of JWT token
type TokenType string

const (
	AccessToken  TokenType = "access"
	RefreshToken TokenType = "refresh"
)

// <PERSON>laims represents JWT claims
type Claims struct {
	UserID       uuid.UUID `json:"user_id"`
	Email        string    `json:"email"`
	Roles        []string  `json:"roles"`
	Permissions  []string  `json:"permissions"`
	TokenType    TokenType `json:"token_type"`
	SessionID    string    `json:"session_id"`
	jwt.RegisteredClaims
}

// TokenPair represents access and refresh tokens
type Token<PERSON>air struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	TokenType    string    `json:"token_type"`
}

// JWTManager handles JWT operations
type J<PERSON><PERSON>anager struct {
	secret           []byte
	accessExpiration time.Duration
	refreshExpiration time.Duration
	issuer           string
	algorithm        string
}

// NewJWTManager creates a new JWT manager
func NewJWTManager(secret string, accessExpiration, refreshExpiration time.Duration, issuer string) *JWTManager {
	return &JWTManager{
		secret:            []byte(secret),
		accessExpiration:  accessExpiration,
		refreshExpiration: refreshExpiration,
		issuer:           issuer,
		algorithm:        "HS256",
	}
}

// GenerateTokenPair generates access and refresh tokens
func (j *JWTManager) GenerateTokenPair(userID uuid.UUID, email string, roles, permissions []string) (*TokenPair, error) {
	sessionID := uuid.New().String()
	now := time.Now()

	// Generate access token
	accessClaims := &Claims{
		UserID:      userID,
		Email:       email,
		Roles:       roles,
		Permissions: permissions,
		TokenType:   AccessToken,
		SessionID:   sessionID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   userID.String(),
			Audience:  []string{"chainops"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.accessExpiration)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        uuid.New().String(),
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString(j.secret)
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %w", err)
	}

	// Generate refresh token
	refreshClaims := &Claims{
		UserID:    userID,
		Email:     email,
		TokenType: RefreshToken,
		SessionID: sessionID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   userID.String(),
			Audience:  []string{"chainops"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.refreshExpiration)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        uuid.New().String(),
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString(j.secret)
	if err != nil {
		return nil, fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessTokenString,
		RefreshToken: refreshTokenString,
		ExpiresAt:    accessClaims.ExpiresAt.Time,
		TokenType:    "Bearer",
	}, nil
}

// ValidateToken validates a JWT token and returns claims
func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	// Check if token is expired
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(time.Now()) {
		return nil, fmt.Errorf("token is expired")
	}

	return claims, nil
}

// RefreshToken generates a new access token using a refresh token
func (j *JWTManager) RefreshToken(refreshTokenString string, roles, permissions []string) (*TokenPair, error) {
	claims, err := j.ValidateToken(refreshTokenString)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	if claims.TokenType != RefreshToken {
		return nil, fmt.Errorf("token is not a refresh token")
	}

	// Generate new token pair with the same session ID
	return j.generateTokenPairWithSession(claims.UserID, claims.Email, roles, permissions, claims.SessionID)
}

// generateTokenPairWithSession generates tokens with a specific session ID
func (j *JWTManager) generateTokenPairWithSession(userID uuid.UUID, email string, roles, permissions []string, sessionID string) (*TokenPair, error) {
	now := time.Now()

	// Generate access token
	accessClaims := &Claims{
		UserID:      userID,
		Email:       email,
		Roles:       roles,
		Permissions: permissions,
		TokenType:   AccessToken,
		SessionID:   sessionID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   userID.String(),
			Audience:  []string{"chainops"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.accessExpiration)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        uuid.New().String(),
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString(j.secret)
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %w", err)
	}

	// Generate refresh token
	refreshClaims := &Claims{
		UserID:    userID,
		Email:     email,
		TokenType: RefreshToken,
		SessionID: sessionID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   userID.String(),
			Audience:  []string{"chainops"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.refreshExpiration)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        uuid.New().String(),
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString(j.secret)
	if err != nil {
		return nil, fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessTokenString,
		RefreshToken: refreshTokenString,
		ExpiresAt:    accessClaims.ExpiresAt.Time,
		TokenType:    "Bearer",
	}, nil
}

// ExtractTokenFromContext extracts JWT claims from context
func ExtractTokenFromContext(ctx context.Context) (*Claims, bool) {
	claims, ok := ctx.Value("jwt_claims").(*Claims)
	return claims, ok
}

// GetUserIDFromContext extracts user ID from context
func GetUserIDFromContext(ctx context.Context) (uuid.UUID, bool) {
	claims, ok := ExtractTokenFromContext(ctx)
	if !ok {
		return uuid.Nil, false
	}
	return claims.UserID, true
}

// GetUserEmailFromContext extracts user email from context
func GetUserEmailFromContext(ctx context.Context) (string, bool) {
	claims, ok := ExtractTokenFromContext(ctx)
	if !ok {
		return "", false
	}
	return claims.Email, true
}

// GetUserRolesFromContext extracts user roles from context
func GetUserRolesFromContext(ctx context.Context) ([]string, bool) {
	claims, ok := ExtractTokenFromContext(ctx)
	if !ok {
		return nil, false
	}
	return claims.Roles, true
}

// GetUserPermissionsFromContext extracts user permissions from context
func GetUserPermissionsFromContext(ctx context.Context) ([]string, bool) {
	claims, ok := ExtractTokenFromContext(ctx)
	if !ok {
		return nil, false
	}
	return claims.Permissions, true
}

// HasPermission checks if user has a specific permission
func HasPermission(ctx context.Context, permission string) bool {
	permissions, ok := GetUserPermissionsFromContext(ctx)
	if !ok {
		return false
	}

	for _, p := range permissions {
		if p == permission {
			return true
		}
	}
	return false
}

// HasRole checks if user has a specific role
func HasRole(ctx context.Context, role string) bool {
	roles, ok := GetUserRolesFromContext(ctx)
	if !ok {
		return false
	}

	for _, r := range roles {
		if r == role {
			return true
		}
	}
	return false
}

// HasAnyRole checks if user has any of the specified roles
func HasAnyRole(ctx context.Context, roles ...string) bool {
	userRoles, ok := GetUserRolesFromContext(ctx)
	if !ok {
		return false
	}

	for _, userRole := range userRoles {
		for _, role := range roles {
			if userRole == role {
				return true
			}
		}
	}
	return false
}

// HasAnyPermission checks if user has any of the specified permissions
func HasAnyPermission(ctx context.Context, permissions ...string) bool {
	userPermissions, ok := GetUserPermissionsFromContext(ctx)
	if !ok {
		return false
	}

	for _, userPermission := range userPermissions {
		for _, permission := range permissions {
			if userPermission == permission {
				return true
			}
		}
	}
	return false
}
