package middleware

import (
	"context"
	"net/http"
	"strings"

	"github.com/chainops/chainops/pkg/auth"
	"github.com/chainops/chainops/pkg/errors"
	"github.com/chainops/chainops/pkg/types"
	"github.com/gin-gonic/gin"
)

// AuthMiddleware creates JWT authentication middleware
func AuthMiddleware(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip authentication for certain paths
		if shouldSkipAuth(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, types.APIResponse{
				Success: false,
				Message: "Authorization header is required",
				Error:   "missing_authorization_header",
			})
			c.Abort()
			return
		}

		// Check Bearer token format
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.<PERSON>(http.StatusUnauthorized, types.APIResponse{
				Success: false,
				Message: "Invalid authorization header format",
				Error:   "invalid_authorization_format",
			})
			c.Abort()
			return
		}

		tokenString := parts[1]

		// Validate token
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, types.APIResponse{
				Success: false,
				Message: "Invalid or expired token",
				Error:   err.Error(),
			})
			c.Abort()
			return
		}

		// Check if it's an access token
		if claims.TokenType != auth.AccessToken {
			c.JSON(http.StatusUnauthorized, types.APIResponse{
				Success: false,
				Message: "Invalid token type",
				Error:   "invalid_token_type",
			})
			c.Abort()
			return
		}

		// Add claims to context
		ctx := context.WithValue(c.Request.Context(), "jwt_claims", claims)
		c.Request = c.Request.WithContext(ctx)

		// Add user info to Gin context for easier access
		c.Set("user_id", claims.UserID.String())
		c.Set("user_email", claims.Email)
		c.Set("user_roles", claims.Roles)
		c.Set("user_permissions", claims.Permissions)
		c.Set("session_id", claims.SessionID)

		c.Next()
	}
}

// OptionalAuthMiddleware creates optional JWT authentication middleware
func OptionalAuthMiddleware(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Check Bearer token format
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.Next()
			return
		}

		tokenString := parts[1]

		// Validate token
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			c.Next()
			return
		}

		// Check if it's an access token
		if claims.TokenType != auth.AccessToken {
			c.Next()
			return
		}

		// Add claims to context
		ctx := context.WithValue(c.Request.Context(), "jwt_claims", claims)
		c.Request = c.Request.WithContext(ctx)

		// Add user info to Gin context for easier access
		c.Set("user_id", claims.UserID.String())
		c.Set("user_email", claims.Email)
		c.Set("user_roles", claims.Roles)
		c.Set("user_permissions", claims.Permissions)
		c.Set("session_id", claims.SessionID)

		c.Next()
	}
}

// RequirePermission creates middleware that requires specific permission
func RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !auth.HasPermission(c.Request.Context(), permission) {
			appErr := errors.InsufficientPermissions("", permission)
			c.JSON(appErr.StatusCode, types.APIResponse{
				Success: false,
				Message: appErr.Message,
				Error:   string(appErr.Code),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// RequireRole creates middleware that requires specific role
func RequireRole(role string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !auth.HasRole(c.Request.Context(), role) {
			appErr := errors.Forbidden("insufficient role permissions")
			c.JSON(appErr.StatusCode, types.APIResponse{
				Success: false,
				Message: appErr.Message,
				Error:   string(appErr.Code),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// RequireAnyRole creates middleware that requires any of the specified roles
func RequireAnyRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !auth.HasAnyRole(c.Request.Context(), roles...) {
			appErr := errors.Forbidden("insufficient role permissions")
			c.JSON(appErr.StatusCode, types.APIResponse{
				Success: false,
				Message: appErr.Message,
				Error:   string(appErr.Code),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// RequireAnyPermission creates middleware that requires any of the specified permissions
func RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !auth.HasAnyPermission(c.Request.Context(), permissions...) {
			appErr := errors.InsufficientPermissions("", strings.Join(permissions, ", "))
			c.JSON(appErr.StatusCode, types.APIResponse{
				Success: false,
				Message: appErr.Message,
				Error:   string(appErr.Code),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// AdminOnly creates middleware that requires admin role
func AdminOnly() gin.HandlerFunc {
	return RequireRole("admin")
}

// shouldSkipAuth determines if authentication should be skipped for a path
func shouldSkipAuth(path string) bool {
	skipPaths := []string{
		"/health",
		"/metrics",
		"/api/v1/auth/login",
		"/api/v1/auth/register",
		"/api/v1/auth/refresh",
		"/api/v1/auth/forgot-password",
		"/api/v1/auth/reset-password",
		"/api/v1/webhooks",
		"/docs",
		"/swagger",
	}

	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}

	return false
}

// ExtractUserID extracts user ID from Gin context
func ExtractUserID(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}
	return userID.(string), true
}

// ExtractUserEmail extracts user email from Gin context
func ExtractUserEmail(c *gin.Context) (string, bool) {
	email, exists := c.Get("user_email")
	if !exists {
		return "", false
	}
	return email.(string), true
}

// ExtractUserRoles extracts user roles from Gin context
func ExtractUserRoles(c *gin.Context) ([]string, bool) {
	roles, exists := c.Get("user_roles")
	if !exists {
		return nil, false
	}
	return roles.([]string), true
}

// ExtractUserPermissions extracts user permissions from Gin context
func ExtractUserPermissions(c *gin.Context) ([]string, bool) {
	permissions, exists := c.Get("user_permissions")
	if !exists {
		return nil, false
	}
	return permissions.([]string), true
}

// ExtractSessionID extracts session ID from Gin context
func ExtractSessionID(c *gin.Context) (string, bool) {
	sessionID, exists := c.Get("session_id")
	if !exists {
		return "", false
	}
	return sessionID.(string), true
}

// RequireOwnership creates middleware that requires resource ownership or admin role
func RequireOwnership(getResourceOwnerID func(*gin.Context) (string, error)) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := ExtractUserID(c)
		if !exists {
			appErr := errors.Unauthorized("authentication required")
			c.JSON(appErr.StatusCode, types.APIResponse{
				Success: false,
				Message: appErr.Message,
				Error:   string(appErr.Code),
			})
			c.Abort()
			return
		}

		// Check if user is admin
		if auth.HasRole(c.Request.Context(), "admin") {
			c.Next()
			return
		}

		// Check ownership
		ownerID, err := getResourceOwnerID(c)
		if err != nil {
			appErr := errors.Internal("failed to check resource ownership")
			c.JSON(appErr.StatusCode, types.APIResponse{
				Success: false,
				Message: appErr.Message,
				Error:   string(appErr.Code),
			})
			c.Abort()
			return
		}

		if userID != ownerID {
			appErr := errors.Forbidden("access denied: insufficient permissions")
			c.JSON(appErr.StatusCode, types.APIResponse{
				Success: false,
				Message: appErr.Message,
				Error:   string(appErr.Code),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
