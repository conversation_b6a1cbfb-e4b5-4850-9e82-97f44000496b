package types

import (
	"time"

	"github.com/google/uuid"
)

// Pipeline represents a CI/CD pipeline configuration
type Pipeline struct {
	ID             uuid.UUID         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name           string            `json:"name" gorm:"not null"`
	Repository     string            `json:"repository" gorm:"not null"`
	Branch         string            `json:"branch" gorm:"default:main"`
	ConfigPath     string            `json:"config_path" gorm:"default:.chainops.yml"`
	YAMLContent    string            `json:"yaml_content" gorm:"type:text;not null"`
	Organization   *Organization     `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	OrganizationID *uuid.UUID        `json:"organization_id,omitempty"`
	CreatedBy      *User             `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	CreatedByID    *uuid.UUID        `json:"created_by_id,omitempty"`
	IsActive       bool              `json:"is_active" gorm:"default:true"`
	IsTemplate     bool              `json:"is_template" gorm:"default:false"`
	TemplateID     *uuid.UUID        `json:"template_id,omitempty" gorm:"type:uuid"`
	Variables      map[string]string `json:"variables" gorm:"serializer:json"`
	CreatedAt      time.Time         `json:"created_at"`
	UpdatedAt      time.Time         `json:"updated_at"`
}

// PipelineConfig represents the parsed YAML configuration
type PipelineConfig struct {
	Name      string            `yaml:"name" json:"name"`
	Triggers  []Trigger         `yaml:"triggers" json:"triggers"`
	Variables map[string]string `yaml:"variables" json:"variables"`
	Matrix    *MatrixConfig     `yaml:"matrix" json:"matrix"`
	Stages    []Stage           `yaml:"stages" json:"stages"`
	Timeout   string            `yaml:"timeout" json:"timeout"`
	Defaults  *DefaultsConfig   `yaml:"defaults" json:"defaults"`
}

// Trigger represents pipeline trigger configuration
type Trigger struct {
	On        []string          `yaml:"on" json:"on"`               // push, pull_request, tag, schedule
	Branches  []string          `yaml:"branches" json:"branches"`   // branch filters
	Paths     []string          `yaml:"paths" json:"paths"`         // path filters
	Schedule  string            `yaml:"schedule" json:"schedule"`   // cron expression
	If        string            `yaml:"if" json:"if"`               // conditional expression
	When      string            `yaml:"when" json:"when"`           // when condition
	Only      []string          `yaml:"only" json:"only"`           // only run on these conditions
	Except    []string          `yaml:"except" json:"except"`       // except these conditions
	Variables map[string]string `yaml:"variables" json:"variables"` // trigger-specific variables
}

// MatrixConfig represents matrix build configuration
type MatrixConfig struct {
	Strategy string                   `yaml:"strategy" json:"strategy"` // "matrix", "parallel", "sequential"
	Matrix   map[string][]interface{} `yaml:"matrix" json:"matrix"`
	Include  []map[string]interface{} `yaml:"include" json:"include"`
	Exclude  []map[string]interface{} `yaml:"exclude" json:"exclude"`
	MaxJobs  int                      `yaml:"max_jobs" json:"max_jobs"`
	FailFast bool                     `yaml:"fail_fast" json:"fail_fast"`
}

// DefaultsConfig represents default configuration
type DefaultsConfig struct {
	Runner      string            `yaml:"runner" json:"runner"`
	Image       string            `yaml:"image" json:"image"`
	Environment map[string]string `yaml:"environment" json:"environment"`
	Timeout     string            `yaml:"timeout" json:"timeout"`
}

// Stage represents a pipeline stage
type Stage struct {
	Name         string            `yaml:"name" json:"name"`
	Steps        []Step            `yaml:"steps" json:"steps"`
	Dependencies []string          `yaml:"dependencies" json:"dependencies"`
	Condition    string            `yaml:"condition" json:"condition"`
	Parallel     bool              `yaml:"parallel" json:"parallel"`
	Matrix       *MatrixConfig     `yaml:"matrix" json:"matrix"`
	Environment  map[string]string `yaml:"environment" json:"environment"`
	Timeout      string            `yaml:"timeout" json:"timeout"`
	Approval     *ApprovalConfig   `yaml:"approval" json:"approval"`
}

// ApprovalConfig represents manual approval configuration
type ApprovalConfig struct {
	Required     bool     `yaml:"required" json:"required"`
	Approvers    []string `yaml:"approvers" json:"approvers"`
	MinApprovals int      `yaml:"min_approvals" json:"min_approvals"`
	Timeout      string   `yaml:"timeout" json:"timeout"`
	Message      string   `yaml:"message" json:"message"`
}

// Step represents a pipeline step
type Step struct {
	Name            string            `yaml:"name" json:"name"`
	Uses            string            `yaml:"uses" json:"uses"` // plugin name
	Run             string            `yaml:"run" json:"run"`   // shell command
	With            map[string]string `yaml:"with" json:"with"` // plugin inputs
	Environment     map[string]string `yaml:"environment" json:"environment"`
	Condition       *ConditionConfig  `yaml:"condition" json:"condition"`
	Timeout         string            `yaml:"timeout" json:"timeout"`
	Retry           *RetryConfig      `yaml:"retry" json:"retry"`
	Cache           *CacheConfig      `yaml:"cache" json:"cache"`
	Artifacts       *ArtifactConfig   `yaml:"artifacts" json:"artifacts"`
	Secrets         []string          `yaml:"secrets" json:"secrets"`
	AllowFailure    bool              `yaml:"allow_failure" json:"allow_failure"`
	ContinueOnError bool              `yaml:"continue_on_error" json:"continue_on_error"`
	ManualApproval  *ApprovalConfig   `yaml:"manual_approval" json:"manual_approval"`
	Dependencies    []string          `yaml:"dependencies" json:"dependencies"`
	Matrix          *MatrixConfig     `yaml:"matrix" json:"matrix"`
}

// ConditionConfig represents conditional execution logic
type ConditionConfig struct {
	If     string   `yaml:"if" json:"if"`         // conditional expression
	When   string   `yaml:"when" json:"when"`     // when condition (always, on_success, on_failure, manual)
	Only   []string `yaml:"only" json:"only"`     // only run on these conditions
	Except []string `yaml:"except" json:"except"` // except these conditions
}

// RetryConfig represents retry configuration
type RetryConfig struct {
	Attempts      int     `yaml:"attempts" json:"attempts"`
	BackoffFactor float64 `yaml:"backoff_factor" json:"backoff_factor"`
	MaxDelay      string  `yaml:"max_delay" json:"max_delay"`
}

// CacheConfig represents caching configuration
type CacheConfig struct {
	Key         string   `yaml:"key" json:"key"`
	Paths       []string `yaml:"paths" json:"paths"`
	RestoreKeys []string `yaml:"restore_keys" json:"restore_keys"`
	TTL         string   `yaml:"ttl" json:"ttl"`
}

// ArtifactConfig represents artifact configuration
type ArtifactConfig struct {
	Name        string   `yaml:"name" json:"name"`
	Paths       []string `yaml:"paths" json:"paths"`
	Retention   string   `yaml:"retention" json:"retention"`
	Compression bool     `yaml:"compression" json:"compression"`
}

// PipelineExecution represents a pipeline execution instance
type PipelineExecution struct {
	ID            uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Pipeline      *Pipeline              `json:"pipeline,omitempty" gorm:"foreignKey:PipelineID"`
	PipelineID    uuid.UUID              `json:"pipeline_id" gorm:"not null"`
	Status        ExecutionStatus        `json:"status" gorm:"not null"`
	TriggerType   TriggerType            `json:"trigger_type" gorm:"not null"`
	TriggerData   map[string]interface{} `json:"trigger_data" gorm:"serializer:json"`
	Variables     map[string]string      `json:"variables" gorm:"serializer:json"`
	CommitSHA     string                 `json:"commit_sha"`
	CommitMessage string                 `json:"commit_message"`
	Branch        string                 `json:"branch"`
	StartedAt     *time.Time             `json:"started_at"`
	CompletedAt   *time.Time             `json:"completed_at"`
	Duration      time.Duration          `json:"duration" gorm:"-"` // Computed field
	CreatedAt     time.Time              `json:"created_at"`
	Jobs          []Job                  `json:"jobs,omitempty" gorm:"foreignKey:ExecutionID"`
}

// Job represents a job within a pipeline execution
type Job struct {
	ID              uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Execution       *PipelineExecution     `json:"execution,omitempty" gorm:"foreignKey:ExecutionID"`
	ExecutionID     uuid.UUID              `json:"execution_id" gorm:"not null"`
	PipelineID      uuid.UUID              `json:"pipeline_id" gorm:"not null"`
	Name            string                 `json:"name" gorm:"not null"`
	Stage           string                 `json:"stage" gorm:"not null"`
	Status          ExecutionStatus        `json:"status" gorm:"not null"`
	RunnerType      RunnerType             `json:"runner_type" gorm:"not null"`
	Image           string                 `json:"image"`
	Script          []string               `json:"script" gorm:"type:text[]"`
	Commands        []string               `json:"commands" gorm:"type:text[]"`
	Environment     map[string]string      `json:"environment" gorm:"serializer:json"`
	Config          map[string]interface{} `json:"config" gorm:"serializer:json"`
	Artifacts       map[string]interface{} `json:"artifacts" gorm:"serializer:json"`
	Dependencies    []string               `json:"dependencies" gorm:"type:text[]"`
	Condition       string                 `json:"condition"`
	AllowFailure    bool                   `json:"allow_failure" gorm:"default:false"`
	ContinueOnError bool                   `json:"continue_on_error" gorm:"default:false"`
	ManualApproval  bool                   `json:"manual_approval" gorm:"default:false"`
	ApprovedBy      *uuid.UUID             `json:"approved_by" gorm:"type:uuid"`
	ApprovedAt      *time.Time             `json:"approved_at"`
	RetryCount      int                    `json:"retry_count" gorm:"default:0"`
	MaxRetries      int                    `json:"max_retries" gorm:"default:0"`
	LogsURL         string                 `json:"logs_url"`
	StartedAt       *time.Time             `json:"started_at"`
	CompletedAt     *time.Time             `json:"completed_at"`
	CreatedAt       time.Time              `json:"created_at"`
}

// User represents a system user
type User struct {
	ID           uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Username     string     `json:"username" gorm:"unique;not null"`
	Email        string     `json:"email" gorm:"unique;not null"`
	FullName     string     `json:"full_name"`
	PasswordHash string     `json:"-" gorm:"column:password_hash"` // Hidden from JSON
	AvatarURL    string     `json:"avatar_url"`
	Provider     string     `json:"provider" gorm:"not null"` // github, gitlab, local
	ProviderID   string     `json:"provider_id"`
	IsActive     bool       `json:"is_active" gorm:"default:true"`
	IsAdmin      bool       `json:"is_admin" gorm:"default:false"`
	LastLogin    *time.Time `json:"last_login"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// Organization represents an organization/team
type Organization struct {
	ID          uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string     `json:"name" gorm:"unique;not null"`
	DisplayName string     `json:"display_name"`
	Description string     `json:"description"`
	AvatarURL   string     `json:"avatar_url"`
	CreatedBy   *User      `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	CreatedByID *uuid.UUID `json:"created_by_id,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// Secret represents an encrypted secret
type Secret struct {
	ID             uuid.UUID     `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Organization   *Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	OrganizationID uuid.UUID     `json:"organization_id" gorm:"not null"`
	Name           string        `json:"name" gorm:"not null"`
	Description    string        `json:"description"`
	EncryptedValue string        `json:"-" gorm:"not null"` // Never expose in JSON
	CreatedBy      *User         `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	CreatedByID    *uuid.UUID    `json:"created_by_id,omitempty"`
	CreatedAt      time.Time     `json:"created_at"`
	UpdatedAt      time.Time     `json:"updated_at"`
}

// Artifact represents a build artifact
type Artifact struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Job         *Job      `json:"job,omitempty" gorm:"foreignKey:JobID"`
	JobID       uuid.UUID `json:"job_id" gorm:"not null"`
	Name        string    `json:"name" gorm:"not null"`
	Path        string    `json:"path" gorm:"not null"`
	SizeBytes   int64     `json:"size_bytes"`
	ContentType string    `json:"content_type"`
	StorageURL  string    `json:"storage_url"`
	CreatedAt   time.Time `json:"created_at"`
}

// ExecutionStatus represents the status of a pipeline execution or job
type ExecutionStatus string

const (
	StatusPending         ExecutionStatus = "pending"
	StatusQueued          ExecutionStatus = "queued"
	StatusRunning         ExecutionStatus = "running"
	StatusSuccess         ExecutionStatus = "success"
	StatusFailure         ExecutionStatus = "failure"
	StatusCancelled       ExecutionStatus = "cancelled"
	StatusSkipped         ExecutionStatus = "skipped"
	StatusWaitingApproval ExecutionStatus = "waiting_approval"
	StatusBlocked         ExecutionStatus = "blocked"
	StatusTimeout         ExecutionStatus = "timeout"
	StatusRetrying        ExecutionStatus = "retrying"
)

// TriggerType represents the type of trigger that started the execution
type TriggerType string

const (
	TriggerGit       TriggerType = "git"
	TriggerManual    TriggerType = "manual"
	TriggerScheduled TriggerType = "scheduled"
	TriggerAPI       TriggerType = "api"
	TriggerPipeline  TriggerType = "pipeline" // Triggered by another pipeline
	TriggerWebhook   TriggerType = "webhook"  // External webhook
	TriggerTag       TriggerType = "tag"      // Git tag trigger
)

// TriggerPipelineRequest represents a request to trigger a pipeline
type TriggerPipelineRequest struct {
	PipelineID  uuid.UUID              `json:"pipeline_id"`
	TriggerType TriggerType            `json:"trigger_type"`
	Branch      string                 `json:"branch,omitempty"`
	CommitSHA   string                 `json:"commit_sha,omitempty"`
	Variables   map[string]string      `json:"variables,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// PipelineStatus represents pipeline execution status (alias for compatibility)
type PipelineStatus = ExecutionStatus

// Pipeline status constants for compatibility
const (
	PipelineStatusPending   = StatusPending
	PipelineStatusRunning   = StatusRunning
	PipelineStatusSuccess   = StatusSuccess
	PipelineStatusFailed    = StatusFailure
	PipelineStatusCancelled = StatusCancelled
)

// RunnerType represents the type of runner used for job execution
type RunnerType string

const (
	RunnerDocker     RunnerType = "docker"
	RunnerKubernetes RunnerType = "kubernetes"
)

// JobResult represents the result of a job execution
type JobResult struct {
	Success   bool                   `json:"success"`
	ExitCode  int                    `json:"exit_code"`
	Output    string                 `json:"output"`
	Error     string                 `json:"error,omitempty"`
	Artifacts []ArtifactInfo         `json:"artifacts,omitempty"`
	Metrics   map[string]interface{} `json:"metrics,omitempty"`
}

// ArtifactInfo represents information about an artifact
type ArtifactInfo struct {
	Name        string `json:"name"`
	Path        string `json:"path"`
	SizeBytes   int64  `json:"size_bytes"`
	ContentType string `json:"content_type"`
}

// WebhookPayload represents a Git webhook payload
type WebhookPayload struct {
	Repository struct {
		Name     string `json:"name"`
		FullName string `json:"full_name"`
		CloneURL string `json:"clone_url"`
	} `json:"repository"`
	Ref     string `json:"ref"`
	Before  string `json:"before"`
	After   string `json:"after"`
	Commits []struct {
		ID      string `json:"id"`
		Message string `json:"message"`
		Author  struct {
			Name  string `json:"name"`
			Email string `json:"email"`
		} `json:"author"`
	} `json:"commits"`
	Pusher struct {
		Name  string `json:"name"`
		Email string `json:"email"`
	} `json:"pusher"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	APIResponse
	Pagination PaginationInfo `json:"pagination"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	Page       int   `json:"page"`
	PerPage    int   `json:"per_page"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// Deployment represents a deployment record
type Deployment struct {
	ID           string                 `json:"id" gorm:"primary_key"`
	PipelineID   uuid.UUID              `json:"pipeline_id" gorm:"not null"`
	ExecutionID  uuid.UUID              `json:"execution_id" gorm:"not null"`
	Environment  string                 `json:"environment" gorm:"not null"`
	Strategy     string                 `json:"strategy" gorm:"not null"`
	Status       DeploymentStatus       `json:"status" gorm:"not null"`
	Config       map[string]interface{} `json:"config" gorm:"type:jsonb"`
	Result       *DeploymentResult      `json:"result" gorm:"type:jsonb"`
	Error        string                 `json:"error"`
	CreatedBy    uuid.UUID              `json:"created_by"`
	CreatedAt    time.Time              `json:"created_at"`
	StartedAt    *time.Time             `json:"started_at"`
	CompletedAt  *time.Time             `json:"completed_at"`
	RolledBackAt *time.Time             `json:"rolled_back_at"`
}

// DeploymentResult represents deployment result
type DeploymentResult struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	Resources []interface{}          `json:"resources"`
	Endpoints []interface{}          `json:"endpoints"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// DeploymentStatus represents deployment status
type DeploymentStatus string

const (
	DeploymentStatusPending    DeploymentStatus = "pending"
	DeploymentStatusRunning    DeploymentStatus = "running"
	DeploymentStatusSuccess    DeploymentStatus = "success"
	DeploymentStatusFailure    DeploymentStatus = "failure"
	DeploymentStatusRolledBack DeploymentStatus = "rolled_back"
	DeploymentStatusCancelled  DeploymentStatus = "cancelled"
)

// PipelineStep represents a step in pipeline execution (for plugins)
type PipelineStep struct {
	Name        string            `json:"name"`
	Uses        string            `json:"uses"`
	Run         string            `json:"run"`
	With        map[string]string `json:"with"`
	Environment map[string]string `json:"environment"`
	Condition   string            `json:"condition"`
	Timeout     string            `json:"timeout"`
}

// Status represents execution status (alias for compatibility)
type Status = ExecutionStatus

// TableName methods for GORM
func (Pipeline) TableName() string          { return "pipelines" }
func (PipelineExecution) TableName() string { return "pipeline_executions" }
func (Job) TableName() string               { return "jobs" }
func (User) TableName() string              { return "users" }
func (Organization) TableName() string      { return "organizations" }
func (Secret) TableName() string            { return "secrets" }
func (Artifact) TableName() string          { return "artifacts" }
func (Deployment) TableName() string        { return "deployments" }

// AuditLog represents an audit log entry
type AuditLog struct {
	ID        uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID    *uuid.UUID             `json:"user_id" gorm:"type:uuid"`
	Action    string                 `json:"action" gorm:"not null"`
	Resource  string                 `json:"resource" gorm:"not null"`
	Metadata  map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	IPAddress string                 `json:"ip_address"`
	UserAgent string                 `json:"user_agent"`
	Timestamp time.Time              `json:"timestamp" gorm:"not null"`
}

func (AuditLog) TableName() string { return "audit_logs" }

// Role represents a user role for RBAC
type Role struct {
	ID          uuid.UUID    `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string       `json:"name" gorm:"unique;not null"`
	Description string       `json:"description"`
	Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions;"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
}

// Permission represents a permission for RBAC
type Permission struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"unique;not null"`
	Resource    string    `json:"resource" gorm:"not null"`
	Action      string    `json:"action" gorm:"not null"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

// UserRole represents the many-to-many relationship between users and roles
type UserRole struct {
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	RoleID uuid.UUID `json:"role_id" gorm:"type:uuid;not null"`
	User   *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Role   *Role     `json:"role,omitempty" gorm:"foreignKey:RoleID"`
}

// PipelineTemplate represents a reusable pipeline template
type PipelineTemplate struct {
	ID          uuid.UUID         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string            `json:"name" gorm:"not null"`
	Description string            `json:"description"`
	Category    string            `json:"category"`
	Tags        []string          `json:"tags" gorm:"type:text[]"`
	YAMLContent string            `json:"yaml_content" gorm:"type:text;not null"`
	Variables   map[string]string `json:"variables" gorm:"type:jsonb"`
	IsPublic    bool              `json:"is_public" gorm:"default:false"`
	CreatedBy   *User             `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	CreatedByID *uuid.UUID        `json:"created_by_id,omitempty"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// Plugin represents a pipeline plugin/action
type Plugin struct {
	ID          uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string                 `json:"name" gorm:"unique;not null"`
	Version     string                 `json:"version" gorm:"not null"`
	Description string                 `json:"description"`
	Author      string                 `json:"author"`
	Repository  string                 `json:"repository"`
	DockerImage string                 `json:"docker_image"`
	Inputs      map[string]interface{} `json:"inputs" gorm:"type:jsonb"`
	Outputs     map[string]interface{} `json:"outputs" gorm:"type:jsonb"`
	IsOfficial  bool                   `json:"is_official" gorm:"default:false"`
	IsActive    bool                   `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// Approval represents a manual approval record
type Approval struct {
	ID         uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	JobID      uuid.UUID      `json:"job_id" gorm:"type:uuid;not null"`
	Job        *Job           `json:"job,omitempty" gorm:"foreignKey:JobID"`
	Status     ApprovalStatus `json:"status" gorm:"not null"`
	RequiredBy []uuid.UUID    `json:"required_by" gorm:"type:uuid[]"`
	ApprovedBy []uuid.UUID    `json:"approved_by" gorm:"type:uuid[]"`
	RejectedBy *uuid.UUID     `json:"rejected_by" gorm:"type:uuid"`
	Message    string         `json:"message"`
	Reason     string         `json:"reason"`
	ExpiresAt  *time.Time     `json:"expires_at"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
}

// ApprovalStatus represents the status of an approval
type ApprovalStatus string

const (
	ApprovalStatusPending  ApprovalStatus = "pending"
	ApprovalStatusApproved ApprovalStatus = "approved"
	ApprovalStatusRejected ApprovalStatus = "rejected"
	ApprovalStatusExpired  ApprovalStatus = "expired"
)

// Table names for new types
func (Role) TableName() string             { return "roles" }
func (Permission) TableName() string       { return "permissions" }
func (UserRole) TableName() string         { return "user_roles" }
func (PipelineTemplate) TableName() string { return "pipeline_templates" }
func (Plugin) TableName() string           { return "plugins" }
func (Approval) TableName() string         { return "approvals" }
