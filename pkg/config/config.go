package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server     ServerConfig     `yaml:"server" mapstructure:"server"`
	Database   DatabaseConfig   `yaml:"database" mapstructure:"database"`
	Redis      RedisConfig      `yaml:"redis" mapstructure:"redis"`
	JWT        JWTConfig        `yaml:"jwt" mapstructure:"jwt"`
	Logger     LoggerConfig     `yaml:"logger" mapstructure:"logger"`
	Security   SecurityConfig   `yaml:"security" mapstructure:"security"`
	Pipeline   PipelineConfig   `yaml:"pipeline" mapstructure:"pipeline"`
	Storage    StorageConfig    `yaml:"storage" mapstructure:"storage"`
	Monitoring MonitoringConfig `yaml:"monitoring" mapstructure:"monitoring"`
	Features   FeatureConfig    `yaml:"features" mapstructure:"features"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Host            string        `yaml:"host" mapstructure:"host" env:"SERVER_HOST" default:"0.0.0.0"`
	Port            int           `yaml:"port" mapstructure:"port" env:"SERVER_PORT" default:"8080"`
	ReadTimeout     time.Duration `yaml:"read_timeout" mapstructure:"read_timeout" env:"SERVER_READ_TIMEOUT" default:"30s"`
	WriteTimeout    time.Duration `yaml:"write_timeout" mapstructure:"write_timeout" env:"SERVER_WRITE_TIMEOUT" default:"30s"`
	IdleTimeout     time.Duration `yaml:"idle_timeout" mapstructure:"idle_timeout" env:"SERVER_IDLE_TIMEOUT" default:"60s"`
	ShutdownTimeout time.Duration `yaml:"shutdown_timeout" mapstructure:"shutdown_timeout" env:"SERVER_SHUTDOWN_TIMEOUT" default:"30s"`
	TLS             TLSConfig     `yaml:"tls" mapstructure:"tls"`
	CORS            CORSConfig    `yaml:"cors" mapstructure:"cors"`
}

// TLSConfig holds TLS configuration
type TLSConfig struct {
	Enabled  bool   `yaml:"enabled" mapstructure:"enabled" env:"TLS_ENABLED" default:"false"`
	CertFile string `yaml:"cert_file" mapstructure:"cert_file" env:"TLS_CERT_FILE"`
	KeyFile  string `yaml:"key_file" mapstructure:"key_file" env:"TLS_KEY_FILE"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowOrigins     []string `yaml:"allow_origins" mapstructure:"allow_origins" env:"CORS_ALLOW_ORIGINS"`
	AllowMethods     []string `yaml:"allow_methods" mapstructure:"allow_methods" env:"CORS_ALLOW_METHODS"`
	AllowHeaders     []string `yaml:"allow_headers" mapstructure:"allow_headers" env:"CORS_ALLOW_HEADERS"`
	AllowCredentials bool     `yaml:"allow_credentials" mapstructure:"allow_credentials" env:"CORS_ALLOW_CREDENTIALS" default:"true"`
	MaxAge           int      `yaml:"max_age" mapstructure:"max_age" env:"CORS_MAX_AGE" default:"86400"`
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Host            string        `yaml:"host" mapstructure:"host" env:"DB_HOST" default:"localhost"`
	Port            int           `yaml:"port" mapstructure:"port" env:"DB_PORT" default:"5432"`
	User            string        `yaml:"user" mapstructure:"user" env:"DB_USER" default:"chainops"`
	Password        string        `yaml:"password" mapstructure:"password" env:"DB_PASSWORD"`
	Name            string        `yaml:"name" mapstructure:"name" env:"DB_NAME" default:"chainops"`
	SSLMode         string        `yaml:"ssl_mode" mapstructure:"ssl_mode" env:"DB_SSL_MODE" default:"require"`
	MaxOpenConns    int           `yaml:"max_open_conns" mapstructure:"max_open_conns" env:"DB_MAX_OPEN_CONNS" default:"25"`
	MaxIdleConns    int           `yaml:"max_idle_conns" mapstructure:"max_idle_conns" env:"DB_MAX_IDLE_CONNS" default:"5"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime" mapstructure:"conn_max_lifetime" env:"DB_CONN_MAX_LIFETIME" default:"5m"`
	ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time" mapstructure:"conn_max_idle_time" env:"DB_CONN_MAX_IDLE_TIME" default:"5m"`
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Host         string        `yaml:"host" mapstructure:"host" env:"REDIS_HOST" default:"localhost"`
	Port         int           `yaml:"port" mapstructure:"port" env:"REDIS_PORT" default:"6379"`
	Password     string        `yaml:"password" mapstructure:"password" env:"REDIS_PASSWORD"`
	DB           int           `yaml:"db" mapstructure:"db" env:"REDIS_DB" default:"0"`
	PoolSize     int           `yaml:"pool_size" mapstructure:"pool_size" env:"REDIS_POOL_SIZE" default:"10"`
	MinIdleConns int           `yaml:"min_idle_conns" mapstructure:"min_idle_conns" env:"REDIS_MIN_IDLE_CONNS" default:"5"`
	DialTimeout  time.Duration `yaml:"dial_timeout" mapstructure:"dial_timeout" env:"REDIS_DIAL_TIMEOUT" default:"5s"`
	ReadTimeout  time.Duration `yaml:"read_timeout" mapstructure:"read_timeout" env:"REDIS_READ_TIMEOUT" default:"3s"`
	WriteTimeout time.Duration `yaml:"write_timeout" mapstructure:"write_timeout" env:"REDIS_WRITE_TIMEOUT" default:"3s"`
}

// JWTConfig holds JWT configuration
type JWTConfig struct {
	Secret           string        `yaml:"secret" mapstructure:"secret" env:"JWT_SECRET"`
	Expiration       time.Duration `yaml:"expiration" mapstructure:"expiration" env:"JWT_EXPIRATION" default:"24h"`
	RefreshExpiration time.Duration `yaml:"refresh_expiration" mapstructure:"refresh_expiration" env:"JWT_REFRESH_EXPIRATION" default:"168h"`
	Issuer           string        `yaml:"issuer" mapstructure:"issuer" env:"JWT_ISSUER" default:"chainops"`
	Algorithm        string        `yaml:"algorithm" mapstructure:"algorithm" env:"JWT_ALGORITHM" default:"HS256"`
}

// LoggerConfig holds logger configuration
type LoggerConfig struct {
	Level      string `yaml:"level" mapstructure:"level" env:"LOG_LEVEL" default:"info"`
	Format     string `yaml:"format" mapstructure:"format" env:"LOG_FORMAT" default:"json"`
	Output     string `yaml:"output" mapstructure:"output" env:"LOG_OUTPUT" default:"stdout"`
	File       string `yaml:"file" mapstructure:"file" env:"LOG_FILE"`
	MaxSize    int    `yaml:"max_size" mapstructure:"max_size" env:"LOG_MAX_SIZE" default:"100"`
	MaxBackups int    `yaml:"max_backups" mapstructure:"max_backups" env:"LOG_MAX_BACKUPS" default:"3"`
	MaxAge     int    `yaml:"max_age" mapstructure:"max_age" env:"LOG_MAX_AGE" default:"28"`
	Compress   bool   `yaml:"compress" mapstructure:"compress" env:"LOG_COMPRESS" default:"true"`
}

// SecurityConfig holds security configuration
type SecurityConfig struct {
	RateLimiting RateLimitConfig `yaml:"rate_limiting" mapstructure:"rate_limiting"`
	CSRF         CSRFConfig      `yaml:"csrf" mapstructure:"csrf"`
	Headers      HeadersConfig   `yaml:"headers" mapstructure:"headers"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled    bool          `yaml:"enabled" mapstructure:"enabled" env:"RATE_LIMIT_ENABLED" default:"true"`
	Requests   int           `yaml:"requests" mapstructure:"requests" env:"RATE_LIMIT_REQUESTS" default:"100"`
	Window     time.Duration `yaml:"window" mapstructure:"window" env:"RATE_LIMIT_WINDOW" default:"1m"`
	SkipPaths  []string      `yaml:"skip_paths" mapstructure:"skip_paths"`
	SkipIPs    []string      `yaml:"skip_ips" mapstructure:"skip_ips"`
}

// CSRFConfig holds CSRF configuration
type CSRFConfig struct {
	Enabled    bool   `yaml:"enabled" mapstructure:"enabled" env:"CSRF_ENABLED" default:"true"`
	Secret     string `yaml:"secret" mapstructure:"secret" env:"CSRF_SECRET"`
	TokenName  string `yaml:"token_name" mapstructure:"token_name" env:"CSRF_TOKEN_NAME" default:"csrf_token"`
	HeaderName string `yaml:"header_name" mapstructure:"header_name" env:"CSRF_HEADER_NAME" default:"X-CSRF-Token"`
}

// HeadersConfig holds security headers configuration
type HeadersConfig struct {
	ContentTypeNosniff bool   `yaml:"content_type_nosniff" mapstructure:"content_type_nosniff" default:"true"`
	FrameDeny          bool   `yaml:"frame_deny" mapstructure:"frame_deny" default:"true"`
	ContentSecurityPolicy string `yaml:"content_security_policy" mapstructure:"content_security_policy"`
	ReferrerPolicy     string `yaml:"referrer_policy" mapstructure:"referrer_policy" default:"strict-origin-when-cross-origin"`
}

// PipelineConfig holds pipeline configuration
type PipelineConfig struct {
	MaxConcurrentExecutions int           `yaml:"max_concurrent_executions" mapstructure:"max_concurrent_executions" env:"PIPELINE_MAX_CONCURRENT" default:"10"`
	DefaultTimeout          time.Duration `yaml:"default_timeout" mapstructure:"default_timeout" env:"PIPELINE_DEFAULT_TIMEOUT" default:"1h"`
	MaxTimeout              time.Duration `yaml:"max_timeout" mapstructure:"max_timeout" env:"PIPELINE_MAX_TIMEOUT" default:"6h"`
	WorkspaceDir            string        `yaml:"workspace_dir" mapstructure:"workspace_dir" env:"PIPELINE_WORKSPACE_DIR" default:"/tmp/chainops/workspaces"`
	ArtifactsDir            string        `yaml:"artifacts_dir" mapstructure:"artifacts_dir" env:"PIPELINE_ARTIFACTS_DIR" default:"/tmp/chainops/artifacts"`
	LogsDir                 string        `yaml:"logs_dir" mapstructure:"logs_dir" env:"PIPELINE_LOGS_DIR" default:"/tmp/chainops/logs"`
}

// StorageConfig holds storage configuration
type StorageConfig struct {
	Type   string      `yaml:"type" mapstructure:"type" env:"STORAGE_TYPE" default:"local"`
	Local  LocalConfig `yaml:"local" mapstructure:"local"`
	S3     S3Config    `yaml:"s3" mapstructure:"s3"`
}

// LocalConfig holds local storage configuration
type LocalConfig struct {
	BasePath string `yaml:"base_path" mapstructure:"base_path" env:"STORAGE_LOCAL_BASE_PATH" default:"/tmp/chainops/storage"`
}

// S3Config holds S3 storage configuration
type S3Config struct {
	Region          string `yaml:"region" mapstructure:"region" env:"STORAGE_S3_REGION"`
	Bucket          string `yaml:"bucket" mapstructure:"bucket" env:"STORAGE_S3_BUCKET"`
	AccessKeyID     string `yaml:"access_key_id" mapstructure:"access_key_id" env:"STORAGE_S3_ACCESS_KEY_ID"`
	SecretAccessKey string `yaml:"secret_access_key" mapstructure:"secret_access_key" env:"STORAGE_S3_SECRET_ACCESS_KEY"`
	Endpoint        string `yaml:"endpoint" mapstructure:"endpoint" env:"STORAGE_S3_ENDPOINT"`
	UseSSL          bool   `yaml:"use_ssl" mapstructure:"use_ssl" env:"STORAGE_S3_USE_SSL" default:"true"`
}

// MonitoringConfig holds monitoring configuration
type MonitoringConfig struct {
	Enabled    bool           `yaml:"enabled" mapstructure:"enabled" env:"MONITORING_ENABLED" default:"true"`
	Prometheus PrometheusConfig `yaml:"prometheus" mapstructure:"prometheus"`
	Jaeger     JaegerConfig   `yaml:"jaeger" mapstructure:"jaeger"`
}

// PrometheusConfig holds Prometheus configuration
type PrometheusConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled" env:"PROMETHEUS_ENABLED" default:"true"`
	Path    string `yaml:"path" mapstructure:"path" env:"PROMETHEUS_PATH" default:"/metrics"`
}

// JaegerConfig holds Jaeger configuration
type JaegerConfig struct {
	Enabled     bool   `yaml:"enabled" mapstructure:"enabled" env:"JAEGER_ENABLED" default:"false"`
	Endpoint    string `yaml:"endpoint" mapstructure:"endpoint" env:"JAEGER_ENDPOINT"`
	ServiceName string `yaml:"service_name" mapstructure:"service_name" env:"JAEGER_SERVICE_NAME" default:"chainops"`
}

// FeatureConfig holds feature flags
type FeatureConfig struct {
	BlueGreenDeployment bool `yaml:"blue_green_deployment" mapstructure:"blue_green_deployment" env:"FEATURE_BLUE_GREEN" default:"false"`
	CanaryDeployment    bool `yaml:"canary_deployment" mapstructure:"canary_deployment" env:"FEATURE_CANARY" default:"false"`
	SecurityScanning    bool `yaml:"security_scanning" mapstructure:"security_scanning" env:"FEATURE_SECURITY_SCANNING" default:"false"`
	MultiTenancy        bool `yaml:"multi_tenancy" mapstructure:"multi_tenancy" env:"FEATURE_MULTI_TENANCY" default:"false"`
	VisualEditor        bool `yaml:"visual_editor" mapstructure:"visual_editor" env:"FEATURE_VISUAL_EDITOR" default:"false"`
}

// Load loads configuration from file and environment variables
func Load(configPath string) (*Config, error) {
	viper.SetConfigType("yaml")
	
	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		viper.SetConfigName("config")
		viper.AddConfigPath(".")
		viper.AddConfigPath("./config")
		viper.AddConfigPath("/etc/chainops")
	}

	// Set environment variable prefix
	viper.SetEnvPrefix("CHAINOPS")
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Set defaults
	setDefaults()

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Override with environment variables
	overrideWithEnv()

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Validate configuration
	if err := validate(&config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &config, nil
}

// setDefaults sets default values for configuration
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "60s")
	viper.SetDefault("server.shutdown_timeout", "30s")

	// Database defaults
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "chainops")
	viper.SetDefault("database.name", "chainops")
	viper.SetDefault("database.ssl_mode", "require")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")

	// JWT defaults
	viper.SetDefault("jwt.expiration", "24h")
	viper.SetDefault("jwt.refresh_expiration", "168h")
	viper.SetDefault("jwt.issuer", "chainops")
	viper.SetDefault("jwt.algorithm", "HS256")

	// Logger defaults
	viper.SetDefault("logger.level", "info")
	viper.SetDefault("logger.format", "json")
	viper.SetDefault("logger.output", "stdout")

	// Pipeline defaults
	viper.SetDefault("pipeline.max_concurrent_executions", 10)
	viper.SetDefault("pipeline.default_timeout", "1h")
	viper.SetDefault("pipeline.max_timeout", "6h")
}

// overrideWithEnv overrides configuration with environment variables
func overrideWithEnv() {
	// Override with environment variables that have different names
	if val := os.Getenv("PORT"); val != "" {
		if port, err := strconv.Atoi(val); err == nil {
			viper.Set("server.port", port)
		}
	}
	
	if val := os.Getenv("DATABASE_URL"); val != "" {
		// Parse DATABASE_URL if provided
		// This is commonly used in cloud deployments
		viper.Set("database.url", val)
	}
}

// validate validates the configuration
func validate(config *Config) error {
	if config.JWT.Secret == "" {
		return fmt.Errorf("JWT secret is required")
	}
	
	if config.Database.Password == "" {
		return fmt.Errorf("database password is required")
	}
	
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", config.Server.Port)
	}
	
	return nil
}
