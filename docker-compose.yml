version: '3.8'

services:
  # ChainOps Application
  chainops:
    build: .
    container_name: chainops-app
    ports:
      - "8080:8080"
    environment:
      CHAINOPS_DATABASE_HOST: postgres
      CHAINOPS_DATABASE_PORT: 5432
      CHAINOPS_DATABASE_USER: chainops
      CHAINOPS_DATABASE_PASSWORD: password
      CHAINOPS_DATABASE_DATABASE: chainops
      CHAINOPS_CACHE_HOST: redis
      CHAINOPS_CACHE_PORT: 6379
      CHAINOPS_MESSAGE_QUEUE_URL: nats://nats:4222
      CHAINOPS_OBJECT_STORAGE_ENDPOINT: minio:9000
      CHAINOPS_OBJECT_STORAGE_ACCESS_KEY_ID: minioadmin
      CHAINOPS_OBJECT_STORAGE_SECRET_ACCESS_KEY: minioadmin
      CHAINOPS_RUNNER_TYPE: docker
      CHAINOPS_RUNNER_DOCKER_HOST: unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      nats:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - chainops-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chainops-postgres
    environment:
      POSTGRES_DB: chainops
      POSTGRES_USER: chainops
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chainops -d chainops"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - chainops-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: chainops-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - chainops-network
    restart: unless-stopped

  # NATS Message Queue
  nats:
    image: nats:2.10-alpine
    container_name: chainops-nats
    ports:
      - "4222:4222"
      - "8222:8222"
    volumes:
      - nats_data:/data
    command: 
      - "--jetstream"
      - "--store_dir=/data"
      - "--http_port=8222"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - chainops-network
    restart: unless-stopped

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: chainops-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - chainops-network
    restart: unless-stopped

  # Prometheus for Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: chainops-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployments/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - chainops-network
    restart: unless-stopped

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: chainops-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - chainops-network
    restart: unless-stopped



volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nats_data:
    driver: local
  minio_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  chainops-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
