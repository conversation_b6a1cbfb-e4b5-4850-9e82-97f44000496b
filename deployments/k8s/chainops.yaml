apiVersion: v1
kind: Secret
metadata:
  name: chainops-secret
  namespace: chainops
  labels:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: application
type: Opaque
data:
  # your-jwt-secret-key-change-in-production (base64 encoded)
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWtleS1jaGFuZ2UtaW4tcHJvZHVjdGlvbg==
  # your-encryption-key-32-chars-long (base64 encoded)
  encryption-key: eW91ci1lbmNyeXB0aW9uLWtleS0zMi1jaGFycy1sb25n
  # chainops_minio_password (base64 encoded)
  minio-secret: Y2hhaW5vcHNfbWluaW9fcGFzc3dvcmQ=
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chainops
  namespace: chainops
  labels:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: application
    app.kubernetes.io/version: "0.1.0"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: chainops
      app.kubernetes.io/component: application
  template:
    metadata:
      labels:
        app.kubernetes.io/name: chainops
        app.kubernetes.io/component: application
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8081"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: chainops
      initContainers:
      - name: migrate
        image: chainops:latest
        command: ["./migrate", "up"]
        env:
        - name: CHAINOPS_DATABASE_HOST
          value: postgres-service
        - name: CHAINOPS_DATABASE_PORT
          value: "5432"
        - name: CHAINOPS_DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-user
        - name: CHAINOPS_DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-password
        - name: CHAINOPS_DATABASE_NAME
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-db
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
      containers:
      - name: chainops
        image: chainops:latest
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        - containerPort: 8081
          name: metrics
          protocol: TCP
        env:
        - name: CHAINOPS_ENV
          value: production
        - name: CHAINOPS_DATABASE_HOST
          value: postgres-service
        - name: CHAINOPS_DATABASE_PORT
          value: "5432"
        - name: CHAINOPS_DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-user
        - name: CHAINOPS_DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-password
        - name: CHAINOPS_DATABASE_NAME
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-db
        - name: CHAINOPS_CACHE_HOST
          value: redis-service
        - name: CHAINOPS_CACHE_PORT
          value: "6379"
        - name: CHAINOPS_MESSAGE_QUEUE_URL
          value: nats://nats-service:4222
        - name: CHAINOPS_OBJECT_STORAGE_ENDPOINT
          value: minio-service:9000
        - name: CHAINOPS_OBJECT_STORAGE_ACCESS_KEY_ID
          value: chainops
        - name: CHAINOPS_OBJECT_STORAGE_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: chainops-secret
              key: minio-secret
        - name: CHAINOPS_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: chainops-secret
              key: jwt-secret
        - name: CHAINOPS_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: chainops-secret
              key: encryption-key
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config
        configMap:
          name: chainops-config
      securityContext:
        fsGroup: 1001
---
apiVersion: v1
kind: Service
metadata:
  name: chainops-service
  namespace: chainops
  labels:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: application
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: metrics
  selector:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: application
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: chainops
  namespace: chainops
  labels:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: application
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: chainops-runner
  labels:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: rbac
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log", "pods/exec"]
  verbs: ["create", "delete", "get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["create", "delete", "get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: chainops-runner
  labels:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: rbac
subjects:
- kind: ServiceAccount
  name: chainops
  namespace: chainops
roleRef:
  kind: ClusterRole
  name: chainops-runner
  apiGroup: rbac.authorization.k8s.io
