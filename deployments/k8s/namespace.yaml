apiVersion: v1
kind: Namespace
metadata:
  name: chainops
  labels:
    name: chainops
    app.kubernetes.io/name: chainops
    app.kubernetes.io/version: "0.1.0"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: chainops-config
  namespace: chainops
  labels:
    app.kubernetes.io/name: chainops
    app.kubernetes.io/component: config
data:
  config.yaml: |
    server:
      host: "0.0.0.0"
      port: 8080
      read_timeout: 30s
      write_timeout: 30s
      idle_timeout: 120s
    
    database:
      host: postgres-service
      port: 5432
      user: chainops
      password: chainops_password
      name: chainops
      ssl_mode: disable
      max_open_conns: 25
      max_idle_conns: 5
      conn_max_lifetime: 300s
    
    cache:
      type: redis
      host: redis-service
      port: 6379
      password: ""
      db: 0
      max_retries: 3
      pool_size: 10
    
    message_queue:
      type: nats
      url: nats://nats-service:4222
      max_reconnects: 10
      reconnect_wait: 2s
    
    object_storage:
      type: minio
      endpoint: minio-service:9000
      access_key_id: chainops
      secret_access_key: chainops_minio_password
      bucket: chainops-artifacts
      region: us-east-1
      use_ssl: false
    
    runner:
      type: kubernetes
      namespace: chainops-runners
      image_pull_policy: IfNotPresent
      resource_limits:
        cpu: "1"
        memory: "1Gi"
      resource_requests:
        cpu: "100m"
        memory: "128Mi"
    
    security:
      jwt:
        secret: "your-jwt-secret-key-change-in-production"
        expiration: 24h
      encryption:
        key: "your-encryption-key-32-chars-long"
    
    monitoring:
      metrics:
        enabled: true
        endpoint: ":8081"
        namespace: chainops
      logging:
        level: info
        format: json
    
    features:
      approval_workflows: true
      matrix_builds: true
      deployment_strategies: true
      security_scanning: true
      analytics: true
