apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: chainops
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
type: Opaque
data:
  # chainops_password (base64 encoded)
  postgres-password: Y2hhaW5vcHNfcGFzc3dvcmQ=
  # chainops (base64 encoded)
  postgres-user: Y2hhaW5vcHM=
  # chainops (base64 encoded)
  postgres-db: Y2hhaW5vcHM=
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: chainops
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: chainops
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
    app.kubernetes.io/version: "15"
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres
      app.kubernetes.io/component: database
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgres
        app.kubernetes.io/component: database
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-db
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: postgres-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: init-scripts
          mountPath: /docker-entrypoint-initdb.d
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - chainops
            - -d
            - chainops
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - chainops
            - -d
            - chainops
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: init-scripts
        configMap:
          name: postgres-init-scripts
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: chainops
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-scripts
  namespace: chainops
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
data:
  init-db.sql: |
    -- ChainOps Database Initialization Script
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    
    -- Create schemas
    CREATE SCHEMA IF NOT EXISTS chainops;
    CREATE SCHEMA IF NOT EXISTS audit;
    
    -- Grant permissions
    GRANT USAGE ON SCHEMA chainops TO chainops;
    GRANT USAGE ON SCHEMA audit TO chainops;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA chainops TO chainops;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA audit TO chainops;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA chainops TO chainops;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA audit TO chainops;
    
    -- Set default privileges
    ALTER DEFAULT PRIVILEGES IN SCHEMA chainops GRANT ALL ON TABLES TO chainops;
    ALTER DEFAULT PRIVILEGES IN SCHEMA audit GRANT ALL ON TABLES TO chainops;
    ALTER DEFAULT PRIVILEGES IN SCHEMA chainops GRANT ALL ON SEQUENCES TO chainops;
    ALTER DEFAULT PRIVILEGES IN SCHEMA audit GRANT ALL ON SEQUENCES TO chainops;
