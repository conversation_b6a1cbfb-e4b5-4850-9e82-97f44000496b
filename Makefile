# ChainOps Development Makefile

# Variables
GO_VERSION := 1.21
DOCKER_COMPOSE_FILE := docker-compose.dev.yml
BINARY_NAME := chainops
MAIN_PATH := ./cmd/server

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

.PHONY: help install-deps dev-up dev-down build test clean migrate-up migrate-down

# Default target
help: ## Show this help message
	@echo "ChainOps Development Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-20s$(NC) %s\n", $$1, $$2}'

# Development Environment Setup
install-deps: ## Install all development dependencies
	@echo "$(YELLOW)Installing Go dependencies...$(NC)"
	go mod download
	go mod tidy
	@echo "$(YELLOW)Installing development tools...$(NC)"
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/golang-migrate/migrate/v4/cmd/migrate@latest
	go install github.com/air-verse/air@latest
	@echo "$(GREEN)Dependencies installed successfully!$(NC)"

# Infrastructure Management
dev-up: ## Start development infrastructure (PostgreSQL, Redis, NATS, MinIO)
	@echo "$(YELLOW)Starting development infrastructure...$(NC)"
	docker-compose -f $(DOCKER_COMPOSE_FILE) up -d
	@echo "$(GREEN)Development infrastructure started!$(NC)"
	@echo "$(BLUE)Services available at:$(NC)"
	@echo "  PostgreSQL: localhost:5432"
	@echo "  Redis: localhost:6379"
	@echo "  NATS: localhost:4222"
	@echo "  MinIO: localhost:9000 (admin: minioadmin/minioadmin)"

dev-down: ## Stop development infrastructure
	@echo "$(YELLOW)Stopping development infrastructure...$(NC)"
	docker-compose -f $(DOCKER_COMPOSE_FILE) down
	@echo "$(GREEN)Development infrastructure stopped!$(NC)"

dev-clean: ## Clean development infrastructure (removes volumes)
	@echo "$(YELLOW)Cleaning development infrastructure...$(NC)"
	docker-compose -f $(DOCKER_COMPOSE_FILE) down -v
	@echo "$(GREEN)Development infrastructure cleaned!$(NC)"

# Database Management
migrate-up: ## Run database migrations
	@echo "$(YELLOW)Running database migrations...$(NC)"
	migrate -path ./migrations -database "postgres://chainops:password@localhost:5432/chainops?sslmode=disable" up
	@echo "$(GREEN)Database migrations completed!$(NC)"

migrate-down: ## Rollback database migrations
	@echo "$(YELLOW)Rolling back database migrations...$(NC)"
	migrate -path ./migrations -database "postgres://chainops:password@localhost:5432/chainops?sslmode=disable" down
	@echo "$(GREEN)Database migrations rolled back!$(NC)"

migrate-create: ## Create a new migration (usage: make migrate-create NAME=migration_name)
	@if [ -z "$(NAME)" ]; then \
		echo "$(RED)Error: NAME is required. Usage: make migrate-create NAME=migration_name$(NC)"; \
		exit 1; \
	fi
	migrate create -ext sql -dir ./migrations $(NAME)
	@echo "$(GREEN)Migration $(NAME) created!$(NC)"

# Development Servers
dev-server: ## Start the backend development server with hot reload
	@echo "$(YELLOW)Starting backend development server...$(NC)"
	air -c .air.toml

# Building
build: ## Build the application
	@echo "$(YELLOW)Building backend...$(NC)"
	go build -o bin/$(BINARY_NAME) $(MAIN_PATH)
	@echo "$(GREEN)Build completed!$(NC)"

build-docker: ## Build Docker image
	@echo "$(YELLOW)Building Docker image...$(NC)"
	docker build -t chainops:latest .
	@echo "$(GREEN)Docker image built successfully!$(NC)"

# Testing
test: ## Run all tests
	@echo "$(YELLOW)Running Go tests...$(NC)"
	go test -v ./...
	@echo "$(GREEN)All tests completed!$(NC)"

test-go: ## Run only Go tests
	@echo "$(YELLOW)Running Go tests...$(NC)"
	go test -v ./...

test-coverage: ## Run tests with coverage
	@echo "$(YELLOW)Running tests with coverage...$(NC)"
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)Coverage report generated: coverage.html$(NC)"

test-integration: ## Run integration tests
	@echo "$(YELLOW)Running integration tests...$(NC)"
	go test -v -tags=integration ./tests/integration/...

# Code Quality
lint: ## Run linters
	@echo "$(YELLOW)Running Go linter...$(NC)"
	golangci-lint run
	@echo "$(GREEN)Linting completed!$(NC)"

fmt: ## Format code
	@echo "$(YELLOW)Formatting Go code...$(NC)"
	go fmt ./...
	@echo "$(GREEN)Code formatting completed!$(NC)"

# Security
security-scan: ## Run security scans
	@echo "$(YELLOW)Running security scans...$(NC)"
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	gosec ./...
	@echo "$(GREEN)Security scan completed!$(NC)"

# Cleanup
clean: ## Clean build artifacts
	@echo "$(YELLOW)Cleaning build artifacts...$(NC)"
	rm -rf bin/
	rm -rf coverage.out coverage.html
	@echo "$(GREEN)Cleanup completed!$(NC)"

# Documentation
docs: ## Generate documentation
	@echo "$(YELLOW)Generating documentation...$(NC)"
	go install golang.org/x/tools/cmd/godoc@latest
	@echo "$(GREEN)Documentation server available at: http://localhost:6060$(NC)"
	godoc -http=:6060

# Release
release: ## Build release version
	@echo "$(YELLOW)Building release version...$(NC)"
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/$(BINARY_NAME)-linux $(MAIN_PATH)
	CGO_ENABLED=0 GOOS=darwin go build -a -installsuffix cgo -o bin/$(BINARY_NAME)-darwin $(MAIN_PATH)
	CGO_ENABLED=0 GOOS=windows go build -a -installsuffix cgo -o bin/$(BINARY_NAME)-windows.exe $(MAIN_PATH)
	@echo "$(GREEN)Release build completed!$(NC)"

# Kubernetes
k8s-deploy: ## Deploy to local Kubernetes
	@echo "$(YELLOW)Deploying to Kubernetes...$(NC)"
	kubectl apply -f deployments/k8s/
	@echo "$(GREEN)Deployed to Kubernetes!$(NC)"

k8s-delete: ## Delete from Kubernetes
	@echo "$(YELLOW)Deleting from Kubernetes...$(NC)"
	kubectl delete -f deployments/k8s/
	@echo "$(GREEN)Deleted from Kubernetes!$(NC)"

# Helm
helm-install: ## Install with Helm
	@echo "$(YELLOW)Installing with Helm...$(NC)"
	helm install chainops deployments/helm/chainops
	@echo "$(GREEN)Installed with Helm!$(NC)"

helm-upgrade: ## Upgrade with Helm
	@echo "$(YELLOW)Upgrading with Helm...$(NC)"
	helm upgrade chainops deployments/helm/chainops
	@echo "$(GREEN)Upgraded with Helm!$(NC)"

helm-uninstall: ## Uninstall with Helm
	@echo "$(YELLOW)Uninstalling with Helm...$(NC)"
	helm uninstall chainops
	@echo "$(GREEN)Uninstalled with Helm!$(NC)"
